package com.ruoyi.expense.service.impl;

import java.util.List;
import java.util.Date;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.expense.mapper.ExpenseAllocationTableMapper;
import com.ruoyi.expense.mapper.ExpenseBillMapper;
import com.ruoyi.expense.domain.ExpenseAllocationTable;
import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.service.IExpenseAllocationTableService;
import com.ruoyi.expense.service.IExpenseBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.servlet.http.HttpServletResponse;

/**
 * 分摊表管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ExpenseAllocationTableServiceImpl implements IExpenseAllocationTableService 
{
    private static final Logger log = LoggerFactory.getLogger(ExpenseAllocationTableServiceImpl.class);

    @Autowired
    private ExpenseAllocationTableMapper expenseAllocationTableMapper;

    @Autowired
    private ExpenseBillMapper expenseBillMapper;

    @Autowired
    private IExpenseBillService expenseBillService;

    /**
     * 查询分摊表管理
     * 
     * @param id 分摊表管理主键
     * @return 分摊表管理
     */
    @Override
    public ExpenseAllocationTable selectExpenseAllocationTableById(Long id)
    {
        return expenseAllocationTableMapper.selectExpenseAllocationTableById(id);
    }

    /**
     * 查询分摊表管理列表
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 分摊表管理
     */
    @Override
    public List<ExpenseAllocationTable> selectExpenseAllocationTableList(ExpenseAllocationTable expenseAllocationTable)
    {
        return expenseAllocationTableMapper.selectExpenseAllocationTableList(expenseAllocationTable);
    }

    /**
     * 新增分摊表管理
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 结果
     */
    @Override
    public int insertExpenseAllocationTable(ExpenseAllocationTable expenseAllocationTable)
    {
        return expenseAllocationTableMapper.insertExpenseAllocationTable(expenseAllocationTable);
    }

    /**
     * 修改分摊表管理
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 结果
     */
    @Override
    public int updateExpenseAllocationTable(ExpenseAllocationTable expenseAllocationTable)
    {
        return expenseAllocationTableMapper.updateExpenseAllocationTable(expenseAllocationTable);
    }

    /**
     * 批量删除分摊表管理
     * 
     * @param ids 需要删除的分摊表管理主键
     * @return 结果
     */
    @Override
    public int deleteExpenseAllocationTableByIds(Long[] ids)
    {
        return expenseAllocationTableMapper.deleteExpenseAllocationTableByIds(ids);
    }

    /**
     * 删除分摊表管理信息
     * 
     * @param id 分摊表管理主键
     * @return 结果
     */
    @Override
    public int deleteExpenseAllocationTableById(Long id)
    {
        return expenseAllocationTableMapper.deleteExpenseAllocationTableById(id);
    }

    /**
     * 根据费用类型和计费周期生成分摊表
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param reviewer 复核人
     * @return 结果
     */
    @Override
    @Transactional
    public int generateAllocationTable(String expenseType, String billingCycle, String reviewer)
    {
        try {
            // 查询同一费用类型和计费周期的所有账单，获取制表人信息
            ExpenseBill queryBill = new ExpenseBill();
            queryBill.setExpense_type(expenseType);
            queryBill.setBilling_cycle(billingCycle);
            List<ExpenseBill> bills = expenseBillMapper.selectExpenseBillList(queryBill);
            
            if (bills == null || bills.isEmpty()) {
                log.warn("未找到相关账单，费用类型：{}，计费周期：{}", expenseType, billingCycle);
                return 0;
            }

            // 获取第一个账单的发布人作为制表人（假设同一费用类型和计费周期的账单制表人相同）
            String preparer = bills.get(0).getBill_publisher();
            if (StringUtils.isEmpty(preparer)) {
                preparer = "未知";
            }

            // 检查是否已存在相同的分摊表（不包括已拒绝状态）
            if (checkExistByExpenseTypeAndBillingCycle(expenseType, billingCycle)) {
                log.warn("分摊表已存在且非拒绝状态，费用类型：{}，计费周期：{}", expenseType, billingCycle);
                return 0;
            }

            // 检查是否存在已拒绝状态的分摊表，如果存在则更新它
            ExpenseAllocationTable existingTable = expenseAllocationTableMapper.selectByExpenseTypeAndBillingCycle(expenseType, billingCycle);
            ExpenseAllocationTable allocationTable = null;
            
            if (existingTable != null && "已拒绝".equals(existingTable.getStatus())) {
                allocationTable = existingTable;
            }

            if (allocationTable != null) {
                // 更新已拒绝的分摊表
                allocationTable.setPreparer(preparer);
                allocationTable.setReviewer(reviewer);
                allocationTable.setResponsiblePerson(""); // 负责人重置为空
                allocationTable.setComments(""); // 清空拒绝原因
                allocationTable.setStatus("审核中");
                allocationTable.setCreateTime(new Date()); // 重新设置创建时间
                allocationTable.setConfirmTime(null); // 清空确认时间

                int result = expenseAllocationTableMapper.updateExpenseAllocationTable(allocationTable);
                
                if (result > 0) {
                    log.info("成功重新生成已拒绝的分摊表，ID：{}，费用类型：{}，计费周期：{}，制表人：{}，复核人：{}", 
                            allocationTable.getId(), expenseType, billingCycle, preparer, reviewer);
                }
                
                return result;
            } else {
                // 创建新的分摊表记录
                allocationTable = new ExpenseAllocationTable();
                allocationTable.setTableName(billingCycle + "-" + expenseType + "-分摊表");
                allocationTable.setBillingCycle(billingCycle);
                allocationTable.setExpenseType(expenseType);
                allocationTable.setPreparer(preparer);
                allocationTable.setReviewer(reviewer);
                allocationTable.setResponsiblePerson(""); // 负责人初始为空
                allocationTable.setComments("");
                allocationTable.setStatus("审核中");
                allocationTable.setCreateTime(new Date()); // 设置创建时间

                int result = expenseAllocationTableMapper.insertExpenseAllocationTable(allocationTable);
                
                if (result > 0) {
                    log.info("成功生成新分摊表，费用类型：{}，计费周期：{}，制表人：{}，复核人：{}", 
                            expenseType, billingCycle, preparer, reviewer);
                }
                
                return result;
            }
        } catch (Exception e) {
            log.error("生成分摊表时发生异常", e);
            throw new RuntimeException("生成分摊表失败：" + e.getMessage());
        }
    }

    /**
     * 确认分摊表（分摊表核对人操作）
     * 
     * @param id 分摊表ID
     * @param responsiblePerson 负责人
     * @return 结果
     */
    @Override
    @Transactional
    public int confirmAllocationTable(Long id, String responsiblePerson)
    {
        try {
            ExpenseAllocationTable allocationTable = expenseAllocationTableMapper.selectExpenseAllocationTableById(id);
            if (allocationTable == null) {
                log.warn("分摊表不存在，ID：{}", id);
                return 0;
            }

            if (!"审核中".equals(allocationTable.getStatus())) {
                log.warn("分摊表状态不是审核中，无法确认，ID：{}，状态：{}", id, allocationTable.getStatus());
                return 0;
            }

            allocationTable.setStatus("已审核");
            allocationTable.setResponsiblePerson(responsiblePerson);
            allocationTable.setComments(""); // 清空之前的意见
            allocationTable.setConfirmTime(new Date()); // 设置确认时间

            int result = expenseAllocationTableMapper.updateExpenseAllocationTable(allocationTable);
            
            if (result > 0) {
                log.info("成功确认分摊表，ID：{}，负责人：{}", id, responsiblePerson);
            }
            
            return result;
        } catch (Exception e) {
            log.error("确认分摊表时发生异常", e);
            throw new RuntimeException("确认分摊表失败：" + e.getMessage());
        }
    }

    /**
     * 退回分摊表（分摊表核对人操作）
     * 
     * @param id 分摊表ID
     * @param comments 退回原因
     * @return 结果
     */
    @Override
    @Transactional
    public int rejectAllocationTable(Long id, String comments)
    {
        try {
            ExpenseAllocationTable allocationTable = expenseAllocationTableMapper.selectExpenseAllocationTableById(id);
            if (allocationTable == null) {
                log.warn("分摊表不存在，ID：{}", id);
                return 0;
            }

            if (!"审核中".equals(allocationTable.getStatus())) {
                log.warn("分摊表状态不是审核中，无法退回，ID：{}，状态：{}", id, allocationTable.getStatus());
                return 0;
            }

            allocationTable.setStatus("已拒绝");
            allocationTable.setComments(comments);

            int result = expenseAllocationTableMapper.updateExpenseAllocationTable(allocationTable);
            
            if (result > 0) {
                log.info("成功退回分摊表，ID：{}，退回原因：{}", id, comments);
            }
            
            return result;
        } catch (Exception e) {
            log.error("退回分摊表时发生异常", e);
            throw new RuntimeException("退回分摊表失败：" + e.getMessage());
        }
    }

    /**
     * 导出分摊表数据
     *
     * @param id 分摊表ID
     * @param response HTTP响应
     * @throws Exception 异常
     */
    @Override
    public void exportAllocationTable(Long id, HttpServletResponse response) throws Exception
    {
        try {
            ExpenseAllocationTable allocationTable = expenseAllocationTableMapper.selectExpenseAllocationTableById(id);
            if (allocationTable == null) {
                throw new RuntimeException("分摊表不存在");
            }

            // 调用账单服务的导出功能，传入负责人信息、时间信息和分摊表名称
            expenseBillService.exportShareBillByParamsWithResponsiblePersonAndTimeAndTableName(
                allocationTable.getExpenseType(),
                allocationTable.getBillingCycle(),
                allocationTable.getResponsiblePerson(),
                allocationTable.getCreateTime(),
                allocationTable.getConfirmTime(),
                allocationTable.getTableName(),
                response
            );
            
            log.info("成功导出分摊表，ID：{}，费用类型：{}，计费周期：{}，负责人：{}", 
                    id, allocationTable.getExpenseType(), allocationTable.getBillingCycle(), allocationTable.getResponsiblePerson());
                    
        } catch (Exception e) {
            log.error("导出分摊表时发生异常", e);
            throw e;
        }
    }

    /**
     * 检查是否已存在相同的分摊表
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @return 是否存在
     */
    @Override
    public boolean checkExistByExpenseTypeAndBillingCycle(String expenseType, String billingCycle)
    {
        int count = expenseAllocationTableMapper.checkExistByExpenseTypeAndBillingCycle(expenseType, billingCycle);
        return count > 0;
    }

    /**
     * 一键确认所有处于审核中状态的分摊表
     * 
     * @param responsiblePerson 负责人
     * @return 成功确认的数量
     */
    @Override
    @Transactional
    public int confirmAllPendingAllocationTables(String responsiblePerson)
    {
        try {
            log.info("开始一键确认所有审核中的分摊表，负责人：{}", responsiblePerson);
            
            // 查询所有处于审核中状态的分摊表
            ExpenseAllocationTable queryTable = new ExpenseAllocationTable();
            queryTable.setStatus("审核中");
            List<ExpenseAllocationTable> pendingTables = expenseAllocationTableMapper.selectExpenseAllocationTableList(queryTable);
            
            if (pendingTables == null || pendingTables.isEmpty()) {
                log.info("没有找到处于审核中状态的分摊表");
                return 0;
            }
            
            log.info("找到 {} 个处于审核中状态的分摊表", pendingTables.size());
            
            int successCount = 0;
            for (ExpenseAllocationTable table : pendingTables) {
                try {
                    // 更新分摊表状态为已审核
                    table.setStatus("已审核");
                    table.setResponsiblePerson(responsiblePerson);
                    table.setComments(""); // 清空之前的意见
                    table.setConfirmTime(new Date()); // 设置确认时间
                    
                    int result = expenseAllocationTableMapper.updateExpenseAllocationTable(table);
                    if (result > 0) {
                        successCount++;
                        log.info("成功确认分摊表，ID：{}，名称：{}", table.getId(), table.getTableName());
                    } else {
                        log.warn("确认分摊表失败，ID：{}，名称：{}", table.getId(), table.getTableName());
                    }
                } catch (Exception e) {
                    log.error("确认分摊表时发生异常，ID：{}，名称：{}，错误：{}", table.getId(), table.getTableName(), e.getMessage());
                }
            }
            
            log.info("一键确认完成，成功确认 {} 个分摊表", successCount);
            return successCount;
            
        } catch (Exception e) {
            log.error("一键确认分摊表时发生异常", e);
            throw new RuntimeException("一键确认分摊表失败：" + e.getMessage());
        }
    }

    @Override
    public List<Long> selectPendingAllocationTableIds()
    {
        try {
            log.info("开始查询所有审核中状态的分摊表ID");
            
            // 查询所有处于审核中状态的分摊表
            ExpenseAllocationTable queryTable = new ExpenseAllocationTable();
            queryTable.setStatus("审核中");
            List<ExpenseAllocationTable> pendingTables = expenseAllocationTableMapper.selectExpenseAllocationTableList(queryTable);
            
            List<Long> pendingIds = new ArrayList<>();
            if (pendingTables != null && !pendingTables.isEmpty()) {
                for (ExpenseAllocationTable table : pendingTables) {
                    pendingIds.add(table.getId());
                }
                log.info("找到 {} 个审核中状态的分摊表", pendingIds.size());
            } else {
                log.info("没有找到处于审核中状态的分摊表");
            }
            
            return pendingIds;
            
        } catch (Exception e) {
            log.error("查询审核中分摊表ID时发生异常", e);
            throw e;
        }
    }

} 