package com.ruoyi.line.mapper;

import java.util.List;
import com.ruoyi.line.domain.LineAlldata;

/**
 * 通讯线路管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface LineAlldataMapper 
{
    /**
     * 查询通讯线路管理
     * 
     * @param lineCode 通讯线路管理ID
     * @return 通讯线路管理
     */
    public LineAlldata selectLineAlldataById(Long lineCode);

    /**
     * 新增通讯线路管理
     *
     * @param lineAlldata 通讯线路管理
     * @return 结果
     */
    public int insertLineAlldata(LineAlldata lineAlldata);

    /**
     * 查询通讯线路管理列表
     *
     * @param lineAlldata 通讯线路管理
     * @return 通讯线路管理集合
     */
    public List<LineAlldata> selectLineAlldataList(LineAlldata lineAlldata);

    /**
     * 修改通讯线路管理
     * 
     * @param lineAlldata 通讯线路管理
     * @return 结果
     */
    public int updateLineAlldata(LineAlldata lineAlldata);

    /**
     * 删除通讯线路管理
     * 
     * @param lineCode 通讯线路管理ID
     * @return 结果
     */
    public int deleteLineAlldataById(Long lineCode);

    /**
     * 批量删除通讯线路管理
     * 
     * @param lineCodes 需要删除的数据ID
     * @return 结果
     */
    public int deleteLineAlldataByIds(String[] lineCodes);
}
