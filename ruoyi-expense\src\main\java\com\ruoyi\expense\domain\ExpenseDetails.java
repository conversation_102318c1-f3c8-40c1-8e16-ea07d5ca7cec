package com.ruoyi.expense.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 费用明细对象 expense_details
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class ExpenseDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 编号 */
    @Excel(name = "编号")
    private String number;

    /** 品牌 */
    @Excel(name = "品牌")
    private String brand;

    /** 具体规格 */
    @Excel(name = "具体规格")
    private String specificSpecification;

    /** 费用类型 */
    @Excel(name = "费用类型")
    private String expenseType;

    /** 计费周期 */
    @Excel(name = "计费周期")
    private String billingCycle;

    /** 划账部门或支行 */
    @Excel(name = "划账部门或支行")
    private String transferDepartment;

    /** 数量 */
    @Excel(name = "数量")
    private Long quantity;

    /** 费用变动情况 */
    @Excel(name = "费用变动情况")
    private String expenseChangeStatus;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 含税单价 */
    @Excel(name = "含税单价")
    private BigDecimal unitPriceIncludingTax;

    /** 不含税单价 */
    @Excel(name = "不含税单价")
    private BigDecimal unitPriceExcludingTax;

    /** 含税单行总价 */
    @Excel(name = "含税单行总价")
    private BigDecimal totalLinePriceIncludingTax;

    /** 不含税单行总价 */
    @Excel(name = "不含税单行总价")
    private BigDecimal totalLinePriceExcludingTax;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setNumber(String number) 
    {
        this.number = number;
    }

    public String getNumber() 
    {
        return number;
    }
    public void setBrand(String brand) 
    {
        this.brand = brand;
    }

    public String getBrand() 
    {
        return brand;
    }
    public void setSpecificSpecification(String specificSpecification) 
    {
        this.specificSpecification = specificSpecification;
    }

    public String getSpecificSpecification() 
    {
        return specificSpecification;
    }
    public void setExpenseType(String expenseType) 
    {
        this.expenseType = expenseType;
    }

    public String getExpenseType() 
    {
        return expenseType;
    }
    public void setBillingCycle(String billingCycle) 
    {
        this.billingCycle = billingCycle;
    }

    public String getBillingCycle() 
    {
        return billingCycle;
    }
    public void setTransferDepartment(String transferDepartment) 
    {
        this.transferDepartment = transferDepartment;
    }

    public String getTransferDepartment() 
    {
        return transferDepartment;
    }
    public void setQuantity(Long quantity) 
    {
        this.quantity = quantity;
    }

    public Long getQuantity() 
    {
        return quantity;
    }
    public void setExpenseChangeStatus(String expenseChangeStatus) 
    {
        this.expenseChangeStatus = expenseChangeStatus;
    }

    public String getExpenseChangeStatus() 
    {
        return expenseChangeStatus;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setUnitPriceIncludingTax(BigDecimal unitPriceIncludingTax) 
    {
        this.unitPriceIncludingTax = unitPriceIncludingTax;
    }

    public BigDecimal getUnitPriceIncludingTax() 
    {
        return unitPriceIncludingTax;
    }
    public void setUnitPriceExcludingTax(BigDecimal unitPriceExcludingTax) 
    {
        this.unitPriceExcludingTax = unitPriceExcludingTax;
    }

    public BigDecimal getUnitPriceExcludingTax() 
    {
        return unitPriceExcludingTax;
    }
    public void setTotalLinePriceIncludingTax(BigDecimal totalLinePriceIncludingTax) 
    {
        this.totalLinePriceIncludingTax = totalLinePriceIncludingTax;
    }

    public BigDecimal getTotalLinePriceIncludingTax() 
    {
        return totalLinePriceIncludingTax;
    }
    public void setTotalLinePriceExcludingTax(BigDecimal totalLinePriceExcludingTax) 
    {
        this.totalLinePriceExcludingTax = totalLinePriceExcludingTax;
    }

    public BigDecimal getTotalLinePriceExcludingTax() 
    {
        return totalLinePriceExcludingTax;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("number", getNumber())
            .append("brand", getBrand())
            .append("specificSpecification", getSpecificSpecification())
            .append("expenseType", getExpenseType())
            .append("billingCycle", getBillingCycle())
            .append("transferDepartment", getTransferDepartment())
            .append("quantity", getQuantity())
            .append("expenseChangeStatus", getExpenseChangeStatus())
            .append("remarks", getRemarks())
            .append("unitPriceIncludingTax", getUnitPriceIncludingTax())
            .append("unitPriceExcludingTax", getUnitPriceExcludingTax())
            .append("totalLinePriceIncludingTax", getTotalLinePriceIncludingTax())
            .append("totalLinePriceExcludingTax", getTotalLinePriceExcludingTax())
            .toString();
    }
}
