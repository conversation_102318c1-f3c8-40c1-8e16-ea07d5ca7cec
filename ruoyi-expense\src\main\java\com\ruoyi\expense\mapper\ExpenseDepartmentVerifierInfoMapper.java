package com.ruoyi.expense.mapper;

import com.ruoyi.expense.domain.ExpenseDepartmentVerifierInfo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 部门账单核对人数据接口
 */
public interface ExpenseDepartmentVerifierInfoMapper {
    
    /**
     * 根据条件查询核对人列表
     */
    List<ExpenseDepartmentVerifierInfo> selectVerifierInfoList(ExpenseDepartmentVerifierInfo verifierInfo);
    
    /**
     * 通过ID查询单条数据
     */
    ExpenseDepartmentVerifierInfo selectVerifierInfoById(Integer id);
    
    /**
     * 根据核对人姓名查询信息
     */
    ExpenseDepartmentVerifierInfo selectVerifierInfoByName(@Param("name") String name);
    
    /**
     * 根据部门名称查询信息
     */
    List<ExpenseDepartmentVerifierInfo> selectVerifierInfoByDepartmentName(@Param("departmentName") String departmentName);
    
    /**
     * 根据EHR编号查询信息
     */
    ExpenseDepartmentVerifierInfo selectVerifierInfoByEhrNumber(@Param("ehrNumber") String ehrNumber);
} 