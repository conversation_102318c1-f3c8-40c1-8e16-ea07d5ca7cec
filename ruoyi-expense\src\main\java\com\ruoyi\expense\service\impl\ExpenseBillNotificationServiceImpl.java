package com.ruoyi.expense.service.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.domain.ExpenseCheckerManage;
import com.ruoyi.expense.domain.Expense_wechat;
import com.ruoyi.expense.mapper.ExpenseCheckerManageMapper;
import com.ruoyi.expense.service.IExpenseBillNotificationService;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 账单通知服务实现类
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Service
public class ExpenseBillNotificationServiceImpl implements IExpenseBillNotificationService {
    
    private static final Logger log = LoggerFactory.getLogger(ExpenseBillNotificationServiceImpl.class);

    @Autowired
    private ISysConfigService configService;
    
    @Autowired
    private ExpenseCheckerManageMapper checkerManageMapper;
    
    @Autowired
    private DictService dictService;

    @Autowired
    private ISysUserService sysUserService;

    @Override
    public String sendBillNotifications(List<ExpenseBill> bills) {
        if (bills == null || bills.isEmpty()) {
            log.warn("账单列表为空，无需发送通知");
            return "账单列表为空，无需发送通知";
        }

        List<String> successResults = new ArrayList<>();
        List<String> failResults = new ArrayList<>();

        for (ExpenseBill bill : bills) {
            try {
                String result = sendSingleBillNotification(bill);
                if (result.contains("发送成功")) {
                    successResults.add(result);
                } else {
                    failResults.add(result);
                }
            } catch (Exception e) {
                String errorMsg = String.format("账单[%s]通知发送失败：%s", 
                    bill.getTransfer_department(), e.getMessage());
                failResults.add(errorMsg);
                log.error(errorMsg, e);
            }
        }

        // 构建结果消息
        StringBuilder resultBuilder = new StringBuilder();
        if (!successResults.isEmpty()) {
            resultBuilder.append("成功发送通知：").append(successResults.size()).append("条；");
        }
        if (!failResults.isEmpty()) {
            resultBuilder.append("发送失败：").append(failResults.size()).append("条；");
            resultBuilder.append("失败详情：").append(String.join(", ", failResults));
        }

        String finalResult = resultBuilder.toString();
        log.info("批量通知发送结果：{}", finalResult);
        return finalResult;
    }

    @Override
    public String sendSingleBillNotification(ExpenseBill bill) {
        if (bill == null) {
            return "账单信息为空，无法发送通知";
        }

        try {
            // 1. 获取配置信息
            String url = configService.selectConfigByKey("expense.sendCharSms.url");
            String sysCode = configService.selectConfigByKey("expense.sendCharSms.sysCode");
            String sysName = configService.selectConfigByKey("expense.sendCharSms.sysName");

            if (StringUtils.isEmpty(url) || StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(sysName)) {
                log.error("微信通知配置信息不完整：url={}, sysCode={}, sysName={}", url, sysCode, sysName);
                return "微信通知配置信息不完整，请联系系统管理员";
            }

            // 2. 获取部门所有核对人的EHR号
            List<String> verifierEhrs = getAllVerifierEhrsByDepartment(bill.getTransfer_department());
            if (verifierEhrs.isEmpty()) {
                String departmentLabel = dictService.getLabel("expense_institude", bill.getTransfer_department());
                return String.format("未找到部门[%s]的核对人信息，无法发送通知", 
                    departmentLabel != null ? departmentLabel : bill.getTransfer_department());
            }

            // 3. 构建消息内容
            String departmentLabel = dictService.getLabel("expense_institude", bill.getTransfer_department());
            String expenseTypeLabel = dictService.getLabel("expense_type", bill.getExpense_type());
            
            String msgContent = String.format(
                "您有一份账单需要核对，请在本月25日前（从智慧桌面访问信科小工具-电子划账）完成核对。该份账单的摘要信息如下，费用类型：%s，划账月份：%s，划账部门：%s", 
                expenseTypeLabel != null ? expenseTypeLabel : bill.getExpense_type(),
                bill.getBilling_cycle(),
                departmentLabel != null ? departmentLabel : bill.getTransfer_department()
            );

            // 4. 给每个核对人发送通知
            int successCount = 0;
            int failCount = 0;
            StringBuilder failDetails = new StringBuilder();

            for (String verifierEhr : verifierEhrs) {
                try {
                    // 构建微信通知对象
                    Expense_wechat weChat = new Expense_wechat();
                    weChat.setRecvEhr(verifierEhr);
                    weChat.setSysCode(sysCode);
                    weChat.setSysName(sysName);
                    weChat.setMsgTitle("[账单核对通知]");
                    weChat.setMsgContent(msgContent);

                    // 转换为JSON并发送
                    String jsonParam = JSON.toJSONString(weChat);
                    log.info("发送通知给EHR[{}]：{}", verifierEhr, jsonParam);

                    String response = HttpUtils.sendPost(url, jsonParam);
                    log.info("通知发送响应：{}", response);
                    
                    successCount++;
                    
                } catch (Exception e) {
                    failCount++;
                    String errorMsg = String.format("EHR[%s]通知发送失败：%s", verifierEhr, e.getMessage());
                    log.error(errorMsg, e);
                    failDetails.append(errorMsg).append("；");
                }
            }

            // 5. 构建结果消息
            String result;
            if (failCount == 0) {
                result = String.format("账单[%s]通知发送成功，共通知%d个核对人", 
                    departmentLabel != null ? departmentLabel : bill.getTransfer_department(),
                    successCount);
            } else {
                result = String.format("账单[%s]通知发送完成，成功%d个，失败%d个。失败详情：%s", 
                    departmentLabel != null ? departmentLabel : bill.getTransfer_department(),
                    successCount, failCount, failDetails.toString());
            }
            
            log.info("部门[{}]账单通知发送结果：{}", bill.getTransfer_department(), result);
            return result;

        } catch (Exception e) {
            String errorMsg = String.format("账单[%s]通知发送失败：%s", 
                bill.getTransfer_department(), e.getMessage());
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    @Override
    public String getVerifierEhrByDepartment(String departmentName) {
        if (StringUtils.isEmpty(departmentName)) {
            return null;
        }

        try {
            // 根据部门名称查询核对人信息列表
            List<ExpenseCheckerManage> checkers = checkerManageMapper.selectByDepartmentName(departmentName);
            if (checkers != null && !checkers.isEmpty()) {
                // 为了保持接口兼容性，返回第一个核对人的EHR号
                ExpenseCheckerManage firstChecker = checkers.get(0);
                if (checkers.size() > 1) {
                    log.info("部门[{}]有{}个核对人，返回第一个核对人[{}]的EHR号", 
                        departmentName, checkers.size(), firstChecker.getName());
                }
                return firstChecker.getEhrNumber();
            }
            
            // 如果没有找到，记录日志
            log.warn("未找到部门[{}]的核对人信息", departmentName);
            return null;
            
        } catch (Exception e) {
            log.error("查询部门[{}]核对人信息时发生异常", departmentName, e);
            return null;
        }
    }

    /**
     * 获取部门所有核对人的EHR号列表
     * @param departmentName 部门名称
     * @return 核对人EHR号列表
     */
    @Override
    public List<String> getAllVerifierEhrsByDepartment(String departmentName) {
        List<String> ehrNumbers = new ArrayList<>();
        if (StringUtils.isEmpty(departmentName)) {
            return ehrNumbers;
        }

        try {
            // 根据部门名称查询核对人信息列表
            List<ExpenseCheckerManage> checkers = checkerManageMapper.selectByDepartmentName(departmentName);
            if (checkers != null && !checkers.isEmpty()) {
                log.info("部门[{}]共有{}个核对人", departmentName, checkers.size());
                for (ExpenseCheckerManage checker : checkers) {
                    if (StringUtils.isNotEmpty(checker.getEhrNumber())) {
                        ehrNumbers.add(checker.getEhrNumber());
                        log.info("添加核对人[{}]的EHR号[{}]", checker.getName(), checker.getEhrNumber());
                    }
                }
            } else {
                log.warn("未找到部门[{}]的核对人信息", departmentName);
            }
            
        } catch (Exception e) {
            log.error("查询部门[{}]核对人信息时发生异常", departmentName, e);
        }
        
        return ehrNumbers;
    }

    @Override
    public String sendBillReturnNotification(ExpenseBill bill, String refuseComment) {
        if (bill == null) {
            return "账单信息为空，无法发送通知";
        }

        try {
            // 1. 获取配置信息
            String url = configService.selectConfigByKey("expense.sendCharSms.url");
            String sysCode = configService.selectConfigByKey("expense.sendCharSms.sysCode");
            String sysName = configService.selectConfigByKey("expense.sendCharSms.sysName");

            if (StringUtils.isEmpty(url) || StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(sysName)) {
                log.error("微信通知配置信息不完整：url={}, sysCode={}, sysName={}", url, sysCode, sysName);
                return "微信通知配置信息不完整，请联系系统管理员";
            }

            // 2. 获取账单上传人的EHR号
            String publisherEhr = getUserEhrByUserName(bill.getBill_publisher());
            if (StringUtils.isEmpty(publisherEhr)) {
                return String.format("未找到账单上传人[%s]的EHR号信息，无法发送通知", bill.getBill_publisher());
            }

            // 3. 构建微信通知对象
            Expense_wechat weChat = new Expense_wechat();
            weChat.setRecvEhr(publisherEhr);
            weChat.setSysCode(sysCode);
            weChat.setSysName(sysName);
            weChat.setMsgTitle("[账单退回通知]");

            // 4. 构建消息内容
            String departmentLabel = dictService.getLabel("expense_institude", bill.getTransfer_department());
            String expenseTypeLabel = dictService.getLabel("expense_type", bill.getExpense_type());
            
            String msgContent = String.format(
                "您上传的账单已被退回，请及时处理。账单信息：费用类型：%s，划账月份：%s，划账部门：%s。退回原因：%s", 
                expenseTypeLabel != null ? expenseTypeLabel : bill.getExpense_type(),
                bill.getBilling_cycle(),
                departmentLabel != null ? departmentLabel : bill.getTransfer_department(),
                StringUtils.isNotEmpty(refuseComment) ? refuseComment : "无"
            );
            weChat.setMsgContent(msgContent);

            // 5. 转换为JSON并发送
            String jsonParam = JSON.toJSONString(weChat);
            log.info("账单退回通知：{}", jsonParam);

            String response = HttpUtils.sendPost(url, jsonParam);
            log.info("退回通知发送响应：{}", response);

            return String.format("账单退回通知发送成功，已通知上传人[%s]", bill.getBill_publisher());

        } catch (Exception e) {
            String errorMsg = String.format("账单退回通知发送失败：%s", e.getMessage());
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    @Override
    public String sendBillRevokeNotification(ExpenseBill bill, String revokeComment) {
        if (bill == null) {
            return "账单信息为空，无法发送通知";
        }

        try {
            // 1. 获取配置信息
            String url = configService.selectConfigByKey("expense.sendCharSms.url");
            String sysCode = configService.selectConfigByKey("expense.sendCharSms.sysCode");
            String sysName = configService.selectConfigByKey("expense.sendCharSms.sysName");

            if (StringUtils.isEmpty(url) || StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(sysName)) {
                log.error("微信通知配置信息不完整：url={}, sysCode={}, sysName={}", url, sysCode, sysName);
                return "微信通知配置信息不完整，请联系系统管理员";
            }

            // 2. 获取账单上传人的EHR号
            String publisherEhr = getUserEhrByUserName(bill.getBill_publisher());
            if (StringUtils.isEmpty(publisherEhr)) {
                return String.format("未找到账单上传人[%s]的EHR号信息，无法发送通知", bill.getBill_publisher());
            }

            // 3. 构建微信通知对象
            Expense_wechat weChat = new Expense_wechat();
            weChat.setRecvEhr(publisherEhr);
            weChat.setSysCode(sysCode);
            weChat.setSysName(sysName);
            weChat.setMsgTitle("[账单撤回通知]");

            // 4. 构建消息内容
            String departmentLabel = dictService.getLabel("expense_institude", bill.getTransfer_department());
            String expenseTypeLabel = dictService.getLabel("expense_type", bill.getExpense_type());
            
            String msgContent = String.format(
                "您上传的账单已被管理员撤回，请知悉。账单信息：费用类型：%s，划账月份：%s，划账部门：%s。%s", 
                expenseTypeLabel != null ? expenseTypeLabel : bill.getExpense_type(),
                bill.getBilling_cycle(),
                departmentLabel != null ? departmentLabel : bill.getTransfer_department(),
                StringUtils.isNotEmpty(revokeComment) ? "撤回说明：" + revokeComment : "如有疑问请联系管理员。"
            );
            weChat.setMsgContent(msgContent);

            // 5. 转换为JSON并发送
            String jsonParam = JSON.toJSONString(weChat);
            log.info("账单撤回通知：{}", jsonParam);

            String response = HttpUtils.sendPost(url, jsonParam);
            log.info("撤回通知发送响应：{}", response);

            return String.format("账单撤回通知发送成功，已通知上传人[%s]", bill.getBill_publisher());

        } catch (Exception e) {
            String errorMsg = String.format("账单撤回通知发送失败：%s", e.getMessage());
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    @Override
    public String sendBillRevokeNotifications(List<ExpenseBill> bills, String revokeComment) {
        if (bills == null || bills.isEmpty()) {
            log.warn("账单列表为空，无需发送撤回通知");
            return "账单列表为空，无需发送撤回通知";
        }

        List<String> successResults = new ArrayList<>();
        List<String> failResults = new ArrayList<>();

        for (ExpenseBill bill : bills) {
            try {
                String result = sendBillRevokeNotification(bill, revokeComment);
                if (result.contains("发送成功")) {
                    successResults.add(result);
                } else {
                    failResults.add(result);
                }
            } catch (Exception e) {
                String errorMsg = String.format("账单[%s]撤回通知发送失败：%s", 
                    bill.getTransfer_department(), e.getMessage());
                failResults.add(errorMsg);
                log.error(errorMsg, e);
            }
        }

        // 构建结果消息
        StringBuilder resultBuilder = new StringBuilder();
        if (!successResults.isEmpty()) {
            resultBuilder.append("成功发送撤回通知：").append(successResults.size()).append("条；");
        }
        if (!failResults.isEmpty()) {
            resultBuilder.append("发送失败：").append(failResults.size()).append("条；");
            resultBuilder.append("失败详情：").append(String.join(", ", failResults));
        }

        String finalResult = resultBuilder.toString();
        log.info("批量撤回通知发送结果：{}", finalResult);
        return finalResult;
    }

    @Override
    public String sendBillReturnNotifications(List<ExpenseBill> bills, String refuseComment) {
        if (bills == null || bills.isEmpty()) {
            log.warn("账单列表为空，无需发送退回通知");
            return "账单列表为空，无需发送退回通知";
        }

        List<String> successResults = new ArrayList<>();
        List<String> failResults = new ArrayList<>();

        for (ExpenseBill bill : bills) {
            try {
                String result = sendBillReturnNotification(bill, refuseComment);
                if (result.contains("发送成功")) {
                    successResults.add(result);
                } else {
                    failResults.add(result);
                }
            } catch (Exception e) {
                String errorMsg = String.format("账单[%s]退回通知发送失败：%s", 
                    bill.getTransfer_department(), e.getMessage());
                failResults.add(errorMsg);
                log.error(errorMsg, e);
            }
        }

        // 构建结果消息
        StringBuilder resultBuilder = new StringBuilder();
        if (!successResults.isEmpty()) {
            resultBuilder.append("成功发送退回通知：").append(successResults.size()).append("条；");
        }
        if (!failResults.isEmpty()) {
            resultBuilder.append("发送失败：").append(failResults.size()).append("条；");
            resultBuilder.append("失败详情：").append(String.join(", ", failResults));
        }

        String finalResult = resultBuilder.toString();
        log.info("批量退回通知发送结果：{}", finalResult);
        return finalResult;
    }

    @Override
    public String getUserEhrByUserName(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return null;
        }

        try {
            // 查询sys_user表中user_name字段匹配的用户，取其login_name字段作为EHR号
            log.info("查询用户[{}]的EHR号", userName);
            
            SysUser queryUser = new SysUser();
            queryUser.setUserName(userName);
            List<SysUser> userList = sysUserService.selectUserList(queryUser);
            if (userList != null && !userList.isEmpty()) {
                // 手动在返回的用户列表中找到匹配userName的用户
                for (SysUser sysUser : userList) {
                    if (userName.equals(sysUser.getUserName())) {
                        log.info("找到用户[{}]，EHR号为[{}]", userName, sysUser.getLoginName());
                        return sysUser.getLoginName();
                    }
                }
                log.warn("在用户列表中未找到用户名为[{}]的用户", userName);
            } else {
                log.warn("用户列表为空，未找到任何用户信息");
            }
            
            log.warn("未找到用户名为[{}]的用户信息", userName);
            return null;
            
        } catch (Exception e) {
            log.error("查询用户[{}]EHR号时发生异常", userName, e);
            return null;
        }
    }
} 