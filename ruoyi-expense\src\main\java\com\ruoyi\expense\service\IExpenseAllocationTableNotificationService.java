package com.ruoyi.expense.service;

import com.ruoyi.expense.domain.ExpenseAllocationTable;
import java.util.List;

/**
 * 分摊表通知服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IExpenseAllocationTableNotificationService {
    
    /**
     * 发送分摊表核对通知给核对人员
     * 
     * @param allocationTables 分摊表列表
     * @return 通知发送结果
     */
    String sendAllocationTableNotifications(List<ExpenseAllocationTable> allocationTables);
    
    /**
     * 发送单个分摊表核对通知
     * 
     * @param allocationTable 分摊表信息
     * @return 通知发送结果
     */
    String sendSingleAllocationTableNotification(ExpenseAllocationTable allocationTable);
    
    /**
     * 根据角色获取分摊表核对人员的EHR号列表
     * 
     * @param roleKey 角色标识（如：expense_allocationTable_checker）
     * @return 核对人员EHR号列表
     */
    List<String> getAllCheckerEhrsByRole(String roleKey);
    
    /**
     * 根据用户名获取用户的EHR号
     * 
     * @param userName 用户名
     * @return 用户EHR号
     */
    String getUserEhrByUserName(String userName);
}
