package com.ruoyi.expense.service;

import java.util.List;
import com.ruoyi.expense.domain.ExpenseAllocationTable;
import javax.servlet.http.HttpServletResponse;

/**
 * 分摊表管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IExpenseAllocationTableService 
{
    /**
     * 查询分摊表管理
     * 
     * @param id 分摊表管理主键
     * @return 分摊表管理
     */
    public ExpenseAllocationTable selectExpenseAllocationTableById(Long id);

    /**
     * 查询分摊表管理列表
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 分摊表管理集合
     */
    public List<ExpenseAllocationTable> selectExpenseAllocationTableList(ExpenseAllocationTable expenseAllocationTable);

    /**
     * 新增分摊表管理
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 结果
     */
    public int insertExpenseAllocationTable(ExpenseAllocationTable expenseAllocationTable);

    /**
     * 修改分摊表管理
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 结果
     */
    public int updateExpenseAllocationTable(ExpenseAllocationTable expenseAllocationTable);

    /**
     * 批量删除分摊表管理
     * 
     * @param ids 需要删除的分摊表管理主键集合
     * @return 结果
     */
    public int deleteExpenseAllocationTableByIds(Long[] ids);

    /**
     * 删除分摊表管理信息
     * 
     * @param id 分摊表管理主键
     * @return 结果
     */
    public int deleteExpenseAllocationTableById(Long id);

    /**
     * 根据费用类型和计费周期生成分摊表
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param reviewer 复核人
     * @return 结果
     */
    public int generateAllocationTable(String expenseType, String billingCycle, String reviewer);

    /**
     * 确认分摊表（分摊表核对人操作）
     * 
     * @param id 分摊表ID
     * @param responsiblePerson 负责人
     * @return 结果
     */
    public int confirmAllocationTable(Long id, String responsiblePerson);

    /**
     * 退回分摊表（分摊表核对人操作）
     * 
     * @param id 分摊表ID
     * @param comments 退回原因
     * @return 结果
     */
    public int rejectAllocationTable(Long id, String comments);

    /**
     * 导出分摊表数据
     * 
     * @param id 分摊表ID
     * @param response HTTP响应
     * @throws Exception 异常
     */
    public void exportAllocationTable(Long id, HttpServletResponse response) throws Exception;

    /**
     * 检查是否已存在相同的分摊表
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @return 是否存在
     */
    public boolean checkExistByExpenseTypeAndBillingCycle(String expenseType, String billingCycle);

    /**
     * 一键确认所有处于审核中状态的分摊表
     * 
     * @param responsiblePerson 负责人
     * @return 成功确认的数量
     */
    public int confirmAllPendingAllocationTables(String responsiblePerson);

    /**
     * 获取所有处于审核中状态的分摊表ID列表
     * 
     * @return 分摊表ID列表
     */
    public List<Long> selectPendingAllocationTableIds();
} 