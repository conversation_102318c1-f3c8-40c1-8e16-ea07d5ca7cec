<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改费用明细')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-details-edit" th:object="${expenseDetails}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">名称：</label>
                <div class="col-sm-8">
                    <input name="name" th:field="*{name}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">编号：</label>
                <div class="col-sm-8">
                    <input name="number" th:field="*{number}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">品牌：</label>
                <div class="col-sm-8">
                    <input name="brand" th:field="*{brand}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">具体规格：</label>
                <div class="col-sm-8">
                    <input name="specificSpecification" th:field="*{specificSpecification}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">费用类型：</label>
                <div class="col-sm-8">
                    <select name="expenseType" class="form-control m-b">
                        <option value="">所有</option>
                    </select>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">计费周期：</label>
                <div class="col-sm-8">
                    <input name="billingCycle" th:field="*{billingCycle}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">划账部门或支行：</label>
                <div class="col-sm-8">
                    <input name="transferDepartment" th:field="*{transferDepartment}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">数量：</label>
                <div class="col-sm-8">
                    <input name="quantity" th:field="*{quantity}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">费用变动情况：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="expenseChangeStatus" value="">
                        <label th:for="expenseChangeStatus" th:text="未知"></label>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remarks" class="form-control">[[*{remarks}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">含税单价：</label>
                <div class="col-sm-8">
                    <input name="unitPriceIncludingTax" th:field="*{unitPriceIncludingTax}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">不含税单价：</label>
                <div class="col-sm-8">
                    <input name="unitPriceExcludingTax" th:field="*{unitPriceExcludingTax}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">含税单行总价：</label>
                <div class="col-sm-8">
                    <input name="totalLinePriceIncludingTax" th:field="*{totalLinePriceIncludingTax}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">不含税单行总价：</label>
                <div class="col-sm-8">
                    <input name="totalLinePriceExcludingTax" th:field="*{totalLinePriceExcludingTax}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "expense/details";
        $("#form-details-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-details-edit').serialize());
            }
        }
    </script>
</body>
</html>