package com.ruoyi.expense.service;

import java.util.List;
import java.util.Date;
import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.domain.BillReceiveDTO;
import javax.servlet.http.HttpServletResponse;

/**
 * 账单数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface IExpenseBillService 
{
    /**
     * 查询账单数据
     * 
     * @param id 账单数据主键
     * @return 账单数据
     */
    public ExpenseBill selectExpenseBillById(Long id);
    
    /**
     * 根据费用类型、计费周期和划账部门查询账单
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param transferDepartment 划账部门
     * @return 账单数据
     */
    public ExpenseBill selectExpenseBillByUniqueKey(String expenseType, String billingCycle, String transferDepartment);

    /**
     * 查询账单数据列表
     * 
     * @param expenseBill 账单数据
     * @return 账单数据集合
     */
    public List<ExpenseBill> selectExpenseBillList(ExpenseBill expenseBill);

    /**
     * 新增账单数据
     * 
     * @param expenseBill 账单数据
     * @return 结果
     */
    public int insertExpenseBill(ExpenseBill expenseBill);

    /**
     * 修改账单数据
     * 
     * @param expenseBill 账单数据
     * @return 结果
     */
    public int updateExpenseBill(ExpenseBill expenseBill);

    /**
     * 批量删除账单数据
     * 
     * @param ids 需要删除的账单数据主键集合
     * @return 结果
     */
    public int deleteExpenseBillByIds(String ids);

    /**
     * 删除账单数据信息
     * 
     * @param id 账单数据主键
     * @return 结果
     */
    public int deleteExpenseBillById(Long id);
    
    /**
     * 接收并处理账单数据
     * 
     * @param receiveDTO 接收的账单数据
     * @return 处理结果消息
     */
    public String receiveBillData(BillReceiveDTO receiveDTO);

    /**
     * 清空退回人和退回意见
     * @param billId 账单ID
     * @return 结果
     */
    int clearReturnInfo(Long billId);

    /**
     * 导出账单分摊表
     *
     * @param billIds 账单ID列表，多个ID用逗号分隔
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    public void exportShareBill(String billIds, HttpServletResponse response) throws Exception;

    /**
     * 根据参数导出账单分摊表
     *
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    public void exportShareBillByParams(String expenseType, String billingCycle, HttpServletResponse response) throws Exception;

    /**
     * 根据参数导出账单分摊表（带负责人信息）
     *
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param responsiblePerson 负责人
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    public void exportShareBillByParamsWithResponsiblePerson(String expenseType, String billingCycle, String responsiblePerson, HttpServletResponse response) throws Exception;

    /**
     * 根据参数导出账单分摊表（带负责人信息和时间信息）
     *
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param responsiblePerson 负责人
     * @param createTime 创建时间
     * @param confirmTime 确认时间
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    public void exportShareBillByParamsWithResponsiblePersonAndTime(String expenseType, String billingCycle, String responsiblePerson, Date createTime, Date confirmTime, HttpServletResponse response) throws Exception;

    /**
     * 根据参数导出账单分摊表（带负责人信息、时间信息和分摊表名称）
     *
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param responsiblePerson 负责人
     * @param createTime 创建时间
     * @param confirmTime 确认时间
     * @param tableName 分摊表名称
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    public void exportShareBillByParamsWithResponsiblePersonAndTimeAndTableName(String expenseType, String billingCycle, String responsiblePerson, Date createTime, Date confirmTime, String tableName, HttpServletResponse response) throws Exception;
} 