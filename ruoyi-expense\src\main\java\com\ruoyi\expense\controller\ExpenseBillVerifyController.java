package com.ruoyi.expense.controller;

import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.domain.ExpenseDetails;
import com.ruoyi.expense.domain.ExpenseDepartmentVerifierInfo;
import com.ruoyi.expense.service.IExpenseBillService;
import com.ruoyi.expense.service.IExpenseDetailsService;
import com.ruoyi.expense.service.IExpenseDepartmentVerifierInfoService;
import com.ruoyi.expense.service.IExpenseBillNotificationService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.system.service.ISysRoleService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
 * 账单核对Controller
 */
@Controller
@RequestMapping("/expense/bill_verify")
public class ExpenseBillVerifyController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(ExpenseBillVerifyController.class);

    private String prefix = "expense/bill_verify";

    @Autowired
    private IExpenseBillService expenseBillService;

    @Autowired
    private IExpenseDetailsService expenseDetailsService;

    @Autowired
    private IExpenseDepartmentVerifierInfoService verifierInfoService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private IExpenseBillNotificationService expenseBillNotificationService;

    // 简单的内存缓存，用于admin用户的账单列表
    private static volatile List<ExpenseBill> adminBillsCache = null;
    private static volatile long adminCacheTime = 0;
    private static final long CACHE_EXPIRE_TIME = 10000; // 10秒缓存过期时间
    
    // 请求频率限制
    private static volatile long lastRequestTime = 0;
    private static final long REQUEST_INTERVAL = 1000; // 1秒内不允许重复请求

    /**
     * 查看账单详情（弹窗）
     */
    @RequiresPermissions("expense:bill_verify:detail")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") Long id, ModelMap mmap) {
        ExpenseBill bill = expenseBillService.selectExpenseBillById(id);
        mmap.put("bill", bill);
        // 查询费用明细
        ExpenseDetails query = new ExpenseDetails();
        query.setExpenseType(bill.getExpense_type());
        query.setBillingCycle(bill.getBilling_cycle());
        query.setTransferDepartment(bill.getTransfer_department());
        List<ExpenseDetails> detailsList = expenseDetailsService.selectExpenseDetailsList(query);
        mmap.put("detailsList", detailsList);
        return prefix + "/billDetail";
    }

    /**
     * 账单核对主页面跳转
     */
    @RequiresPermissions("expense:bill_verify:view")
    @GetMapping()
    public String billVerify(ModelMap mmap) {
        Long userId = ShiroUtils.getSysUser().getUserId();
        Set<String> roles = sysRoleService.selectRoleKeys(userId);
        mmap.put("roles", roles);
        mmap.put("currentUser", ShiroUtils.getLoginName());
        return prefix + "/bill";
    }

    /**
     * 账单核对列表
     * 核对人只能查看自己部门的账单
     */
    @RequiresPermissions("expense:bill_verify:list")
    @PostMapping("/list")
    @ResponseBody
    public synchronized TableDataInfo list(ExpenseBill expenseBill) {
        String loginName = ShiroUtils.getLoginName();
        String userName = ShiroUtils.getSysUser().getUserName();
        Long userId = ShiroUtils.getSysUser().getUserId();
        
        // 为admin账号提供特殊处理，使用缓存避免重复查询
        if ("admin".equals(loginName) || userId.equals(1L)) {
            // 请求频率限制
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastRequestTime < REQUEST_INTERVAL) {
                // 如果缓存还有效，直接返回缓存数据
                if (adminBillsCache != null && (currentTime - adminCacheTime) < CACHE_EXPIRE_TIME) {
                    return getDataTable(adminBillsCache);
                }
            }
            lastRequestTime = currentTime;
            
            // 检查缓存是否有效
            if (adminBillsCache == null || (currentTime - adminCacheTime) > CACHE_EXPIRE_TIME) {
                startPage();
                List<ExpenseBill> list = expenseBillService.selectExpenseBillList(expenseBill);
                // 过滤掉已入库（status=0）的账单
                list.removeIf(bill -> "0".equals(bill.getStatus()));
                
                // 更新缓存
                adminBillsCache = list;
                adminCacheTime = currentTime;
                
                return getDataTable(list);
            } else {
                // 使用缓存数据
                return getDataTable(adminBillsCache);
            }
        }
        
        log.info("当前登录用户：{}", userName);
        
        // 检查是否为系统管理员或费用管理员
        Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
        boolean isAdmin = ShiroUtils.getSysUser().isAdmin() || roleKeys.contains("expense_admin");
        
        // 根据用户名查询核对人信息
        ExpenseDepartmentVerifierInfo verifier = verifierInfoService.selectVerifierInfoByName(userName);
        
        if (verifier != null) {
            log.info("核对人信息：{}，部门：{}", verifier.getName(), verifier.getDepartmentName());
            // 设置部门名称作为查询条件
            expenseBill.setTransfer_department(verifier.getDepartmentName());
        } else {
            log.warn("未找到用户[{}]对应的核对人信息", userName);
            
            // 如果是管理员但没有核对人信息，允许查看所有账单
            if (isAdmin) {
                log.info("用户[{}]是管理员，允许查看所有部门的账单", userName);
                // 不设置部门限制，查看所有账单
            } else {
                // 普通用户没有核对人信息，返回空列表
                log.warn("普通用户[{}]没有核对人信息，无法查看账单", userName);
                return getDataTable(new ArrayList<>());
            }
        }
        
        startPage();
        List<ExpenseBill> list = expenseBillService.selectExpenseBillList(expenseBill);
        // 过滤掉已入库（status=0）的账单
        list.removeIf(bill -> "0".equals(bill.getStatus()));
        return getDataTable(list);
    }
    
    /**
     * 清理admin缓存
     */
    private void clearAdminCache() {
        adminBillsCache = null;
        adminCacheTime = 0;
    }

    /**
     * 确认账单
     */
    @RequiresPermissions("expense:bill_verify:confirm")
    @Log(title = "账单核对", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    @ResponseBody
    public AjaxResult confirm(@RequestParam(value = "billIds[]", required = false) List<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return AjaxResult.error("未选择账单");
        }
        
        String currentUser = ShiroUtils.getSysUser().getUserName();
        int success = 0, fail = 0;
        StringBuilder failMsg = new StringBuilder();
        
        for (Long billId : billIds) {
            ExpenseBill bill = expenseBillService.selectExpenseBillById(billId);
            if (bill == null) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不存在; ");
                continue;
            }
            
            if (!"1".equals(bill.getStatus())) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不是已发布状态; ");
                continue;
            }
            
            // 更新账单状态为已确认
            bill.setStatus("2"); // 2=已确认
            bill.setBill_confirmer(currentUser);
            int result = expenseBillService.updateExpenseBill(bill);
            // 确认后清空退回人和退回意见
            if (result > 0) {
                expenseBillService.clearReturnInfo(billId);
                success++;
            } else {
                fail++;
                failMsg.append("账单ID:" + billId + " 确认失败; ");
            }
        }
        
        // 清理缓存
        clearAdminCache();
        
        if (fail == 0) {
            return AjaxResult.success("成功确认 " + success + " 条账单");
        } else {
            return AjaxResult.error("成功确认 " + success + " 条，失败 " + fail + " 条。" + failMsg.toString());
        }
    }
    
    /**
     * 退回账单（保留费用明细）
     */
    @RequiresPermissions("expense:bill_verify:return")
    @Log(title = "账单核对", businessType = BusinessType.UPDATE)
    @PostMapping("/return")
    @ResponseBody
    public AjaxResult returnBill(@RequestParam(value = "billIds[]", required = false) List<Long> billIds,
                             @RequestParam(value = "refuseComment", required = false) String refuseComment) {
        if (billIds == null || billIds.isEmpty()) {
            return AjaxResult.error("未选择账单");
        }
        
        String currentUser = ShiroUtils.getSysUser().getUserName();
        int success = 0, fail = 0;
        StringBuilder failMsg = new StringBuilder();
        List<ExpenseBill> returnedBills = new ArrayList<>();
        
        for (Long billId : billIds) {
            ExpenseBill bill = expenseBillService.selectExpenseBillById(billId);
            if (bill == null) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不存在; ");
                continue;
            }
            
            if (!"1".equals(bill.getStatus())) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不是已发布状态; ");
                continue;
            }
            
            // 更新账单状态为已退回
            bill.setStatus("3"); // 3=已退回
            bill.setBill_returner(currentUser);
            bill.setBill_refuse_comment(refuseComment);
            
            int result = expenseBillService.updateExpenseBill(bill);
            if (result > 0) {
                success++;
                returnedBills.add(bill);
            } else {
                fail++;
                failMsg.append("账单ID:" + billId + " 退回失败; ");
            }
        }
        
        // 发送退回通知给账单上传人
        if (!returnedBills.isEmpty()) {
            try {
                log.info("开始发送账单退回通知，共{}份账单", returnedBills.size());
                String notificationResult = expenseBillNotificationService.sendBillReturnNotifications(returnedBills, refuseComment);
                log.info("账单退回通知发送结果：{}", notificationResult);
            } catch (Exception e) {
                log.error("发送账单退回通知时发生异常", e);
                // 通知发送失败不影响退回结果，只记录日志
            }
        }
        
        // 清理缓存
        clearAdminCache();
        
        if (fail == 0) {
            return AjaxResult.success("成功退回 " + success + " 条账单");
        } else {
            return AjaxResult.error("成功退回 " + success + " 条，失败 " + fail + " 条。" + failMsg.toString());
        }
    }

    /**
     * 导出账单关联的费用明细
     */
    @RequiresPermissions("expense:bill_verify:export")
    @Log(title = "账单明细导出", businessType = BusinessType.EXPORT)
    @GetMapping("/export/{id}")
    public void export(@PathVariable("id") Long id, HttpServletResponse response) throws Exception
    {
        expenseDetailsService.exportBillDetails(id, response);
    }
    
    /**
     * 格式化账单状态
     */
    private String statusFormat(String value) {
        switch(value) {
            case "0": return "已入库";
            case "1": return "已发布";
            case "2": return "已确认";
            case "3": return "已退回";
            default: return value;
        }
    }
} 