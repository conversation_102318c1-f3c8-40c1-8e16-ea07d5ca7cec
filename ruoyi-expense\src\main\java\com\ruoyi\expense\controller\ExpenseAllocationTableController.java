package com.ruoyi.expense.controller;

import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.expense.domain.ExpenseAllocationTable;
import com.ruoyi.expense.service.IExpenseAllocationTableService;
import com.ruoyi.expense.service.IExpenseAllocationTableNotificationService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.service.IExpenseBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 分摊表管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Controller
@RequestMapping("/expense/allocation_table")
public class ExpenseAllocationTableController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ExpenseAllocationTableController.class);

    private String prefix = "expense/allocation_table";

    @Autowired
    private IExpenseAllocationTableService expenseAllocationTableService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private IExpenseAllocationTableNotificationService expenseAllocationTableNotificationService;

    @Autowired
    private IExpenseBillService expenseBillService;

    /**
     * 分摊表管理主页面
     */
    @GetMapping()
    public String allocationTable(ModelMap mmap)
    {
        // 获取当前用户的角色信息
        Long userId = ShiroUtils.getSysUser().getUserId();
        Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
        boolean isAdmin = ShiroUtils.getSysUser().isAdmin() || roleKeys.contains("expense_admin");
        boolean isChecker = roleKeys.contains("expense_allocationTable_checker");
        
        mmap.put("isAdmin", isAdmin);
        mmap.put("isChecker", isChecker);
        mmap.put("currentUser", ShiroUtils.getLoginName());
        
        return prefix + "/allocation_table";
    }

    /**
     * 查询分摊表管理列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExpenseAllocationTable expenseAllocationTable)
    {
        startPage();
        List<ExpenseAllocationTable> list = expenseAllocationTableService.selectExpenseAllocationTableList(expenseAllocationTable);
        return getDataTable(list);
    }

    /**
     * 导出分摊表管理列表
     */
    @Log(title = "分摊表管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ExpenseAllocationTable expenseAllocationTable)
    {
        List<ExpenseAllocationTable> list = expenseAllocationTableService.selectExpenseAllocationTableList(expenseAllocationTable);
        ExcelUtil<ExpenseAllocationTable> util = new ExcelUtil<ExpenseAllocationTable>(ExpenseAllocationTable.class);
        return util.exportExcel(list, "分摊表管理数据");
    }

    /**
     * 新增分摊表管理页面
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存分摊表管理
     */
    @Log(title = "分摊表管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ExpenseAllocationTable expenseAllocationTable)
    {
        return toAjax(expenseAllocationTableService.insertExpenseAllocationTable(expenseAllocationTable));
    }

    /**
     * 修改分摊表管理页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        ExpenseAllocationTable expenseAllocationTable = expenseAllocationTableService.selectExpenseAllocationTableById(id);
        mmap.put("expenseAllocationTable", expenseAllocationTable);
        return prefix + "/edit";
    }

    /**
     * 修改保存分摊表管理
     */
    @Log(title = "分摊表管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ExpenseAllocationTable expenseAllocationTable)
    {
        return toAjax(expenseAllocationTableService.updateExpenseAllocationTable(expenseAllocationTable));
    }

    /**
     * 删除分摊表管理
     */
    @Log(title = "分摊表管理", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(expenseAllocationTableService.deleteExpenseAllocationTableByIds(Convert.toLongArray(ids)));
    }

    /**
     * 确认分摊表（分摊表核对人操作）
     */
    @Log(title = "分摊表确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    @ResponseBody
    public AjaxResult confirm(@RequestParam("id") Long id)
    {
        try {
            // 检查用户权限
            Long userId = ShiroUtils.getSysUser().getUserId();
            Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
            if (!roleKeys.contains("expense_allocationTable_checker")) {
                return AjaxResult.error("您没有分摊表核对权限");
            }

            // 使用当前用户的userName作为负责人
            String responsiblePerson = ShiroUtils.getSysUser().getUserName();
            int result = expenseAllocationTableService.confirmAllocationTable(id, responsiblePerson);
            if (result > 0) {
                return AjaxResult.success("分摊表确认成功");
            } else {
                return AjaxResult.error("分摊表确认失败，请检查分摊表状态");
            }
        } catch (Exception e) {
            log.error("确认分摊表时发生异常", e);
            return AjaxResult.error("确认分摊表失败：" + e.getMessage());
        }
    }

    /**
     * 退回分摊表（分摊表核对人操作）
     */
    @Log(title = "分摊表退回", businessType = BusinessType.UPDATE)
    @PostMapping("/reject")
    @ResponseBody
    public AjaxResult reject(@RequestParam("id") Long id, @RequestParam("comments") String comments)
    {
        try {
            // 检查用户权限
            Long userId = ShiroUtils.getSysUser().getUserId();
            Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
            if (!roleKeys.contains("expense_allocationTable_checker")) {
                return AjaxResult.error("您没有分摊表核对权限");
            }

            int result = expenseAllocationTableService.rejectAllocationTable(id, comments);
            if (result > 0) {
                return AjaxResult.success("分摊表退回成功");
            } else {
                return AjaxResult.error("分摊表退回失败，请检查分摊表状态");
            }
        } catch (Exception e) {
            log.error("退回分摊表时发生异常", e);
            return AjaxResult.error("退回分摊表失败：" + e.getMessage());
        }
    }

    /**
     * 在线查看分摊表内容
     */
    @GetMapping("/viewDetail/{id}")
    public String viewDetail(@PathVariable("id") Long id, ModelMap mmap)
    {
        // 检查用户权限
        Long userId = ShiroUtils.getSysUser().getUserId();
        Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
        boolean hasPermission = ShiroUtils.getSysUser().isAdmin() || 
                              roleKeys.contains("expense_admin") || 
                              roleKeys.contains("expense_allocationTable_checker");
        
        if (!hasPermission) {
            return "error/unauth";
        }

        ExpenseAllocationTable allocationTable = expenseAllocationTableService.selectExpenseAllocationTableById(id);
        if (allocationTable == null) {
            return "error/404";
        }

        mmap.put("allocationTable", allocationTable);
        return prefix + "/view_detail";
    }

    /**
     * 导出分摊表Excel
     */
    @Log(title = "分摊表导出", businessType = BusinessType.EXPORT)
    @GetMapping("/exportDetail/{id}")
    public void exportDetail(@PathVariable("id") Long id, HttpServletResponse response) throws Exception
    {
        // 检查用户权限
        Long userId = ShiroUtils.getSysUser().getUserId();
        Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
        boolean hasPermission = ShiroUtils.getSysUser().isAdmin() || 
                              roleKeys.contains("expense_admin") || 
                              roleKeys.contains("expense_allocationTable_checker");
        
        if (!hasPermission) {
            throw new RuntimeException("您没有导出权限");
        }

        expenseAllocationTableService.exportAllocationTable(id, response);
    }

    /**
     * 生成分摊表（从账单管理界面调用）
     */
    @Log(title = "生成分摊表", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    @ResponseBody
    public AjaxResult generate(@RequestParam("expenseType") String expenseType, 
                              @RequestParam("billingCycle") String billingCycle)
    {
        try {
            // 检查是否已存在相同的分摊表
            if (expenseAllocationTableService.checkExistByExpenseTypeAndBillingCycle(expenseType, billingCycle)) {
                return AjaxResult.error("该费用类型和计费周期的分摊表已存在");
            }

            String reviewer = ShiroUtils.getSysUser().getUserName();
            int result = expenseAllocationTableService.generateAllocationTable(expenseType, billingCycle, reviewer);
            
            if (result > 0) {
                return AjaxResult.success("分摊表生成成功");
            } else {
                return AjaxResult.error("分摊表生成失败，未找到相关账单数据");
            }
        } catch (Exception e) {
            log.error("生成分摊表时发生异常", e);
            return AjaxResult.error("生成分摊表失败：" + e.getMessage());
        }
    }

    /**
     * 获取分摊表数据
     */
    @PostMapping("/getAllocationData")
    @ResponseBody
    public AjaxResult getAllocationData(@RequestParam("expenseType") String expenseType, 
                                       @RequestParam("billingCycle") String billingCycle)
    {
        try {
            // 查询相关账单数据
            ExpenseBill queryBill = new ExpenseBill();
            queryBill.setExpense_type(expenseType);
            queryBill.setBilling_cycle(billingCycle);
            List<ExpenseBill> bills = expenseBillService.selectExpenseBillList(queryBill);
            
            if (bills == null || bills.isEmpty()) {
                return AjaxResult.error("未找到相关账单数据");
            }
            
            // 返回账单数据，前端将根据这些数据构建分摊表
            return AjaxResult.success(bills);
        } catch (Exception e) {
            log.error("获取分摊表数据时发生异常", e);
            return AjaxResult.error("获取分摊表数据失败：" + e.getMessage());
        }
    }

        /**
     * 根据参数导出账单分摊表（从账单管理迁移过来的功能）
     */
    @Log(title = "分摊表导出", businessType = BusinessType.EXPORT)
    @GetMapping("/exportShareByParams")
    public void exportShareByParams(@RequestParam(value = "expenseType", required = true) String expenseType,
                                  @RequestParam(value = "billingCycle", required = true) String billingCycle,
                                  HttpServletResponse response) throws Exception
    {
        // 检查用户权限
        Long userId = ShiroUtils.getSysUser().getUserId();
        Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
        boolean hasPermission = ShiroUtils.getSysUser().isAdmin() ||
                              roleKeys.contains("expense_admin") ||
                              roleKeys.contains("expense_allocationTable_checker");

        if (!hasPermission) {
            throw new RuntimeException("您没有导出权限");
        }

        expenseBillService.exportShareBillByParams(expenseType, billingCycle, response);
    }

    /**
     * 获取单行分摊表数据（用于局部刷新）
     */
    @PostMapping("/getRowData")
    @ResponseBody
    public AjaxResult getRowData(@RequestParam("id") Long id)
    {
        try {
            ExpenseAllocationTable allocationTable = expenseAllocationTableService.selectExpenseAllocationTableById(id);
            if (allocationTable != null) {
                return AjaxResult.success(allocationTable);
            } else {
                return AjaxResult.error("分摊表不存在");
            }
        } catch (Exception e) {
            log.error("获取分摊表数据失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 一键确认所有处于审核中状态的分摊表
     */
    @Log(title = "分摊表一键确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirmAllPending")
    @ResponseBody
    public AjaxResult confirmAllPending()
    {
        try {
            // 检查用户权限
            Long userId = ShiroUtils.getSysUser().getUserId();
            Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
            if (!roleKeys.contains("expense_allocationTable_checker")) {
                return AjaxResult.error("您没有分摊表核对权限");
            }

            // 使用当前用户的userName作为负责人
            String responsiblePerson = ShiroUtils.getSysUser().getUserName();
            int result = expenseAllocationTableService.confirmAllPendingAllocationTables(responsiblePerson);
            
            if (result > 0) {
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("没有找到处于审核中状态的分摊表");
            }
        } catch (Exception e) {
            log.error("一键确认分摊表时发生异常", e);
            return AjaxResult.error("一键确认失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有处于审核中状态的分摊表ID列表
     */
    @PostMapping("/getPendingIds")
    @ResponseBody
    public AjaxResult getPendingIds()
    {
        try {
            // 检查用户权限
            Long userId = ShiroUtils.getSysUser().getUserId();
            Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
            boolean hasPermission = ShiroUtils.getSysUser().isAdmin() ||
                                  roleKeys.contains("expense_admin") ||
                                  roleKeys.contains("expense_allocationTable_checker");

            if (!hasPermission) {
                return AjaxResult.error("您没有导出权限");
            }

            List<Long> pendingIds = expenseAllocationTableService.selectPendingAllocationTableIds();
            return AjaxResult.success(pendingIds);

        } catch (Exception e) {
            log.error("获取审核中分摊表ID时发生异常", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 批量通知核对（通知所有审核中状态的分摊表）
     */
    @RequiresPermissions("expense:allocation_table:notify")
    @Log(title = "分摊表通知核对", businessType = BusinessType.OTHER)
    @PostMapping("/notifyAllocationTableCheck")
    @ResponseBody
    public AjaxResult notifyAllocationTableCheck() {
        try {
            // 检查用户权限
            Long userId = ShiroUtils.getSysUser().getUserId();
            Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
            boolean hasPermission = ShiroUtils.getSysUser().isAdmin() || roleKeys.contains("expense_admin");

            if (!hasPermission) {
                return AjaxResult.error("您没有通知权限");
            }

            // 查询所有审核中状态的分摊表
            ExpenseAllocationTable queryTable = new ExpenseAllocationTable();
            queryTable.setStatus("审核中");
            List<ExpenseAllocationTable> pendingTables = expenseAllocationTableService.selectExpenseAllocationTableList(queryTable);

            if (pendingTables == null || pendingTables.isEmpty()) {
                return AjaxResult.error("当前没有处于'审核中'状态的分摊表需要通知");
            }

            log.info("开始批量通知分摊表核对，共{}个分摊表", pendingTables.size());
            String notificationResult = expenseAllocationTableNotificationService.sendAllocationTableNotifications(pendingTables);
            log.info("批量通知分摊表核对结果：{}", notificationResult);

            if (notificationResult.contains("发送成功")) {
                return AjaxResult.success(notificationResult);
            } else {
                return AjaxResult.error(notificationResult);
            }
        } catch (Exception e) {
            log.error("批量通知分摊表核对时发生异常", e);
            return AjaxResult.error("通知发送失败：" + e.getMessage());
        }
    }

    /**
     * 单独通知核对（通知指定的单个分摊表）
     */
    @RequiresPermissions("expense:allocation_table:notify")
    @Log(title = "分摊表通知核对", businessType = BusinessType.OTHER)
    @PostMapping("/notifySingleAllocationTableCheck")
    @ResponseBody
    public AjaxResult notifySingleAllocationTableCheck(@RequestParam("allocationTableId") Long allocationTableId) {
        try {
            if (allocationTableId == null) {
                return AjaxResult.error("分摊表ID不能为空");
            }

            // 检查用户权限
            Long userId = ShiroUtils.getSysUser().getUserId();
            Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
            boolean hasPermission = ShiroUtils.getSysUser().isAdmin() || roleKeys.contains("expense_admin");

            if (!hasPermission) {
                return AjaxResult.error("您没有通知权限");
            }

            ExpenseAllocationTable allocationTable = expenseAllocationTableService.selectExpenseAllocationTableById(allocationTableId);
            if (allocationTable == null) {
                return AjaxResult.error("分摊表不存在");
            }

            if (!"审核中".equals(allocationTable.getStatus())) {
                return AjaxResult.error("只能通知处于'审核中'状态的分摊表");
            }

            log.info("开始单独通知分摊表核对，分摊表ID：{}", allocationTableId);
            String notificationResult = expenseAllocationTableNotificationService.sendSingleAllocationTableNotification(allocationTable);
            log.info("单独通知分摊表核对结果：{}", notificationResult);

            if (notificationResult.contains("发送成功")) {
                return AjaxResult.success(notificationResult);
            } else {
                return AjaxResult.error(notificationResult);
            }
        } catch (Exception e) {
            log.error("单独通知分摊表核对时发生异常", e);
            return AjaxResult.error("通知发送失败：" + e.getMessage());
        }
    }

}