<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('账单详情')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <table class="table table-bordered">
                    <tr><th>费用类型</th><td th:text="${bill.expense_type}"></td></tr>
                    <tr><th>计费周期</th><td th:text="${bill.billing_cycle}"></td></tr>
                    <tr><th>划账部门</th><td th:text="${bill.transfer_department}"></td></tr>
                    <tr><th>账单状态</th><td th:switch="${bill.status}">
                        <span th:case="'0'">已入库</span>
                        <span th:case="'1'">已发布</span>
                        <span th:case="'2'">已确认</span>
                        <span th:case="'3'">已退回</span>
                        <span th:case="*" th:text="${bill.status}"></span>
                    </td></tr>
                    <tr><th>账单发布人</th><td th:text="${bill.bill_publisher}"></td></tr>
                    <tr><th>账单确认人</th><td th:text="${bill.bill_confirmer}"></td></tr>
                    <tr><th>账单退回人</th><td th:text="${bill.bill_returner}"></td></tr>
                    <tr><th>账单退回意见</th><td th:text="${bill.bill_refuse_comment}"></td></tr>
                    <tr><th>含税总价</th><td th:text="${#numbers.formatDecimal(bill.total_price_with_tax, 1, 2)}"></td></tr>
                    <tr><th>不含税总价</th><td th:text="${#numbers.formatDecimal(bill.total_price_without_tax, 1, 2)}"></td></tr>
                </table>
                <h4>费用明细</h4>
                <div style="max-height:400px;overflow:auto;">
                <table class="table table-bordered table-striped table-fixed-header">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>编号</th>
                            <th>品牌</th>
                            <th>具体规格</th>
                            <th>费用类型</th>
                            <th>计费周期</th>
                            <th>划账部门</th>
                            <th>数量</th>
                            <th>费用变动情况</th>
                            <th>备注</th>
                            <th>含税单价</th>
                            <th>不含税单价</th>
                            <th>含税单行总价</th>
                            <th>不含税单行总价</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="detail : ${detailsList}">
                            <td th:text="${detail.name}"></td>
                            <td th:text="${detail.number}"></td>
                            <td th:text="${detail.brand}"></td>
                            <td th:text="${detail.specificSpecification}"></td>
                            <td th:text="${detail.expenseType}"></td>
                            <td th:text="${detail.billingCycle}"></td>
                            <td th:text="${detail.transferDepartment}"></td>
                            <td th:text="${detail.quantity}"></td>
                            <td th:text="${detail.expenseChangeStatus}"></td>
                            <td th:text="${detail.remarks}"></td>
                            <td th:text="${#numbers.formatDecimal(detail.unitPriceIncludingTax, 1, 2)}"></td>
                            <td th:text="${#numbers.formatDecimal(detail.unitPriceExcludingTax, 1, 2)}"></td>
                            <td th:text="${#numbers.formatDecimal(detail.totalLinePriceIncludingTax, 1, 2)}"></td>
                            <td th:text="${#numbers.formatDecimal(detail.totalLinePriceExcludingTax, 1, 2)}"></td>
                        </tr>
                    </tbody>
                </table>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <style>
    .table-fixed-header thead th {
      position: sticky;
      top: 0;
      background: #f8f9fa;
      z-index: 2;
    }
    </style>
</body>
</html> 