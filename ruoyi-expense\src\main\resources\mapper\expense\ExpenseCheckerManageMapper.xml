<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.expense.mapper.ExpenseCheckerManageMapper">
    <resultMap id="CheckerManageMap" type="com.ruoyi.expense.domain.ExpenseCheckerManage">
        <id property="id" column="ID"/>
        <result property="name" column="Name"/>
        <result property="ehrNumber" column="EHR_Number"/>
        <result property="departmentName" column="Department_Name"/>
        <result property="remarks" column="Remarks"/>
    </resultMap>

    <select id="selectCheckerManageList" resultMap="CheckerManageMap">
        SELECT ID, Name, EHR_Number, Department_Name, Remarks
        FROM expense_departmentverifierinfo
        <where>
            <if test="name != null and name != ''">AND Name LIKE CONCAT('%', #{name}, '%')</if>
        </where>
    </select>

    <insert id="insertCheckerManage" parameterType="com.ruoyi.expense.domain.ExpenseCheckerManage">
        INSERT INTO expense_departmentverifierinfo (Name, EHR_Number, Department_Name, Remarks)
        VALUES (#{name}, #{ehrNumber}, #{departmentName}, #{remarks})
    </insert>

    <update id="updateCheckerManage" parameterType="com.ruoyi.expense.domain.ExpenseCheckerManage">
        UPDATE expense_departmentverifierinfo 
        SET Department_Name = #{departmentName}, Remarks = #{remarks}
        WHERE ID = #{id}
    </update>

    <delete id="deleteCheckerManageById" parameterType="int">
        DELETE FROM expense_departmentverifierinfo WHERE ID = #{id}
    </delete>

    <delete id="deleteCheckerManageByIds">
        DELETE FROM expense_departmentverifierinfo WHERE ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByNameAndEhrNumber" resultMap="CheckerManageMap">
        SELECT ID, Name, EHR_Number, Department_Name, Remarks
        FROM expense_departmentverifierinfo
        WHERE Name = #{name} AND EHR_Number = #{ehrNumber}
    </select>

    <select id="selectByName" resultMap="CheckerManageMap">
        SELECT ID, Name, EHR_Number, Department_Name, Remarks
        FROM expense_departmentverifierinfo
        WHERE Name = #{name}
    </select>

    <select id="selectByEhrNumber" resultMap="CheckerManageMap">
        SELECT ID, Name, EHR_Number, Department_Name, Remarks
        FROM expense_departmentverifierinfo
        WHERE EHR_Number = #{ehrNumber}
    </select>

    <select id="selectCheckerManageById" parameterType="int" resultMap="CheckerManageMap">
        SELECT ID, Name, EHR_Number, Department_Name, Remarks FROM expense_departmentverifierinfo WHERE ID = #{id}
    </select>

    <select id="selectByDepartmentName" resultMap="CheckerManageMap">
        SELECT ID, Name, EHR_Number, Department_Name, Remarks
        FROM expense_departmentverifierinfo
        WHERE Department_Name = #{departmentName}
    </select>
</mapper> 