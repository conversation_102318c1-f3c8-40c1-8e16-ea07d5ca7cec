<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改通讯线路管理')" />
    <th:block th:include="include :: datetimepicker-css" />
    <style>
        /* 为只读字段添加视觉样式 */
        input[readonly], select[disabled] {
            background-color: #f5f5f5 !important;
            color: #666 !important;
            cursor: not-allowed;
        }
        .disabled .input-group-addon {
            background-color: #f5f5f5 !important;
            color: #666 !important;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-manage-edit" th:object="${lineAlldata}">
            <input name="lineCode" th:field="*{lineCode}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路编号：</label>
                <div class="col-sm-8">
                    <input name="lineNumber" th:field="*{lineNumber}" class="form-control" type="text" th:readonly="${isLineViewer}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">所属机构：</label>
                <div class="col-sm-8">
                    <select name="institution" class="form-control m-b" th:with="type=${@dict.getType('line_institude')}" required th:disabled="${isLineViewer}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{institution}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">运营商：</label>
                <div class="col-sm-8">
                    <select name="operator" class="form-control m-b" th:with="type=${@dict.getType('line_operator')}" required th:disabled="${isLineViewer}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{operator}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">线路名称：</label>
                <div class="col-sm-8">
                    <input name="lineName" th:field="*{lineName}" class="form-control" type="text" required th:readonly="${isLineViewer}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">线路类型：</label>
                <div class="col-sm-8">
                    <select name="lineType" class="form-control m-b" th:with="type=${@dict.getType('lineType')}" required th:disabled="${isLineViewer}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{lineType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路带宽：</label>
                <div class="col-sm-8">
                    <input name="lineBandwidth" th:field="*{lineBandwidth}" class="form-control" type="text" th:readonly="${isLineViewer}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路状态：</label>
                <div class="col-sm-8">
                    <select name="lineStatus" class="form-control m-b" th:with="type=${@dict.getType('lineStatus')}" required th:disabled="${isLineViewer}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{lineStatus}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">地址：</label>
                <div class="col-sm-8">
                    <input name="address" th:field="*{address}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">含税价：</label>
                <div class="col-sm-8">
                    <input name="taxIncludingPrice" th:field="*{taxIncludingPrice}" class="form-control" type="text" th:readonly="${isLineViewer}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">不含税价：</label>
                <div class="col-sm-8">
                    <input name="taxExcludingPrice" th:field="*{taxExcludingPrice}" class="form-control" type="text" th:readonly="${isLineViewer}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">对端联系人：</label>
                <div class="col-sm-8">
                    <input name="oppositeContact" th:field="*{oppositeContact}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路使用环境：</label>
                <div class="col-sm-8">
                    <input name="lineUsageEnvironment" th:field="*{lineUsageEnvironment}" class="form-control" type="text" th:readonly="${isLineViewer}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">税率：</label>
                <div class="col-sm-8">
                    <input name="taxRate" th:field="*{taxRate}" class="form-control" type="text" th:readonly="${isLineViewer}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路新增日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date" th:class="${isLineViewer ? 'input-group date disabled' : 'input-group date'}">
                        <input name="lineAddDate" th:value="${#dates.format(lineAlldata.lineAddDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" th:readonly="${isLineViewer}">
                        <span class="input-group-addon" th:if="${!isLineViewer}"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路撤销日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date" th:class="${isLineViewer ? 'input-group date disabled' : 'input-group date'}">
                        <input name="lineCancelDate" th:value="${#dates.format(lineAlldata.lineCancelDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" th:readonly="${isLineViewer}">
                        <span class="input-group-addon" th:if="${!isLineViewer}"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" th:field="*{remark}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "comline/manage";
        var isLineViewer = [[${isLineViewer}]];
        
        // 添加自定义验证方法
        $.validator.addMethod("maxDecimalPlaces", function(value, element, param) {
            if (value === "") return true; // 空值通过验证
            var regex = new RegExp("^\\d+(\\.\\d{1," + param + "})?$");
            return regex.test(value);
        }, "最多只能有{0}位小数");
        
        $("#form-manage-edit").validate({
            focusCleanup: true,
            rules: {
                taxIncludingPrice: {
                    number: true,
                    maxDecimalPlaces: 2
                },
                taxExcludingPrice: {
                    number: true,
                    maxDecimalPlaces: 2
                }
            },
            messages: {
                taxIncludingPrice: {
                    maxDecimalPlaces: "含税价格最多只能有2位小数"
                },
                taxExcludingPrice: {
                    maxDecimalPlaces: "不含税价格最多只能有2位小数"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-manage-edit').serialize());
            }
        }

        // 只有非line_viewer用户才初始化日期选择器
        if (!isLineViewer) {
            $("input[name='lineAddDate']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true
            });

            $("input[name='lineCancelDate']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true
            });
        }
    </script>
</body>
</html>