package com.ruoyi.line.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 工单数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public class WorkOrderDTO {
    
    /** 工单类型：1-新增，2-撤销，3-移机，4-扩容，5-停用 */
    private String operationType;
    
    /** 选中的线路ID列表（用于撤销、移机、扩容、停用操作） */
    private List<Long> selectedLineIds;
    
    /** 运营商（新增时填写） */
    private String operator;
    
    /** 带宽（新增和扩容时填写） */
    private String bandwidth;
    
    /** 地址（新增和移机时填写） */
    private String address;
    
    /** 联系人（新增时填写） */
    private String contact;
    
    /** 新地址（移机时填写） */
    private String newAddress;
    
    /** 备注（停用时填写） */
    private String remarks;
    
    /** 工单生成日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workOrderDate;
    
    /** 发送单位 */
    private String senderUnit;
    
    /** 接收单位（运营商） */
    private String receiverUnit;

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public List<Long> getSelectedLineIds() {
        return selectedLineIds;
    }

    public void setSelectedLineIds(List<Long> selectedLineIds) {
        this.selectedLineIds = selectedLineIds;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getBandwidth() {
        return bandwidth;
    }

    public void setBandwidth(String bandwidth) {
        this.bandwidth = bandwidth;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getNewAddress() {
        return newAddress;
    }

    public void setNewAddress(String newAddress) {
        this.newAddress = newAddress;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Date getWorkOrderDate() {
        return workOrderDate;
    }

    public void setWorkOrderDate(Date workOrderDate) {
        this.workOrderDate = workOrderDate;
    }

    public String getSenderUnit() {
        return senderUnit;
    }

    public void setSenderUnit(String senderUnit) {
        this.senderUnit = senderUnit;
    }

    public String getReceiverUnit() {
        return receiverUnit;
    }

    public void setReceiverUnit(String receiverUnit) {
        this.receiverUnit = receiverUnit;
    }

    @Override
    public String toString() {
        return "WorkOrderDTO{" +
                "operationType='" + operationType + '\'' +
                ", selectedLineIds=" + selectedLineIds +
                ", operator='" + operator + '\'' +
                ", bandwidth='" + bandwidth + '\'' +
                ", address='" + address + '\'' +
                ", contact='" + contact + '\'' +
                ", newAddress='" + newAddress + '\'' +
                ", remarks='" + remarks + '\'' +
                ", workOrderDate=" + workOrderDate +
                ", senderUnit='" + senderUnit + '\'' +
                ", receiverUnit='" + receiverUnit + '\'' +
                '}';
    }
} 