<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分摊表基本信息 - 优化前后对比</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
        }

        .comparison-container {
            display: flex;
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .comparison-section {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 16px;
        }

        .before-title {
            background: #ffebee;
            color: #c62828;
        }

        .after-title {
            background: #e8f5e8;
            color: #2e7d32;
        }

        /* 原始样式 */
        .original .ibox {
            background: #ffffff;
            border: 1px solid #e7eaec;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .original .ibox-title {
            background-color: #f5f5f6;
            border-bottom: 1px solid #e7eaec;
            padding: 14px 15px 7px;
            min-height: 48px;
        }

        .original .ibox-title h5 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
        }

        .original .ibox-content {
            padding: 15px 20px 20px 20px;
            background: #ffffff;
        }

        .original .form-group {
            margin-bottom: 15px;
        }

        .original .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .original .badge {
            padding: 3px 7px;
            font-size: 11px;
            border-radius: 3px;
        }

        .original .badge-warning {
            background-color: #f0ad4e;
            color: #fff;
        }

        /* 优化后样式 */
        .optimized .allocation-detail-container {
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .optimized .allocation-detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 18px;
            margin: 0;
            border-bottom: 2px solid #5a67d8;
        }

        .optimized .allocation-detail-header h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .optimized .allocation-detail-header h5 i {
            margin-right: 8px;
            font-size: 14px;
        }

        .optimized .allocation-detail-content {
            padding: 15px;
            background: #fafbfc;
        }

        .optimized .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 12px;
            background: white;
            border-radius: 6px;
            padding: 12px 15px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            border-left: 3px solid #667eea;
        }

        .optimized .info-item {
            flex: 1;
            min-width: 200px;
            margin-bottom: 8px;
            padding-right: 15px;
        }

        .optimized .info-item.half-width {
            flex: 1 1 50%;
            min-width: 250px;
        }

        .optimized .info-item.third-width {
            flex: 1 1 33.333%;
            min-width: 180px;
        }

        .optimized .info-label {
            display: inline-block;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 4px;
            font-size: 13px;
            position: relative;
        }

        .optimized .info-label::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 24px;
            height: 1.5px;
            background: #667eea;
            border-radius: 1px;
        }

        .optimized .info-value {
            display: flex;
            align-items: center;
            color: #2d3748;
            font-size: 14px;
            line-height: 1.4;
            padding: 6px 10px;
            background: #f7fafc;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
            min-height: 32px;
        }

        .optimized .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            color: #d63031;
            border: 1px solid #fdcb6e;
        }

        .optimized .info-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 14px;
        }

        .optimized .basic-info .info-value {
            background: #f3e5f5;
            border-color: #9c27b0;
            color: #6a1b9a;
        }

        .optimized .person-info .info-value {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1565c0;
        }

        .optimized .time-info .info-value {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 1200px) {
            .comparison-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; margin-bottom: 30px; color: #333;">分摊表基本信息 - 优化前后对比</h1>
    
    <div class="comparison-container">
        <!-- 优化前 -->
        <div class="comparison-section original">
            <div class="section-title before-title">优化前 - 原始样式</div>
            
            <div class="ibox">
                <div class="ibox-title">
                    <h5>分摊表基本信息</h5>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>分摊表名称：</label>
                                <span>202506-电子设备配件-分摊表</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>状态：</label>
                                <span class="badge badge-warning">审核中</span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>费用类型：</label>
                                <span>电子设备配件</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>计费周期：</label>
                                <span>202506</span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>制表人：</label>
                                <span>王海东</span>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>复核人：</label>
                                <span>若依</span>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>负责人：</label>
                                <span></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>创建时间：</label>
                                <span>2025.08.01 17:05:00</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>确认时间：</label>
                                <span>未确认</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 优化后 -->
        <div class="comparison-section optimized">
            <div class="section-title after-title">优化后 - 紧凑美观样式</div>
            
            <div class="allocation-detail-container">
                <div class="allocation-detail-header">
                    <h5><i class="fa fa-info-circle"></i>分摊表基本信息</h5>
                </div>
                <div class="allocation-detail-content">
                    <!-- 基本信息行 -->
                    <div class="info-row basic-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-file-text info-icon"></i>分摊表名称</span>
                            <span class="info-value">202506-电子设备配件-分摊表</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-flag info-icon"></i>状态</span>
                            <span class="info-value">
                                <span class="status-badge">审核中</span>
                            </span>
                        </div>
                    </div>

                    <!-- 费用信息行 -->
                    <div class="info-row basic-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-tags info-icon"></i>费用类型</span>
                            <span class="info-value">电子设备配件</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-calendar info-icon"></i>计费周期</span>
                            <span class="info-value">202506</span>
                        </div>
                    </div>

                    <!-- 人员信息行 -->
                    <div class="info-row person-info">
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user info-icon"></i>制表人</span>
                            <span class="info-value">王海东</span>
                        </div>
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user-check info-icon"></i>复核人</span>
                            <span class="info-value">若依</span>
                        </div>
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user-cog info-icon"></i>负责人</span>
                            <span class="info-value"></span>
                        </div>
                    </div>

                    <!-- 时间信息行 -->
                    <div class="info-row time-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-clock-o info-icon"></i>创建时间</span>
                            <span class="info-value">2025.08.01 17:05:00</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-check-circle info-icon"></i>确认时间</span>
                            <span class="info-value">未确认</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
