/**
 * bootstrap-suggest-plugin - v0.1.27
 * @description 这是一个基于 bootstrap 按钮式下拉菜单组件的搜索建议插件，必须使用于按钮式下拉菜单组件上。
 * <AUTHOR> - https://lzw.me
 * @GitHub https://github.com/lzwme/bootstrap-suggest-plugin.git
 * @since 2019-03-27 16:20:36
 */

!function(e){if("function"==typeof define&&define.amd)define(["jquery"],e);else if("object"==typeof exports&&"object"==typeof module)e(require("jquery"));else{if(!window.jQuery)throw new Error("Not found jQuery.");e(window.jQuery)}}(function(c){var p,l=c(window),s="ActiveXObject"in window,e=navigator.userAgent.match(/Chrome\/(\d+)/);e&&(e=+e[1]);var h=s||51<e,v="bsSuggest",g="onDataRequestSuccess",y="disabled",m=!0,w=!1;function b(e){return void 0===e}function x(e){return e.data()}function k(e,t){return b(t)?e.attr("alt"):e.attr("alt",t)}function S(e,t){return void 0!==t?e.attr("data-id",t):e.attr("data-id")}function j(e,t,n){if(t&&t.key){var i,r,o=n.separator||",",a=S(e);n&&n.multiWord?((i=e.val().split(o))[i.length-1]=t.key,a?(r=a.split(o)).push(t.id):r=[t.id],S(e,r.join(o)).val(i.join(o)).focus()):S(e,t.id||"").val(t.key).focus(),e.data("pre-val",e.val()).trigger("onSetSelectValue",[t,(n.data.value||n._lastData.value)[t.index]])}}function C(i,r,e){if(r.is(":visible")){var o=i.parent(),t=o.height(),n=o.width();e.autoDropup&&setTimeout(function(){var e=i.offset().top,t=l.scrollTop(),n=r.height();l.height()+t-e<n&&n+t<e?o.addClass("dropup"):o.removeClass("dropup")},10);var a={};return"left"===e.listAlign?a={left:i.siblings("div").width()-n,right:"auto"}:"right"===e.listAlign&&(a={left:"auto",right:0}),s&&!e.showBtn&&(o.hasClass("dropup")?(a.top="auto",a.bottom=t):(a.top=t,a.bottom="auto")),e.autoMinWidth||(a.minWidth=n),r.css(a),i}}function D(e,t){var n,i,r;if(-1===t.indexId&&!t.idField||t.multiWord)return e;i=t.inputBgColor,r=t.inputWarnColor;var o=e.val(),a=e.data("pre-val");return S(e)||!o?(e.css("background",i||""),!o&&a&&e.trigger("onUnsetSelectValue").data("pre-val","")):(n=e.css("backgroundColor").replace(/ /g,"").split(",",3).join(","),~r.indexOf(n)||e.trigger("onUnsetSelectValue").data("pre-val","").css("background",r)),e}function A(e,t,n){var i,r,o=e.parent().find("tbody tr."+n.listHoverCSS);o.length&&(i=(o.index()+3)*o.height(),(r=+t.css("maxHeight").replace("px",""))<i||t.scrollTop()>r?i-=r:i=0,t.scrollTop(i))}function F(e,t){e.find("tr."+t.listHoverCSS).removeClass(t.listHoverCSS)}function f(e){var t,n=m;for(t in e)if("value"===t){n=w;break}return n?(window.console&&window.console.trace,w):e.value.length?e:w}function T(e,t){var n=t.effectiveFields;return!("__index"===e||n.length&&!~c.inArray(e,n))}function H(e,t,n,i){n.html('<div style="padding:10px 5px 5px">'+e+"</div>").show(),C(t,n,i)}function q(e,t){var n=e.parent().find("ul:eq(0)");n.is(":visible")||(n.show(),e.trigger("onShowDropdown",[t?t.data.value:[]]))}function W(e,t){var n=e.parent().find("ul:eq(0)");n.is(":visible")&&(n.hide(),e.trigger("onHideDropdown",[t?t.data.value:[]]))}function B(e,t,n){var i,r,o,a,l,s,u,d=e.parent().find("ul:eq(0)"),f=0,c=['<table class="table table-condensed table-sm" style="margin:0">'],p=t.value;if(!t||!(i=p.length))return n.emptyTip?H(n.emptyTip,e,d,n):(d.empty(),W(e,n)),e;if(n._lastData&&JSON.stringify(n._lastData)===JSON.stringify(t)&&d.find("tr").length===i)return q(e,n),C(e,d,n);if(n._lastData=t,n.showHeader){for(o in c.push("<thead><tr>"),p[0])T(o,n)&&(c.push("<th>",n.effectiveFieldsAlias[o]||o,0===f?"("+i+")":"","</th>"),f++);c.push("</tr></thead>")}for(c.push("<tbody>"),r=0;r<i;r++){for(o in f=0,a=[],l=(u=p[r])[n.idField],s=u[n.keyField],u)b(s)&&n.indexKey===f&&(s=u[o]),b(l)&&n.indexId===f&&(l=u[o]),f++,T(o,n)&&a.push('<td data-name="',o,'">',u[o],"</td>");c.push('<tr data-index="',u.__index||r,'" data-id="',l,'" data-key="',s,'">',a.join(""),"</tr>")}return c.push("</tbody></table>"),d.html(c.join("")),q(e,n),setTimeout(function(){if(!h){var e=d.find("table:eq(0)"),t=0,n=0;d.height()<e.height()&&+d.css("minWidth").replace("px","")<d.width()&&(t=18,n=20),d.css("paddingRight",t),e.css("marginBottom",n)}},301),C(e,d,n),e}function _(t,n){n=n||"";var e=t._preAjax;e&&e.abort&&4!==e.readyState&&e.abort();var i={type:"GET",dataType:t.jsonp?"jsonp":"json",timeout:5e3};t.jsonp&&(i.jsonp=t.jsonp);var r,o=t.fnAdjustAjaxParam;if(c.isFunction(o)){if(r=o(n,t),w===r)return;c.extend(i,r)}return i.url=function(){if(!n||i.data)return i.url||t.url;var e="?";return/=$/.test(t.url)?e="":/\?/.test(t.url)&&(e="&"),t.url+e+encodeURIComponent(n)}(),t._preAjax=c.ajax(i).done(function(e){t.data=t.fnProcessData(e)}).fail(function(e){t.fnAjaxFail&&t.fnAjaxFail(e,t)})}function U(e,t,n,i){return n=c.trim(n),i.ignorecase&&(e=e.toLocaleLowerCase(),n=n.toLocaleLowerCase()),n&&(T(t,i)||(r=t,o=i,~c.inArray(r,o.searchFields)))&&(~n.indexOf(e)||i.twoWayMatch&&~e.indexOf(n));var r,o}var n={url:null,jsonp:null,data:{value:[]},indexId:0,indexKey:0,idField:"",keyField:"",autoSelect:m,allowNoKeyword:m,getDataMethod:"firstByUrl",delayUntilKeyup:w,ignorecase:w,effectiveFields:[],effectiveFieldsAlias:{},searchFields:[],twoWayMatch:m,multiWord:w,separator:",",delay:300,emptyTip:"",searchingTip:"搜索中...",hideOnSelect:w,autoDropup:w,autoMinWidth:w,showHeader:w,showBtn:m,inputBgColor:"",inputWarnColor:"rgba(255,0,0,.1)",listStyle:{"padding-top":0,"max-height":"375px","max-width":"800px",overflow:"auto",width:"auto",transition:"0.3s","-webkit-transition":"0.3s","-moz-transition":"0.3s","-o-transition":"0.3s","word-break":"keep-all","white-space":"nowrap"},listAlign:"left",listHoverStyle:"background: #07d; color:#fff",listHoverCSS:"jhover",clearable:w,keyLeft:37,keyUp:38,keyRight:39,keyDown:40,keyEnter:13,fnProcessData:function(e){return f(e)},fnGetData:function(e,t,n,i){var r,o,a,l,s={value:[]},u=i.fnPreprocessKeyword;if(e=e||"",c.isFunction(u)&&(e=u(e,i)),i.url){var d;i.searchingTip&&(d=setTimeout(function(){H(i.searchingTip,t,t.parent().find("ul"),i)},600)),_(i,e).done(function(e){n(t,i.data,i),t.trigger(g,e),"firstByUrl"===i.getDataMethod&&(i.url=null)}).always(function(){d&&clearTimeout(d)})}else{if(f(r=i.data))if(e){for(l=r.value.length,o=0;o<l;o++)for(a in r.value[o])if(r.value[o][a]&&U(e,a,r.value[o][a]+"",i)){s.value.push(r.value[o]),s.value[s.value.length-1].__index=o;break}}else s=r;n(t,s,i)}},fnAdjustAjaxParam:null,fnPreprocessKeyword:null,fnAjaxFail:null},i={init:function(f){var t=this;return b((f=f||{}).showHeader)&&f.effectiveFields&&1<f.effectiveFields.length&&(f.showHeader=m),(f=c.extend(m,{},n,f)).processData&&(f.fnProcessData=f.processData),f.getData&&(f.fnGetData=f.getData),"firstByUrl"===f.getDataMethod&&f.url&&!f.delayUntilKeyup&&_(f).done(function(e){f.url=null,t.trigger(g,e)}),c("#"+v).length||c("head:eq(0)").append('<style id="'+v+'">.'+f.listHoverCSS+"{"+f.listHoverStyle+"}</style>"),t.each(function(){var e,n,t,i,r,o,a,l=c(this),s=l.parent(),u=(i=f,r=(t=l).prev("i.clearable"),i.clearable&&!r.length&&(r=c('<i class="clearable glyphicon glyphicon-remove fa fa-plus"></i>').prependTo(t.parent())),r.css({position:"absolute",top:"calc(50% - 6px)",transform:"rotate(45deg)",zIndex:4,cursor:"pointer",width:"14px",lineHeight:"14px",textAlign:"center",fontSize:12}).hide()),d=s.find("ul:eq(0)");(d.parent().css("position","relative"),o=l,a=f,!d.length||o.data(v)?w:(o.data(v,{options:a}),m))&&(f.showBtn||(l.css("borderRadius",4),s.css("width","100%").find(".btn:eq(0)").hide()),l.removeClass(y).prop(y,w).attr("autocomplete","off"),d.css(f.listStyle),f.inputBgColor||(f.inputBgColor=l.css("backgroundColor")),l.on("keydown",function(e){var t,n;if(d.is(":visible")){if(t=d.find("."+f.listHoverCSS),n="",F(d,f),e.keyCode===f.keyDown){if(t.length?t.next().length?n=x(t.next().mouseover()):f.autoSelect&&S(l,"").val(k(l)):n=x(d.find("tbody tr:first").mouseover()),A(l,d,f),!f.autoSelect)return}else if(e.keyCode===f.keyUp){if(t.length?t.prev().length?n=x(t.prev().mouseover()):f.autoSelect&&S(l,"").val(k(l)):n=x(d.find("tbody tr:last").mouseover()),A(l,d,f),!f.autoSelect)return}else e.keyCode===f.keyEnter?(n=x(t),W(l,f)):S(l,"");j(l,n,f)}else S(l,"")}).on("compositionstart",function(e){p=m}).on("compositionend",function(e){p=w}).on("keyup input paste",function(e){var t;e.keyCode&&D(l,f),~c.inArray(e.keyCode,[f.keyDown,f.keyUp,f.keyEnter])?l.val(l.val()):(clearTimeout(n),n=setTimeout(function(){p||(t=l.val(),c.trim(t)&&t===k(l)||(k(l,t),f.multiWord&&(t=t.split(f.separator).reverse()[0]),(t.length||f.allowNoKeyword)&&f.fnGetData(c.trim(t),l,B,f)))},f.delay||300))}).on("focus",function(){C(l,d,f)}).on("blur",function(){e||W(l,f)}).on("click",function(){var e=l.val();if(c.trim(e)&&e===k(l)&&d.find("table tr").length)return q(l,f);d.is(":visible")||(f.multiWord&&(e=e.split(f.separator).reverse()[0]),(e.length||f.allowNoKeyword)&&f.fnGetData(c.trim(e),l,B,f))}),s.find(".btn:eq(0)").attr("data-toggle","").click(function(){if(d.is(":visible"))W(l,f);else{if(f.url){if(l.click().focus(),!d.find("tr").length)return w}else B(l,f.data,f);q(l,f)}return w}),d.mouseenter(function(){e=1,l.blur()}).mouseleave(function(){e=0,l.focus()}).on("mouseenter","tbody tr",function(){return F(d,f),c(this).addClass(f.listHoverCSS),w}).on("mousedown","tbody tr",function(){var e=x(c(this));j(l,e,f),k(l,e.key),D(l,f),f.hideOnSelect&&W(l,f)}),u.length&&(u.click(function(){S(l,"").val(""),D(l,f)}),s.mouseenter(function(){l.prop(y)||u.css("right",f.showBtn?Math.max(l.next().width(),33)+2:12).show()}).mouseleave(function(){u.hide()})))})},show:function(){return this.each(function(){c(this).click()})},hide:function(){return this.each(function(){W(c(this))})},disable:function(){return this.each(function(){c(this).attr(y,m).parent().find(".btn:eq(0)").prop(y,m)})},enable:function(){return this.each(function(){c(this).attr(y,w).parent().find(".btn:eq(0)").prop(y,w)})},destroy:function(){return this.each(function(){c(this).off().removeData(v).removeAttr("style").parent().find(".btn:eq(0)").off().show().attr("data-toggle","dropdown").prop(y,w).next().css("display","").off()})},version:function(){return"0.1.27"}};c.fn[v]=function(e){if("string"==typeof e&&i[e]){var t=m;return this.each(function(){if(!c(this).data(v))return t=w}),t||"init"===e||"version"===e?i[e].apply(this,[].slice.call(arguments,1)):this}return i.init.apply(this,arguments)}});
//# sourceMappingURL=bootstrap-suggest.min.js.map