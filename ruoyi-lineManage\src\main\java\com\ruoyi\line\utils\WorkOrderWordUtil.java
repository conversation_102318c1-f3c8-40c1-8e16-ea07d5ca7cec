package com.ruoyi.line.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.util.Units;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.line.domain.WorkOrderExcel;

/**
 * 工单Word生成工具类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public class WorkOrderWordUtil {
    
    private static final Logger log = LoggerFactory.getLogger(WorkOrderWordUtil.class);
    
    /**
     * 生成工单Word文档
     * 
     * @param workOrderList 工单数据列表
     * @param operatorName 运营商名称
     * @param operationType 操作类型名称
     * @param fileName 文件名
     * @return 生成结果
     */
    public static AjaxResult generateWorkOrderWord(List<WorkOrderExcel> workOrderList, 
            String operatorName, String operationType, String fileName) {
        
        return generateWorkOrderWord(workOrderList, operatorName, operationType, fileName, null);
    }
    
    /**
     * 生成工单Word文档（带备注）
     * 
     * @param workOrderList 工单数据列表
     * @param operatorName 运营商名称
     * @param operationType 操作类型名称
     * @param fileName 文件名
     * @param remarks 备注信息
     * @return 生成结果
     */
    public static AjaxResult generateWorkOrderWord(List<WorkOrderExcel> workOrderList, 
            String operatorName, String operationType, String fileName, String remarks) {
        
        XWPFDocument document = null;
        FileOutputStream out = null;
        
        try {
            document = new XWPFDocument();
            
            // 1. 插入银行logo图片
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            
            try {
                // 读取图片文件
                ClassPathResource resource = new ClassPathResource("static/bank-of-china.png");
                java.io.InputStream imageInputStream = resource.getInputStream();
                
                // 插入图片，设置宽度为4英寸，高度按比例缩放
                titleRun.addPicture(imageInputStream, XWPFDocument.PICTURE_TYPE_PNG, "bank-of-china.png", 
                    Units.toEMU(300), Units.toEMU(80)); // 宽度300像素，高度80像素
                
                imageInputStream.close();
            } catch (Exception e) {
                log.warn("插入银行logo失败，使用文字标题: {}", e.getMessage());
                // 如果图片插入失败，使用默认文字
                titleRun.setText("中国银行股份有限公司宁波市分行");
                titleRun.setBold(true);
                titleRun.setFontSize(16);
                titleRun.setFontFamily("宋体");
            }
            

            
            // 2. 主标题
            XWPFParagraph headerParagraph = document.createParagraph();
            headerParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun headerRun = headerParagraph.createRun();
            headerRun.setText("关于通信线路变动的通知");
            headerRun.setBold(true);
            headerRun.setFontSize(14);
            headerRun.setFontFamily("宋体");
            
            // 空行
            document.createParagraph();
            
            // 3. 收件人
            XWPFParagraph receiverParagraph = document.createParagraph();
            XWPFRun receiverRun = receiverParagraph.createRun();
            receiverRun.setText(getReceiverName(operatorName) + "：");
            receiverRun.setFontSize(12);
            receiverRun.setFontFamily("宋体");
            
            // 空行
            document.createParagraph();
            
            // 4. 说明文字
            XWPFParagraph descParagraph = document.createParagraph();
            XWPFRun descRun = descParagraph.createRun();
            descRun.setText("    因业务需要，申请变动以下线路，请贵公司尽快落实。");
            descRun.setFontSize(12);
            descRun.setFontFamily("宋体");
            
            // 空行
            document.createParagraph();
            
            // 5. 创建表格
            XWPFTable table = document.createTable(1, 4);
            
            // 表头
            XWPFTableRow headerRow = table.getRow(0);
            createTableCell(headerRow.getCell(0), "线路编号", true, true);
            createTableCell(headerRow.getCell(1), "名称", true, true);
            createTableCell(headerRow.getCell(2), "类型", true, true);
            createTableCell(headerRow.getCell(3), "地址及联系人", true, true);
            
            // 数据行
            for (WorkOrderExcel workOrder : workOrderList) {
                XWPFTableRow dataRow = table.createRow();
                createTableCell(dataRow.getCell(0), 
                    workOrder.getLineNumber() != null ? workOrder.getLineNumber() : "", 
                    false, true);
                createTableCell(dataRow.getCell(1), 
                    workOrder.getName() != null ? workOrder.getName() : "", 
                    false, true);
                createTableCell(dataRow.getCell(2), 
                    workOrder.getType() != null ? workOrder.getType() : "", 
                    false, true);
                createTableCell(dataRow.getCell(3), 
                    workOrder.getAddressAndContact() != null ? workOrder.getAddressAndContact() : "", 
                    false, false);
            }
            
            // 空行
            document.createParagraph();
            document.createParagraph();
            
            // 6. 备注信息（如果有）
            if (remarks != null && !remarks.trim().isEmpty()) {
                XWPFParagraph remarksParagraph = document.createParagraph();
                XWPFRun remarksRun = remarksParagraph.createRun();
                remarksRun.setText("    备注：" + remarks.trim());
                remarksRun.setFontSize(12);
                remarksRun.setFontFamily("宋体");
                
                // 空行
                document.createParagraph();
            }
            
            // 7. 特此通知
            XWPFParagraph noticeParagraph = document.createParagraph();
            XWPFRun noticeRun = noticeParagraph.createRun();
            noticeRun.setText("    特此通知！");
            noticeRun.setFontSize(12);
            noticeRun.setFontFamily("宋体");
            
            // 空行
            document.createParagraph();
            document.createParagraph();
            
            // 8. 发送单位（右对齐）
            XWPFParagraph senderParagraph = document.createParagraph();
            senderParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun senderRun = senderParagraph.createRun();
            senderRun.setText("中国银行股份有限公司宁波市分行");
            senderRun.setFontSize(12);
            senderRun.setFontFamily("宋体");
            
            // 9. 日期（右对齐）
            XWPFParagraph dateParagraph = document.createParagraph();
            dateParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun dateRun = dateParagraph.createRun();
            String currentDate = new SimpleDateFormat("yyyy年M月d日").format(new Date());
            dateRun.setText(currentDate);
            dateRun.setFontSize(12);
            dateRun.setFontFamily("宋体");
            
            // 保存文件
            String encodedFileName = encodingFilename(fileName);
            String filePath = getAbsoluteFile(encodedFileName);
            out = new FileOutputStream(filePath);
            document.write(out);
            
            return AjaxResult.success(encodedFileName);
            
        } catch (Exception e) {
            log.error("生成工单Word异常: {}", e.getMessage(), e);
            throw new BusinessException("生成工单Word失败，请联系网站管理员！");
        } finally {
            try {
                if (document != null) {
                    document.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                log.error("关闭资源失败: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 创建表格单元格
     */
    private static void createTableCell(XWPFTableCell cell, String text, boolean isBold, boolean isCenter) {
        // 清除现有内容
        cell.removeParagraph(0);
        XWPFParagraph paragraph = cell.addParagraph();
        
        if (isCenter) {
            paragraph.setAlignment(ParagraphAlignment.CENTER);
        }
        
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontSize(11);
        run.setFontFamily("宋体");
        if (isBold) {
            run.setBold(true);
        }
        
        // 设置单元格垂直对齐
        try {
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        } catch (Exception e) {
            // 如果设置垂直对齐失败，忽略错误
        }
    }
    
    /**
     * 根据运营商名称获取收件人名称
     */
    private static String getReceiverName(String operatorName) {
        if ("电信".equals(operatorName)) {
            return "中国电信政企部";
        } else if ("移动".equals(operatorName)) {
            return "中国移动通信集团浙江有限公司宁波分公司";
        } else if ("联通".equals(operatorName)) {
            return "中国联通网络通信有限公司宁波分公司";
        } else if ("华数".equals(operatorName)) {
            return "宁波华数广电网络有限公司";
        } else {
            return operatorName + "政企部";
        }
    }
    
    /**
     * 编码文件名
     */
    private static String encodingFilename(String filename) {
        return UUID.randomUUID().toString() + "_" + filename + ".docx";
    }
    
    /**
     * 获取下载路径
     */
    private static String getAbsoluteFile(String filename) {
        String downloadPath = RuoYiConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }
} 