package com.ruoyi.expense.service;

import java.util.List;
import com.ruoyi.expense.domain.ExpenseDetails;
import com.ruoyi.expense.domain.ExpenseBill;
import javax.servlet.http.HttpServletResponse;

/**
 * 费用明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface IExpenseDetailsService 
{
    /**
     * 查询费用明细
     * 
     * @param id 费用明细ID
     * @return 费用明细
     */
    public ExpenseDetails selectExpenseDetailsById(Long id);

    /**
     * 查询费用明细列表
     * 
     * @param expenseDetails 费用明细
     * @return 费用明细集合
     */
    public List<ExpenseDetails> selectExpenseDetailsList(ExpenseDetails expenseDetails);

    /**
     * 新增费用明细
     * 
     * @param expenseDetails 费用明细
     * @return 结果
     */
    public int insertExpenseDetails(ExpenseDetails expenseDetails);

    /**
     * 修改费用明细
     * 
     * @param expenseDetails 费用明细
     * @return 结果
     */
    public int updateExpenseDetails(ExpenseDetails expenseDetails);

    /**
     * 批量删除费用明细
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteExpenseDetailsByIds(String ids);

    /**
     * 删除费用明细信息
     * 
     * @param id 费用明细ID
     * @return 结果
     */
    public int deleteExpenseDetailsById(Long id);
    
    /**
     * 导出账单关联的费用明细
     *
     * @param billId 账单ID
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    public void exportBillDetails(Long billId, HttpServletResponse response) throws Exception;
}
