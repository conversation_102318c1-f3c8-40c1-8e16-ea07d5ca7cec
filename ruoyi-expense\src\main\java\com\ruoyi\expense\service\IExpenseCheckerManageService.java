package com.ruoyi.expense.service;

import com.ruoyi.expense.domain.ExpenseCheckerManage;
import java.util.List;

public interface IExpenseCheckerManageService {
    
    public List<ExpenseCheckerManage> selectCheckerManageList(ExpenseCheckerManage checkerManage);

    public int insertCheckerManage(ExpenseCheckerManage checkerManage);

    public int deleteCheckerManageByIds(String ids);
    
    /**
     * 根据ID查询核对人信息
     * @param id 核对人ID
     * @return 核对人信息
     */
    public ExpenseCheckerManage selectCheckerManageById(Long id);
    
    /**
     * 更新核对人信息
     * @param checkerManage 核对人信息
     * @return 更新结果
     */
    public int updateCheckerManage(ExpenseCheckerManage checkerManage);
    
    /**
     * 校验用户姓名和EHR号是否存在且匹配
     * @param name 姓名
     * @param ehrNumber EHR号
     * @return 校验结果：0-成功，-3-EHR号不存在，-4-姓名与EHR号不匹配
     */
    public int validateUserInfo(String name, String ehrNumber);
} 