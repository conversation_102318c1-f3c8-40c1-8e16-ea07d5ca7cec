/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t,e){return t(e={exports:{}},e.exports),e.exports}var r=function(t){return t&&t.Math==Math&&t},o=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),c={}.propertyIsEnumerable,a=Object.getOwnPropertyDescriptor,f={f:a&&!c.call({1:2},1)?function(t){var e=a(this,t);return!!e&&e.enumerable}:c},s=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},l={}.toString,p=function(t){return l.call(t).slice(8,-1)},h="".split,y=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?h.call(t,""):Object(t)}:Object,b=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return y(b(t))},d=function(t){return"object"==typeof t?null!==t:"function"==typeof t},g=function(t,e){if(!d(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!d(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!d(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!d(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")},m={}.hasOwnProperty,O=function(t,e){return m.call(t,e)},w=o.document,j=d(w)&&d(w.createElement),S=function(t){return j?w.createElement(t):{}},R=!u&&!i((function(){return 7!=Object.defineProperty(S("div"),"a",{get:function(){return 7}}).a})),T=Object.getOwnPropertyDescriptor,P={f:u?T:function(t,e){if(t=v(t),e=g(e,!0),R)try{return T(t,e)}catch(t){}if(O(t,e))return s(!f.f.call(t,e),t[e])}},A=function(t){if(!d(t))throw TypeError(String(t)+" is not an object");return t},E=Object.defineProperty,x={f:u?E:function(t,e,n){if(A(t),e=g(e,!0),A(n),R)try{return E(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},_=u?function(t,e,n){return x.f(t,e,s(1,n))}:function(t,e,n){return t[e]=n,t},I=function(t,e){try{_(o,t,e)}catch(n){o[t]=e}return e},k="__core-js_shared__",F=o[k]||I(k,{}),M=Function.toString;"function"!=typeof F.inspectSource&&(F.inspectSource=function(t){return M.call(t)});var C,q,B,L=F.inspectSource,N=o.WeakMap,z="function"==typeof N&&/native code/.test(L(N)),D=n((function(t){(t.exports=function(t,e){return F[t]||(F[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),W=0,G=Math.random(),$=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++W+G).toString(36)},K=D("keys"),Q=function(t){return K[t]||(K[t]=$(t))},V={},X=o.WeakMap;if(z){var Y=new X,H=Y.get,J=Y.has,U=Y.set;C=function(t,e){return U.call(Y,t,e),e},q=function(t){return H.call(Y,t)||{}},B=function(t){return J.call(Y,t)}}else{var Z=Q("state");V[Z]=!0,C=function(t,e){return _(t,Z,e),e},q=function(t){return O(t,Z)?t[Z]:{}},B=function(t){return O(t,Z)}}var tt,et,nt={set:C,get:q,has:B,enforce:function(t){return B(t)?q(t):C(t,{})},getterFor:function(t){return function(e){var n;if(!d(e)||(n=q(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}}},rt=n((function(t){var e=nt.get,n=nt.enforce,r=String(String).split("String");(t.exports=function(t,e,i,u){var c=!!u&&!!u.unsafe,a=!!u&&!!u.enumerable,f=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof e||O(i,"name")||_(i,"name",e),n(i).source=r.join("string"==typeof e?e:"")),t!==o?(c?!f&&t[e]&&(a=!0):delete t[e],a?t[e]=i:_(t,e,i)):a?t[e]=i:I(e,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||L(this)}))})),ot=o,it=function(t){return"function"==typeof t?t:void 0},ut=function(t,e){return arguments.length<2?it(ot[t])||it(o[t]):ot[t]&&ot[t][e]||o[t]&&o[t][e]},ct=Math.ceil,at=Math.floor,ft=function(t){return isNaN(t=+t)?0:(t>0?at:ct)(t)},st=Math.min,lt=function(t){return t>0?st(ft(t),9007199254740991):0},pt=Math.max,ht=Math.min,yt=function(t){return function(e,n,r){var o,i=v(e),u=lt(i.length),c=function(t,e){var n=ft(t);return n<0?pt(n+e,0):ht(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},bt={includes:yt(!0),indexOf:yt(!1)}.indexOf,vt=function(t,e){var n,r=v(t),o=0,i=[];for(n in r)!O(V,n)&&O(r,n)&&i.push(n);for(;e.length>o;)O(r,n=e[o++])&&(~bt(i,n)||i.push(n));return i},dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],gt=dt.concat("length","prototype"),mt={f:Object.getOwnPropertyNames||function(t){return vt(t,gt)}},Ot={f:Object.getOwnPropertySymbols},wt=ut("Reflect","ownKeys")||function(t){var e=mt.f(A(t)),n=Ot.f;return n?e.concat(n(t)):e},jt=function(t,e){for(var n=wt(e),r=x.f,o=P.f,i=0;i<n.length;i++){var u=n[i];O(t,u)||r(t,u,o(e,u))}},St=/#|\.prototype\./,Rt=function(t,e){var n=Pt[Tt(t)];return n==Et||n!=At&&("function"==typeof e?i(e):!!e)},Tt=Rt.normalize=function(t){return String(t).replace(St,".").toLowerCase()},Pt=Rt.data={},At=Rt.NATIVE="N",Et=Rt.POLYFILL="P",xt=Rt,_t=P.f,It=function(t,e){var n,r,i,u,c,a=t.target,f=t.global,s=t.stat;if(n=f?o:s?o[a]||I(a,{}):(o[a]||{}).prototype)for(r in e){if(u=e[r],i=t.noTargetGet?(c=_t(n,r))&&c.value:n[r],!xt(f?r:a+(s?".":"#")+r,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;jt(u,i)}(t.sham||i&&i.sham)&&_(u,"sham",!0),rt(n,r,u,t)}},kt=Array.isArray||function(t){return"Array"==p(t)},Ft=function(t){return Object(b(t))},Mt=function(t,e,n){var r=g(e);r in t?x.f(t,r,s(0,n)):t[r]=n},Ct=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),qt=Ct&&!Symbol.sham&&"symbol"==typeof Symbol(),Bt=D("wks"),Lt=o.Symbol,Nt=qt?Lt:$,zt=function(t){return O(Bt,t)||(Ct&&O(Lt,t)?Bt[t]=Lt[t]:Bt[t]=Nt("Symbol."+t)),Bt[t]},Dt=zt("species"),Wt=function(t,e){var n;return kt(t)&&("function"!=typeof(n=t.constructor)||n!==Array&&!kt(n.prototype)?d(n)&&null===(n=n[Dt])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===e?0:e)},Gt=ut("navigator","userAgent")||"",$t=o.process,Kt=$t&&$t.versions,Qt=Kt&&Kt.v8;Qt?et=(tt=Qt.split("."))[0]+tt[1]:Gt&&(!(tt=Gt.match(/Edge\/(\d+)/))||tt[1]>=74)&&(tt=Gt.match(/Chrome\/(\d+)/))&&(et=tt[1]);var Vt,Xt=et&&+et,Yt=zt("species"),Ht=zt("isConcatSpreadable"),Jt=9007199254740991,Ut="Maximum allowed index exceeded",Zt=Xt>=51||!i((function(){var t=[];return t[Ht]=!1,t.concat()[0]!==t})),te=(Vt="concat",Xt>=51||!i((function(){var t=[];return(t.constructor={})[Yt]=function(){return{foo:1}},1!==t[Vt](Boolean).foo}))),ee=function(t){if(!d(t))return!1;var e=t[Ht];return void 0!==e?!!e:kt(t)};It({target:"Array",proto:!0,forced:!Zt||!te},{concat:function(t){var e,n,r,o,i,u=Ft(this),c=Wt(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(ee(i=-1===e?u:arguments[e])){if(a+(o=lt(i.length))>Jt)throw TypeError(Ut);for(n=0;n<o;n++,a++)n in i&&Mt(c,a,i[n])}else{if(a>=Jt)throw TypeError(Ut);Mt(c,a++,i)}return c.length=a,c}});var ne,re=function(t,e,n){if(function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function")}(t),void 0===e)return t;switch(n){case 0:return function(){return t.call(e)};case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,o){return t.call(e,n,r,o)}}return function(){return t.apply(e,arguments)}},oe=[].push,ie=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=5==t||i;return function(c,a,f,s){for(var l,p,h=Ft(c),b=y(h),v=re(a,f,3),d=lt(b.length),g=0,m=s||Wt,O=e?m(c,d):n?m(c,0):void 0;d>g;g++)if((u||g in b)&&(p=v(l=b[g],g,h),t))if(e)O[g]=p;else if(p)switch(t){case 3:return!0;case 5:return l;case 6:return g;case 2:oe.call(O,l)}else if(o)return!1;return i?-1:r||o?o:O}},ue={forEach:ie(0),map:ie(1),filter:ie(2),some:ie(3),every:ie(4),find:ie(5),findIndex:ie(6)},ce=Object.keys||function(t){return vt(t,dt)},ae=u?Object.defineProperties:function(t,e){A(t);for(var n,r=ce(e),o=r.length,i=0;o>i;)x.f(t,n=r[i++],e[n]);return t},fe=ut("document","documentElement"),se=Q("IE_PROTO"),le=function(){},pe=function(t){return"<script>"+t+"</"+"script>"},he=function(){try{ne=document.domain&&new ActiveXObject("htmlfile")}catch(t){}var t,e;he=ne?function(t){t.write(pe("")),t.close();var e=t.parentWindow.Object;return t=null,e}(ne):((e=S("iframe")).style.display="none",fe.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(pe("document.F=Object")),t.close(),t.F);for(var n=dt.length;n--;)delete he.prototype[dt[n]];return he()};V[se]=!0;var ye=Object.create||function(t,e){var n;return null!==t?(le.prototype=A(t),n=new le,le.prototype=null,n[se]=t):n=he(),void 0===e?n:ae(n,e)},be=zt("unscopables"),ve=Array.prototype;null==ve[be]&&x.f(ve,be,{configurable:!0,value:ye(null)});var de,ge=ue.find,me="find",Oe=!0;me in[]&&Array(1).find((function(){Oe=!1})),It({target:"Array",proto:!0,forced:Oe},{find:function(t){return ge(this,t,arguments.length>1?arguments[1]:void 0)}}),de=me,ve[be][de]=!0;var we=Object.assign,je=Object.defineProperty,Se=!we||i((function(){if(u&&1!==we({b:1},we(je({},"a",{enumerable:!0,get:function(){je(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=we({},t)[n]||ce(we({},e)).join("")!=r}))?function(t,e){for(var n=Ft(t),r=arguments.length,o=1,i=Ot.f,c=f.f;r>o;)for(var a,s=y(arguments[o++]),l=i?ce(s).concat(i(s)):ce(s),p=l.length,h=0;p>h;)a=l[h++],u&&!c.call(s,a)||(n[a]=s[a]);return n}:we;function Re(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Te(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function Pe(t){return(Pe=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Ae(t,e){return(Ae=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function Ee(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function xe(t,e,n){return(xe="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Pe(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(n):o.value}})(t,e,n||t)}It({target:"Object",stat:!0,forced:Object.assign!==Se},{assign:Se});var _e=t.fn.bootstrapTable.utils;t.extend(t.fn.bootstrapTable.defaults,{autoRefresh:!1,autoRefreshInterval:60,autoRefreshSilent:!0,autoRefreshStatus:!0,autoRefreshFunction:null}),t.extend(t.fn.bootstrapTable.defaults.icons,{autoRefresh:{bootstrap3:"glyphicon-time icon-time",materialize:"access_time","bootstrap-table":"icon-clock"}[t.fn.bootstrapTable.theme]||"fa-clock"}),t.extend(t.fn.bootstrapTable.locales,{formatAutoRefresh:function(){return"Auto Refresh"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales),t.BootstrapTable=function(t){function e(){return Re(this,e),Ee(this,Pe(e).apply(this,arguments))}var n,r,o;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&Ae(t,e)}(e,t),n=e,(r=[{key:"init",value:function(){for(var t,n=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];(t=xe(Pe(e.prototype),"init",this)).call.apply(t,[this].concat(o)),this.options.autoRefresh&&this.options.autoRefreshStatus&&(this.options.autoRefreshFunction=setInterval((function(){n.refresh({silent:n.options.autoRefreshSilent})}),1e3*this.options.autoRefreshInterval))}},{key:"initToolbar",value:function(){var t;this.options.autoRefresh&&(this.buttons=Object.assign(this.buttons,{autoRefresh:{html:'\n            <button class="auto-refresh '.concat(this.constants.buttonsClass,"\n              ").concat(this.options.autoRefreshStatus?" ".concat(this.constants.classes.buttonActive):"",'"\n              type="button" name="autoRefresh" title="').concat(this.options.formatAutoRefresh(),'">\n              ').concat(this.options.showButtonIcons?_e.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.autoRefresh):"","\n              ").concat(this.options.showButtonText?this.options.formatAutoRefresh():"","\n            </button>\n           "),event:this.toggleAutoRefresh}}));for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];(t=xe(Pe(e.prototype),"initToolbar",this)).call.apply(t,[this].concat(r))}},{key:"toggleAutoRefresh",value:function(){var t=this;this.options.autoRefresh&&(this.options.autoRefreshStatus?(clearInterval(this.options.autoRefreshFunction),this.$toolbar.find(">.columns .auto-refresh").removeClass(this.constants.classes.buttonActive)):(this.options.autoRefreshFunction=setInterval((function(){t.refresh({silent:t.options.autoRefreshSilent})}),1e3*this.options.autoRefreshInterval),this.$toolbar.find(">.columns .auto-refresh").addClass(this.constants.classes.buttonActive)),this.options.autoRefreshStatus=!this.options.autoRefreshStatus)}}])&&Te(n.prototype,r),o&&Te(n,o),e}(t.BootstrapTable)}));