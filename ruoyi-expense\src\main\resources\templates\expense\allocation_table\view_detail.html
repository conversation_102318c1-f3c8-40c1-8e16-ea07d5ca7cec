<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('分摊表详情')" />
    <link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>分摊表基本信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>分摊表名称：</label>
                                    <span th:text="${allocationTable.tableName}"></span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>状态：</label>
                                    <span th:if="${allocationTable.status == '审核中'}" class="badge badge-warning">审核中</span>
                                    <span th:if="${allocationTable.status == '已审核'}" class="badge badge-success">已审核</span>
                                    <span th:if="${allocationTable.status == '已拒绝'}" class="badge badge-danger">已拒绝</span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>费用类型：</label>
                                    <span th:text="${allocationTable.expenseType}"></span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>计费周期：</label>
                                    <span th:text="${allocationTable.billingCycle}"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label>制表人：</label>
                                    <span th:text="${allocationTable.preparer}"></span>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label>复核人：</label>
                                    <span th:text="${allocationTable.reviewer}"></span>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label>负责人：</label>
                                    <span th:text="${allocationTable.responsiblePerson ?: ''}"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${allocationTable.comments != null && allocationTable.comments != ''}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label>修改意见：</label>
                                    <p class="form-control-static" th:text="${allocationTable.comments}"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>创建时间：</label>
                                    <span th:text="${#dates.format(allocationTable.createTime, 'yyyy-MM-dd HH:mm:ss')}"></span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>确认时间：</label>
                                    <span th:text="${allocationTable.confirmTime != null ? #dates.format(allocationTable.confirmTime, 'yyyy-MM-dd HH:mm:ss') : '未确认'}"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>分摊表详细内容</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-sm" id="exportBtn">
                                <i class="fa fa-download"></i> 导出文件
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div id="allocationContent">
                            <p class="text-center">正在加载分摊表数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        /*<![CDATA[*/
        var allocationTable = /*[[${allocationTable}]]*/ {};
        var ctx = /*[[${#httpServletRequest.getContextPath()}]]*/ '';
        /*]]>*/

        $(function() {
            // 加载分摊表详细内容
            loadAllocationContent();
            
            // 导出按钮点击事件
            $("#exportBtn").click(function() {
                window.location.href = ctx + "/expense/allocation_table/exportDetail/" + allocationTable.id;
            });
        });

        function loadAllocationContent() {
            // 请求分摊表数据
            $.post(ctx + "/expense/allocation_table/getAllocationData", {
                expenseType: allocationTable.expenseType,
                billingCycle: allocationTable.billingCycle
            }, function(result) {
                if (result.code == 0 && result.data && result.data.length > 0) {
                    // 创建分摊表展示
                    createAllocationTable(result.data);
                } else {
                    $("#allocationContent").html('<p class="text-center text-muted">暂无相关账单数据</p>');
                }
            }).fail(function() {
                $("#allocationContent").html('<p class="text-center text-danger">加载分摊表数据失败</p>');
            });
        }

        function createAllocationTable(bills) {
            // 创建分摊表展示
            var tableHtml = '<div class="table-responsive">';
            tableHtml += '<table class="table table-bordered table-striped">';
            tableHtml += '<thead>';
            tableHtml += '<tr>';
            tableHtml += '<th>序号</th>';
            tableHtml += '<th>管辖行/部门</th>';
            tableHtml += '<th>分摊内容</th>';
            tableHtml += '<th>入账核算码</th>';
            tableHtml += '<th>金额（不含税）</th>';
            tableHtml += '</tr>';
            tableHtml += '</thead>';
            tableHtml += '<tbody id="allocationTableBody">';
            tableHtml += '</tbody>';
            tableHtml += '</table>';
            tableHtml += '</div>';
            
            $("#allocationContent").html(tableHtml);
            
            // 填充分摊表数据
            fillAllocationTableData(bills);
        }

        function fillAllocationTableData(bills) {
            // 获取入账核算码字典映射
            var expenseCodeMap = {};
            
            // 先获取字典数据
            $.post(ctx + "/expense/bill_manage/getDictData", {
                dictType: "expense-code"
            }, function(result) {
                if (result.code === 0 && result.data) {
                    $.each(result.data, function(index, item) {
                        // 根据费用类型（dictValue）获取入账核算码（dictLabel）
                        expenseCodeMap[item.dictValue] = item.dictLabel;
                    });
                }
                
                // 填充表格数据
                var tbody = '';
                var totalAmount = 0;
                
                $.each(bills, function(i, bill) {
                    var serialNum = i + 1;
                    var transferDepartment = bill.transfer_department || '';
                    var expenseType = bill.expense_type || '';
                    var expenseCode = expenseCodeMap[expenseType] || '6576'; // 默认值
                    var amount = bill.total_price_without_tax || 0;
                    
                    if (amount) {
                        totalAmount += parseFloat(amount);
                    }
                    
                    tbody += '<tr>';
                    tbody += '<td>' + serialNum + '</td>';
                    tbody += '<td>' + transferDepartment + '</td>';
                    tbody += '<td>' + expenseType + '</td>';
                    tbody += '<td>' + expenseCode + '</td>';
                    tbody += '<td>' + formatAmount(amount) + '</td>';
                    tbody += '</tr>';
                });
                
                // 添加合计行
                tbody += '<tr class="table-info">';
                tbody += '<td colspan="4" class="text-center"><strong>合计</strong></td>';
                tbody += '<td><strong>' + formatAmount(totalAmount) + '</strong></td>';
                tbody += '</tr>';
                
                $("#allocationTableBody").html(tbody);
            }).fail(function() {
                // 如果获取字典失败，使用默认值
                var tbody = '';
                var totalAmount = 0;
                
                $.each(bills, function(i, bill) {
                    var serialNum = i + 1;
                    var transferDepartment = bill.transfer_department || '';
                    var expenseType = bill.expense_type || '';
                    var amount = bill.total_price_without_tax || 0;
                    
                    if (amount) {
                        totalAmount += parseFloat(amount);
                    }
                    
                    tbody += '<tr>';
                    tbody += '<td>' + serialNum + '</td>';
                    tbody += '<td>' + transferDepartment + '</td>';
                    tbody += '<td>' + expenseType + '</td>';
                    tbody += '<td>6576</td>'; // 默认入账核算码
                    tbody += '<td>' + formatAmount(amount) + '</td>';
                    tbody += '</tr>';
                });
                
                // 添加合计行
                tbody += '<tr class="table-info">';
                tbody += '<td colspan="4" class="text-center"><strong>合计</strong></td>';
                tbody += '<td><strong>' + formatAmount(totalAmount) + '</strong></td>';
                tbody += '</tr>';
                
                $("#allocationTableBody").html(tbody);
            });
        }

        function formatAmount(amount) {
            if (!amount || amount == 0) {
                return '0.00';
            }
            return parseFloat(amount).toFixed(2);
        }
    </script>
</body>
</html> 