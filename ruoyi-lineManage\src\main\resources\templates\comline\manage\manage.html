<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('通讯线路管理列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>线路编号：</label>
                            <input type="text" name="lineNumber"/>
                        </li>
                        <li>
                            <label>所属机构：</label>
                            <select name="institution" th:with="type=${@dict.getType('line_institude')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>运营商：</label>
                            <select name="operator" th:with="type=${@dict.getType('line_operator')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>线路名称：</label>
                            <input type="text" name="lineName"/>
                        </li>
                        <li>
                            <label>线路类型：</label>
                            <select name="lineType" th:with="type=${@dict.getType('lineType')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>线路带宽：</label>
                            <input type="text" name="lineBandwidth"/>
                        </li>
                        <li>
                            <label>线路状态：</label>
                            <select name="lineStatus" th:with="type=${@dict.getType('lineStatus')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>地址：</label>
                            <input type="text" name="address"/>
                        </li>
                        <li>
                            <label>含税价：</label>
                            <input type="text" name="taxIncludingPrice"/>
                        </li>
                        <li>
                            <label>不含税价：</label>
                            <input type="text" name="taxExcludingPrice"/>
                        </li>
                        <li>
                            <label>对端联系人：</label>
                            <input type="text" name="oppositeContact"/>
                        </li>
                        <li>
                            <label>线路使用环境：</label>
                            <input type="text" name="lineUsageEnvironment"/>
                             </select>
                        </li>
                        <li>
                            <label>线路新增日期：</label>
                            <input type="text" class="time-input" placeholder="请选择线路新增日期" name="lineAddDate"/>
                        </li>
                        <li>
                            <label>线路撤销日期：</label>
                            <input type="text" class="time-input" placeholder="请选择线路撤销日期" name="lineCancelDate"/>
                        </li>
                        <li>
                            <label>税率：</label>
                            <input type="text" name="taxRate"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="comline:manage:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="comline:manage:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="comline:manage:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasAnyRoles="admin,line_manager">
                <i class="fa fa-download"></i> 导出原始数据
            </a>
            <a class="btn btn-info" onclick="exportVisualData()" >
                <i class="fa fa-download"></i> 导出可视化数据
            </a>
            <a class="btn btn-primary multiple disabled" onclick="$.operate.calculate_exTAX()" shiro:hasAnyRoles="admin,line_manager">
                <i class="fa fa-edit"></i> 计算不含税价
            </a>
            <a class="btn btn-info" onclick="billPush()" shiro:hasAnyRoles="admin,line_manager">
                <i class="fa fa-paper-plane"></i> 账单推送
            </a>
            <a class="btn btn-success" onclick="workOrderGenerate()" shiro:hasAnyRoles="admin,line_manager">
                <i class="fa fa-file-text-o"></i> 生成工单
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('comline:manage:edit')}]];
    var removeFlag = [[${@permission.hasPermi('comline:manage:remove')}]];
    var institutionDatas = [[${@dict.getType('line_institude')}]];
    var operatorDatas = [[${@dict.getType('line_operator')}]];
    var lineTypeDatas = [[${@dict.getType('lineType')}]];
    var lineStatusDatas = [[${@dict.getType('lineStatus')}]];
    var prefix = ctx + "comline/manage";

    $(function() {
        var options = {
            sortName:"lineStatus",
            sortOrder: "asc",
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            calculate_exTAXUrl:prefix + "/calculate_exTAX",
            modalName: "通讯线路管理",
            rowStyle:rowStyle,
            columns: [{
                checkbox: true
            },
                {
                    field: 'lineCode',
                    title: '线路代码',
                    visible: false
                },
                {
                    field: 'lineNumber',
                    title: '线路编号'
                },
                {
                    field: 'institution',
                    title: '所属机构',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(institutionDatas, value);
                    },
                },
                {
                    field: 'operator',
                    title: '运营商',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(operatorDatas, value);
                    },
                },
                {
                    field: 'lineName',
                    title: '线路名称'
                },
                {
                    field: 'lineType',
                    title: '线路类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(lineTypeDatas, value);
                    }
                },
                {
                    field: 'lineBandwidth',
                    title: '线路带宽'
                },
                {
                    field: 'lineStatus',
                    title: '线路状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(lineStatusDatas, value);
                    }
                },
                {
                    field: 'address',
                    title: '地址'
                },
                {
                    field: 'taxIncludingPrice',
                    title: '含税价'
                },
                {
                    field: 'taxExcludingPrice',
                    title: '不含税价'
                },
                {
                    field: 'oppositeContact',
                    title: '对端联系人'
                },
                {
                    field: 'lineUsageEnvironment',
                    title: '线路使用环境'
                },
                {
                    field: 'lineAddDate',
                    title: '线路新增日期'
                },
                {
                    field: 'lineCancelDate',
                    title: '线路撤销日期'
                },
                {
                    field: 'taxRate',
                    title: '税率'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.lineCode + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.lineCode + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
    function rowStyle(row, index){
        var line_state = row.lineStatus;
        if (line_state == '2')
        {
            return {css:{'background-color':'rgb(253,244,200)'}};  //自定义浅黄√

        }
        else if(line_state == '3'){
            return {css:{'background-color':'rgb(255,195,195)'}};  //自定义浅红√
        }
        else{
            // return {css:{'background-color':'rgb(253,244,200)'}};  //自定义浅黄√
            return {};
        }
    };
    
    /* 打开账单推送页面 */
    function billPush() {
        var url = prefix + "/bill_push";
        $.modal.open("账单推送", url);
    }
    
    /* 导出可视化数据 */
    function exportVisualData() {
        $.modal.confirm("确认导出所有通讯线路可视化数据吗？", function() {
            var currentId = 'formId';
            var params = $("#bootstrap-table").bootstrapTable('getOptions');
            var dataParam = $("#" + currentId).serializeArray();
            dataParam.push({ "name": "orderByColumn", "value": params.sortName });
            dataParam.push({ "name": "isAsc", "value": params.sortOrder });
            $.modal.loading("正在导出数据，请稍候...");
            $.post(prefix + "/exportVisual", dataParam, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }
    
    /* 生成工单 */
    function workOrderGenerate() {
        var rows = $.table.selectColumns("lineCode");
        var validRows = [];
        var operatorName = "";
        
        // 如果有选中记录，则处理选中的记录
        if (rows.length > 0) {
            // 过滤掉空值和无效ID
            validRows = rows.filter(function(id) {
                return id !== null && id !== undefined && id !== '' && !isNaN(id) && id > 0;
            });
            
            // 获取第一个选中线路的运营商信息
            var selectedRows = $("#bootstrap-table").bootstrapTable('getSelections');
            if (selectedRows.length > 0) {
                operatorName = $.table.selectDictLabel(operatorDatas, selectedRows[0].operator);
            }
        }
        
        // 将线路ID存储到sessionStorage（可能为空数组）
        sessionStorage.setItem('selectedLineIds', JSON.stringify(validRows));
        sessionStorage.setItem('selectedOperatorName', operatorName);
        
        // 打开工单生成页面
        var url = prefix + "/work_order";
        $.modal.open("生成运营商工单", url, '900', '700');
    }
</script>
</body>
</html>