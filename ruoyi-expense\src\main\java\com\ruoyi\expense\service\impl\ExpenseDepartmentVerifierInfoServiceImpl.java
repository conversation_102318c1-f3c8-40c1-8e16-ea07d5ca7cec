package com.ruoyi.expense.service.impl;

import com.ruoyi.expense.domain.ExpenseDepartmentVerifierInfo;
import com.ruoyi.expense.mapper.ExpenseDepartmentVerifierInfoMapper;
import com.ruoyi.expense.service.IExpenseDepartmentVerifierInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 部门账单核对人服务实现
 */
@Service
public class ExpenseDepartmentVerifierInfoServiceImpl implements IExpenseDepartmentVerifierInfoService {

    @Autowired
    private ExpenseDepartmentVerifierInfoMapper verifierInfoMapper;

    /**
     * 查询核对人列表
     */
    @Override
    public List<ExpenseDepartmentVerifierInfo> selectVerifierInfoList(ExpenseDepartmentVerifierInfo verifierInfo) {
        return verifierInfoMapper.selectVerifierInfoList(verifierInfo);
    }

    /**
     * 通过ID查询单条数据
     */
    @Override
    public ExpenseDepartmentVerifierInfo selectVerifierInfoById(Integer id) {
        return verifierInfoMapper.selectVerifierInfoById(id);
    }

    /**
     * 根据核对人姓名查询信息
     */
    @Override
    public ExpenseDepartmentVerifierInfo selectVerifierInfoByName(String name) {
        return verifierInfoMapper.selectVerifierInfoByName(name);
    }

    /**
     * 根据部门名称查询信息
     */
    @Override
    public List<ExpenseDepartmentVerifierInfo> selectVerifierInfoByDepartmentName(String departmentName) {
        return verifierInfoMapper.selectVerifierInfoByDepartmentName(departmentName);
    }

    /**
     * 根据EHR编号查询信息
     */
    @Override
    public ExpenseDepartmentVerifierInfo selectVerifierInfoByEhrNumber(String ehrNumber) {
        return verifierInfoMapper.selectVerifierInfoByEhrNumber(ehrNumber);
    }
} 