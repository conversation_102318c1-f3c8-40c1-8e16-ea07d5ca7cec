/**
 * <AUTHOR> wen <<EMAIL>>
 * version: 1.18.0
 * https://github.com/wenzhixin/bootstrap-table/
 */
function getRememberRowIds(t,e){return $.isArray(t)?props=$.map(t,function(t){return t[e]}):props=[t[e]],props}function addRememberRow(t,e){var i=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId,n=getRememberRowIds(t,i);-1==$.inArray(e[i],n)&&(t[t.length]=e)}function removeRememberRow(t,e){var i=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId,n=getRememberRowIds(t,i),o=$.inArray(e[i],n);-1!=o&&t.splice(o,1)}!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):(t=t||self,t.BootstrapTable=e(t.jQuery))}(this,function(t){function e(t,e){return e={exports:{}},t(e,e.exports),e.exports}function i(t,e){return RegExp(t,e)}function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}function r(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value" in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function a(t,e,i){return e&&r(t.prototype,e),i&&r(t,i),t}function s(t,e){return h(t)||d(t,e)||f()}function l(t){return c(t)||u(t)||p()}function c(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++){i[e]=t[e]}return i}}function h(t){return Array.isArray(t)?t:void 0}function u(t){return Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t)?Array.from(t):void 0}function d(t,e){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t)){var i=[],n=!0,o=!1,r=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done)&&(i.push(a.value),!e||i.length!==e);n=!0){}}catch(l){o=!0,r=l}finally{try{n||null==s["return"]||s["return"]()}finally{if(o){throw r}}}return i}}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t["default"]:t;var g="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},v=function(t){return t&&t.Math==Math&&t},b=v("object"==typeof globalThis&&globalThis)||v("object"==typeof window&&window)||v("object"==typeof self&&self)||v("object"==typeof g&&g)||Function("return this")(),m=function(t){try{return !!t()}catch(e){return !0}},y=!m(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),w={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,x=S&&!w.call({1:2},1),k=x?function(t){var e=S(this,t);return !!e&&e.enumerable}:w,O={f:k},C=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},P={}.toString,T=function(t){return P.call(t).slice(8,-1)},I="".split,A=m(function(){return !Object("z").propertyIsEnumerable(0)})?function(t){return"String"==T(t)?I.call(t,""):Object(t)}:Object,$=function(t){if(void 0==t){throw TypeError("Can't call method on "+t)}return t},R=function(t){return A($(t))},E=function(t){return"object"==typeof t?null!==t:"function"==typeof t},j=function(t,e){if(!E(t)){return t}var i,n;if(e&&"function"==typeof(i=t.toString)&&!E(n=i.call(t))){return n}if("function"==typeof(i=t.valueOf)&&!E(n=i.call(t))){return n}if(!e&&"function"==typeof(i=t.toString)&&!E(n=i.call(t))){return n}throw TypeError("Can't convert object to primitive value")},_={}.hasOwnProperty,N=function(t,e){return _.call(t,e)},F=b.document,V=E(F)&&E(F.createElement),D=function(t){return V?F.createElement(t):{}},B=!y&&!m(function(){return 7!=Object.defineProperty(D("div"),"a",{get:function(){return 7}}).a}),L=Object.getOwnPropertyDescriptor,H=y?L:function(t,e){if(t=R(t),e=j(e,!0),B){try{return L(t,e)}catch(i){}}return N(t,e)?C(!O.f.call(t,e),t[e]):void 0},M={f:H},U=function(t){if(!E(t)){throw TypeError(t+" is not an object")}return t},q=Object.defineProperty,z=y?q:function(t,e,i){if(U(t),e=j(e,!0),U(i),B){try{return q(t,e,i)}catch(n){}}if("get" in i||"set" in i){throw TypeError("Accessors not supported")}return"value" in i&&(t[e]=i.value),t},W={f:z},G=y?function(t,e,i){return W.f(t,e,C(1,i))}:function(t,e,i){return t[e]=i,t},K=function(t,e){try{G(b,t,e)}catch(i){b[t]=e}return e},Y="__core-js_shared__",J=b[Y]||K(Y,{}),X=J,Q=Function.toString;"function"!=typeof X.inspectSource&&(X.inspectSource=function(t){return Q.call(t)});var Z,tt,et,it=X.inspectSource,nt=b.WeakMap,ot="function"==typeof nt&&/native code/.test(it(nt)),rt=e(function(t){(t.exports=function(t,e){return X[t]||(X[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})}),at=0,st=Math.random(),lt=function(t){return"Symbol("+((void 0===t?"":t)+"")+")_"+(++at+st).toString(36)},ct=rt("keys"),ht=function(t){return ct[t]||(ct[t]=lt(t))},ut={},dt=b.WeakMap,pt=function(t){return et(t)?tt(t):Z(t,{})},ft=function(t){return function(e){var i;if(!E(e)||(i=tt(e)).type!==t){throw TypeError("Incompatible receiver, "+t+" required")}return i}};if(ot){var gt=new dt,vt=gt.get,bt=gt.has,mt=gt.set;Z=function(t,e){return mt.call(gt,t,e),e},tt=function(t){return vt.call(gt,t)||{}},et=function(t){return bt.call(gt,t)}}else{var yt=ht("state");ut[yt]=!0,Z=function(t,e){return G(t,yt,e),e},tt=function(t){return N(t,yt)?t[yt]:{}},et=function(t){return N(t,yt)}}var wt,St={set:Z,get:tt,has:et,enforce:pt,getterFor:ft},xt=e(function(t){var e=St.get,i=St.enforce,n=(String+"").split("String");(t.exports=function(t,e,o,r){var a=r?!!r.unsafe:!1,s=r?!!r.enumerable:!1,l=r?!!r.noTargetGet:!1;return"function"==typeof o&&("string"!=typeof e||N(o,"name")||G(o,"name",e),i(o).source=n.join("string"==typeof e?e:"")),t===b?void (s?t[e]=o:K(e,o)):(a?!l&&t[e]&&(s=!0):delete t[e],void (s?t[e]=o:G(t,e,o)))})(Function.prototype,"toString",function(){return"function"==typeof this&&e(this).source||it(this)})}),kt=b,Ot=function(t){return"function"==typeof t?t:void 0},Ct=function(t,e){return arguments.length<2?Ot(kt[t])||Ot(b[t]):kt[t]&&kt[t][e]||b[t]&&b[t][e]},Pt=Math.ceil,Tt=Math.floor,It=function(t){return isNaN(t=+t)?0:(t>0?Tt:Pt)(t)},At=Math.min,$t=function(t){return t>0?At(It(t),9007199254740991):0},Rt=Math.max,Et=Math.min,jt=function(t,e){var i=It(t);return 0>i?Rt(i+e,0):Et(i,e)},_t=function(t){return function(e,i,n){var o,r=R(e),a=$t(r.length),s=jt(n,a);if(t&&i!=i){for(;a>s;){if(o=r[s++],o!=o){return !0}}}else{for(;a>s;s++){if((t||s in r)&&r[s]===i){return t||s||0}}}return !t&&-1}},Nt={includes:_t(!0),indexOf:_t(!1)},Ft=Nt.indexOf,Vt=function(t,e){var i,n=R(t),o=0,r=[];for(i in n){!N(ut,i)&&N(n,i)&&r.push(i)}for(;e.length>o;){N(n,i=e[o++])&&(~Ft(r,i)||r.push(i))}return r},Dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Bt=Dt.concat("length","prototype"),Lt=Object.getOwnPropertyNames||function(t){return Vt(t,Bt)},Ht={f:Lt},Mt=Object.getOwnPropertySymbols,Ut={f:Mt},qt=Ct("Reflect","ownKeys")||function(t){var e=Ht.f(U(t)),i=Ut.f;return i?e.concat(i(t)):e},zt=function(t,e){for(var i=qt(e),n=W.f,o=M.f,r=0;r<i.length;r++){var a=i[r];N(t,a)||n(t,a,o(e,a))}},Wt=/#|\.prototype\./,Gt=function(t,e){var i=Yt[Kt(t)];return i==Xt?!0:i==Jt?!1:"function"==typeof e?m(e):!!e},Kt=Gt.normalize=function(t){return(t+"").replace(Wt,".").toLowerCase()},Yt=Gt.data={},Jt=Gt.NATIVE="N",Xt=Gt.POLYFILL="P",Qt=Gt,Zt=M.f,te=function(t,e){var i,n,o,r,a,s,l=t.target,c=t.global,h=t.stat;if(n=c?b:h?b[l]||K(l,{}):(b[l]||{}).prototype){for(o in e){if(a=e[o],t.noTargetGet?(s=Zt(n,o),r=s&&s.value):r=n[o],i=Qt(c?o:l+(h?".":"#")+o,t.forced),!i&&void 0!==r){if(typeof a==typeof r){continue}zt(a,r)}(t.sham||r&&r.sham)&&G(a,"sham",!0),xt(n,o,a,t)}}},ee=!!Object.getOwnPropertySymbols&&!m(function(){return !(Symbol()+"")}),ie=ee&&!Symbol.sham&&"symbol"==typeof Symbol(),ne=Array.isArray||function(t){return"Array"==T(t)},oe=function(t){return Object($(t))},re=Object.keys||function(t){return Vt(t,Dt)},ae=y?Object.defineProperties:function(t,e){U(t);for(var i,n=re(e),o=n.length,r=0;o>r;){W.f(t,i=n[r++],e[i])}return t},se=Ct("document","documentElement"),le=">",ce="<",he="prototype",ue="script",de=ht("IE_PROTO"),pe=function(){},fe=function(t){return ce+ue+le+t+ce+"/"+ue+le},ge=function(t){t.write(fe("")),t.close();var e=t.parentWindow.Object;return t=null,e},ve=function(){var t,e=D("iframe"),i="java"+ue+":";return e.style.display="none",se.appendChild(e),e.src=i+"",t=e.contentWindow.document,t.open(),t.write(fe("document.F=Object")),t.close(),t.F},be=function(){try{wt=document.domain&&new ActiveXObject("htmlfile")}catch(t){}be=wt?ge(wt):ve();for(var e=Dt.length;e--;){delete be[he][Dt[e]]}return be()};ut[de]=!0;var me=Object.create||function(t,e){var i;return null!==t?(pe[he]=U(t),i=new pe,pe[he]=null,i[de]=t):i=be(),void 0===e?i:ae(i,e)},ye=Ht.f,we={}.toString,Se="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],xe=function(t){try{return ye(t)}catch(e){return Se.slice()}},ke=function(t){return Se&&"[object Window]"==we.call(t)?xe(t):ye(R(t))},Oe={f:ke},Ce=rt("wks"),Pe=b.Symbol,Te=ie?Pe:lt,Ie=function(t){return N(Ce,t)||(ee&&N(Pe,t)?Ce[t]=Pe[t]:Ce[t]=Te("Symbol."+t)),Ce[t]},Ae=Ie,$e={f:Ae},Re=W.f,Ee=function(t){var e=kt.Symbol||(kt.Symbol={});N(e,t)||Re(e,t,{value:$e.f(t)})},je=W.f,_e=Ie("toStringTag"),Ne=function(t,e,i){t&&!N(t=i?t:t.prototype,_e)&&je(t,_e,{configurable:!0,value:e})},Fe=function(t){if("function"!=typeof t){throw TypeError(t+" is not a function")}return t},Ve=function(t,e,i){if(Fe(t),void 0===e){return t}switch(i){case 0:return function(){return t.call(e)};case 1:return function(i){return t.call(e,i)};case 2:return function(i,n){return t.call(e,i,n)};case 3:return function(i,n,o){return t.call(e,i,n,o)}}return function(){return t.apply(e,arguments)}},De=Ie("species"),Be=function(t,e){var i;return ne(t)&&(i=t.constructor,"function"!=typeof i||i!==Array&&!ne(i.prototype)?E(i)&&(i=i[De],null===i&&(i=void 0)):i=void 0),new (void 0===i?Array:i)(0===e?0:e)},Le=[].push,He=function(t){var e=1==t,i=2==t,n=3==t,o=4==t,r=6==t,a=5==t||r;return function(s,l,c,h){for(var u,d,p=oe(s),f=A(p),g=Ve(l,c,3),v=$t(f.length),b=0,m=h||Be,y=e?m(s,v):i?m(s,0):void 0;v>b;b++){if((a||b in f)&&(u=f[b],d=g(u,b,p),t)){if(e){y[b]=d}else{if(d){switch(t){case 3:return !0;case 5:return u;case 6:return b;case 2:Le.call(y,u)}}else{if(o){return !1}}}}}return r?-1:n||o?o:y}},Me={forEach:He(0),map:He(1),filter:He(2),some:He(3),every:He(4),find:He(5),findIndex:He(6)},Ue=Me.forEach,qe=ht("hidden"),ze="Symbol",We="prototype",Ge=Ie("toPrimitive"),Ke=St.set,Ye=St.getterFor(ze),Je=Object[We],Xe=b.Symbol,Qe=Ct("JSON","stringify"),Ze=M.f,ti=W.f,ei=Oe.f,ii=O.f,ni=rt("symbols"),oi=rt("op-symbols"),ri=rt("string-to-symbol-registry"),ai=rt("symbol-to-string-registry"),si=rt("wks"),li=b.QObject,ci=!li||!li[We]||!li[We].findChild,hi=y&&m(function(){return 7!=me(ti({},"a",{get:function(){return ti(this,"a",{value:7}).a}})).a})?function(t,e,i){var n=Ze(Je,e);n&&delete Je[e],ti(t,e,i),n&&t!==Je&&ti(Je,e,n)}:ti,ui=function(t,e){var i=ni[t]=me(Xe[We]);return Ke(i,{type:ze,tag:t,description:e}),y||(i.description=e),i},di=ee&&"symbol"==typeof Xe.iterator?function(t){return"symbol"==typeof t}:function(t){return Object(t) instanceof Xe},pi=function(t,e,i){t===Je&&pi(oi,e,i),U(t);var n=j(e,!0);return U(i),N(ni,n)?(i.enumerable?(N(t,qe)&&t[qe][n]&&(t[qe][n]=!1),i=me(i,{enumerable:C(0,!1)})):(N(t,qe)||ti(t,qe,C(1,{})),t[qe][n]=!0),hi(t,n,i)):ti(t,n,i)},fi=function(t,e){U(t);var i=R(e),n=re(i).concat(yi(i));return Ue(n,function(e){(!y||vi.call(i,e))&&pi(t,e,i[e])}),t},gi=function(t,e){return void 0===e?me(t):fi(me(t),e)},vi=function(t){var e=j(t,!0),i=ii.call(this,e);return this===Je&&N(ni,e)&&!N(oi,e)?!1:i||!N(this,e)||!N(ni,e)||N(this,qe)&&this[qe][e]?i:!0},bi=function(t,e){var i=R(t),n=j(e,!0);if(i!==Je||!N(ni,n)||N(oi,n)){var o=Ze(i,n);return !o||!N(ni,n)||N(i,qe)&&i[qe][n]||(o.enumerable=!0),o}},mi=function(t){var e=ei(R(t)),i=[];return Ue(e,function(t){N(ni,t)||N(ut,t)||i.push(t)}),i},yi=function(t){var e=t===Je,i=ei(e?oi:R(t)),n=[];return Ue(i,function(t){!N(ni,t)||e&&!N(Je,t)||n.push(ni[t])}),n};if(ee||(Xe=function(){if(this instanceof Xe){throw TypeError("Symbol is not a constructor")}var t=arguments.length&&void 0!==arguments[0]?arguments[0]+"":void 0,e=lt(t),i=function(t){this===Je&&i.call(oi,t),N(this,qe)&&N(this[qe],e)&&(this[qe][e]=!1),hi(this,e,C(1,t))};return y&&ci&&hi(Je,e,{configurable:!0,set:i}),ui(e,t)},xt(Xe[We],"toString",function(){return Ye(this).tag}),O.f=vi,W.f=pi,M.f=bi,Ht.f=Oe.f=mi,Ut.f=yi,y&&(ti(Xe[We],"description",{configurable:!0,get:function(){return Ye(this).description}}),xt(Je,"propertyIsEnumerable",vi,{unsafe:!0}))),ie||($e.f=function(t){return ui(Ie(t),t)}),te({global:!0,wrap:!0,forced:!ee,sham:!ee},{Symbol:Xe}),Ue(re(si),function(t){Ee(t)}),te({target:ze,stat:!0,forced:!ee},{"for":function(t){var e=t+"";if(N(ri,e)){return ri[e]}var i=Xe(e);return ri[e]=i,ai[i]=e,i},keyFor:function(t){if(!di(t)){throw TypeError(t+" is not a symbol")}return N(ai,t)?ai[t]:void 0},useSetter:function(){ci=!0},useSimple:function(){ci=!1}}),te({target:"Object",stat:!0,forced:!ee,sham:!y},{create:gi,defineProperty:pi,defineProperties:fi,getOwnPropertyDescriptor:bi}),te({target:"Object",stat:!0,forced:!ee},{getOwnPropertyNames:mi,getOwnPropertySymbols:yi}),te({target:"Object",stat:!0,forced:m(function(){Ut.f(1)})},{getOwnPropertySymbols:function(t){return Ut.f(oe(t))}}),Qe){var wi=!ee||m(function(){var t=Xe();return"[null]"!=Qe([t])||"{}"!=Qe({a:t})||"{}"!=Qe(Object(t))});te({target:"JSON",stat:!0,forced:wi},{stringify:function(t,e,i){for(var n,o=[t],r=1;arguments.length>r;){o.push(arguments[r++])}return n=e,!E(e)&&void 0===t||di(t)?void 0:(ne(e)||(e=function(t,e){return"function"==typeof n&&(e=n.call(this,t,e)),di(e)?void 0:e}),o[1]=e,Qe.apply(null,o))}})}Xe[We][Ge]||G(Xe[We],Ge,Xe[We].valueOf),Ne(Xe,ze),ut[qe]=!0;var Si=W.f,xi=b.Symbol;if(y&&"function"==typeof xi&&(!("description" in xi.prototype)||void 0!==xi().description)){var ki={},Oi=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:arguments[0]+"",e=this instanceof Oi?new xi(t):void 0===t?xi():xi(t);return""===t&&(ki[e]=!0),e};zt(Oi,xi);var Ci=Oi.prototype=xi.prototype;Ci.constructor=Oi;var Pi=Ci.toString,Ti=xi("test")+""=="Symbol(test)",Ii=/^Symbol\((.*)\)[^)]+$/;Si(Ci,"description",{configurable:!0,get:function(){var t=E(this)?this.valueOf():this,e=Pi.call(t);if(N(ki,t)){return""}var i=Ti?e.slice(7,-1):e.replace(Ii,"$1");return""===i?void 0:i}}),te({global:!0,forced:!0},{Symbol:Oi})}Ee("iterator");var Ai,$i,Ri=function(t,e,i){var n=j(e);n in t?W.f(t,n,C(0,i)):t[n]=i},Ei=Ct("navigator","userAgent")||"",ji=b.process,_i=ji&&ji.versions,Ni=_i&&_i.v8;Ni?(Ai=Ni.split("."),$i=Ai[0]+Ai[1]):Ei&&(Ai=Ei.match(/Edge\/(\d+)/),(!Ai||Ai[1]>=74)&&(Ai=Ei.match(/Chrome\/(\d+)/),Ai&&($i=Ai[1])));var Fi=$i&&+$i,Vi=Ie("species"),Di=function(t){return Fi>=51||!m(function(){var e=[],i=e.constructor={};return i[Vi]=function(){return{foo:1}},1!==e[t](Boolean).foo})},Bi=Ie("isConcatSpreadable"),Li=9007199254740991,Hi="Maximum allowed index exceeded",Mi=Fi>=51||!m(function(){var t=[];return t[Bi]=!1,t.concat()[0]!==t}),Ui=Di("concat"),qi=function(t){if(!E(t)){return !1}var e=t[Bi];return void 0!==e?!!e:ne(t)},zi=!Mi||!Ui;te({target:"Array",proto:!0,forced:zi},{concat:function(t){var e,i,n,o,r,a=oe(this),s=Be(a,0),l=0;for(e=-1,n=arguments.length;n>e;e++){if(r=-1===e?a:arguments[e],qi(r)){if(o=$t(r.length),l+o>Li){throw TypeError(Hi)}for(i=0;o>i;i++,l++){i in r&&Ri(s,l,r[i])}}else{if(l>=Li){throw TypeError(Hi)}Ri(s,l++,r)}}return s.length=l,s}});var Wi=Me.filter,Gi=Di("filter"),Ki=Gi&&!m(function(){[].filter.call({length:-1,0:1},function(t){throw t})});te({target:"Array",proto:!0,forced:!Gi||!Ki},{filter:function(t){return Wi(this,t,arguments.length>1?arguments[1]:void 0)}});var Yi=Ie("unscopables"),Ji=Array.prototype;void 0==Ji[Yi]&&W.f(Ji,Yi,{configurable:!0,value:me(null)});var Xi=function(t){Ji[Yi][t]=!0},Qi=Me.find,Zi="find",tn=!0;Zi in []&&Array(1)[Zi](function(){tn=!1}),te({target:"Array",proto:!0,forced:tn},{find:function(t){return Qi(this,t,arguments.length>1?arguments[1]:void 0)}}),Xi(Zi);var en=Me.findIndex,nn="findIndex",on=!0;nn in []&&Array(1)[nn](function(){on=!1}),te({target:"Array",proto:!0,forced:on},{findIndex:function(t){return en(this,t,arguments.length>1?arguments[1]:void 0)}}),Xi(nn);var rn=Nt.includes;te({target:"Array",proto:!0},{includes:function(t){return rn(this,t,arguments.length>1?arguments[1]:void 0)}}),Xi("includes");var an=function(t,e){var i=[][t];return !i||!m(function(){i.call(null,e||function(){throw 1},1)})},sn=Nt.indexOf,ln=[].indexOf,cn=!!ln&&1/[1].indexOf(1,-0)<0,hn=an("indexOf");te({target:"Array",proto:!0,forced:cn||hn},{indexOf:function(t){return cn?ln.apply(this,arguments)||0:sn(this,t,arguments.length>1?arguments[1]:void 0)}});var un,dn,pn,fn=!m(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}),gn=ht("IE_PROTO"),vn=Object.prototype,bn=fn?Object.getPrototypeOf:function(t){return t=oe(t),N(t,gn)?t[gn]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?vn:null},mn=Ie("iterator"),yn=!1,wn=function(){return this};[].keys&&(pn=[].keys(),"next" in pn?(dn=bn(bn(pn)),dn!==Object.prototype&&(un=dn)):yn=!0),void 0==un&&(un={}),N(un,mn)||G(un,mn,wn);var Sn={IteratorPrototype:un,BUGGY_SAFARI_ITERATORS:yn},xn=Sn.IteratorPrototype,kn=function(t,e,i){var n=e+" Iterator";return t.prototype=me(xn,{next:C(1,i)}),Ne(t,n,!1),t},On=function(t){if(!E(t)&&null!==t){throw TypeError("Can't set "+(t+"")+" as a prototype")}return t},Cn=Object.setPrototypeOf||("__proto__" in {}?function(){var t,e=!1,i={};try{t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,t.call(i,[]),e=i instanceof Array}catch(n){}return function(i,n){return U(i),On(n),e?t.call(i,n):i.__proto__=n,i}}():void 0),Pn=Sn.IteratorPrototype,Tn=Sn.BUGGY_SAFARI_ITERATORS,In=Ie("iterator"),An="keys",$n="values",Rn="entries",En=function(){return this},jn=function(t,e,i,n,o,r,a){kn(i,e,n);var s,l,c,h=function(t){if(t===o&&g){return g}if(!Tn&&t in p){return p[t]}switch(t){case An:return function(){return new i(this,t)};case $n:return function(){return new i(this,t)};case Rn:return function(){return new i(this,t)}}return function(){return new i(this)}},u=e+" Iterator",d=!1,p=t.prototype,f=p[In]||p["@@iterator"]||o&&p[o],g=!Tn&&f||h(o),v="Array"==e?p.entries||f:f;if(v&&(s=bn(v.call(new t)),Pn!==Object.prototype&&s.next&&(bn(s)!==Pn&&(Cn?Cn(s,Pn):"function"!=typeof s[In]&&G(s,In,En)),Ne(s,u,!0))),o==$n&&f&&f.name!==$n&&(d=!0,g=function(){return f.call(this)}),p[In]!==g&&G(p,In,g),o){if(l={values:h($n),keys:r?g:h(An),entries:h(Rn)},a){for(c in l){!Tn&&!d&&c in p||xt(p,c,l[c])}}else{te({target:e,proto:!0,forced:Tn||d},l)}}return l},_n="Array Iterator",Nn=St.set,Fn=St.getterFor(_n),Vn=jn(Array,"Array",function(t,e){Nn(this,{type:_n,target:R(t),index:0,kind:e})},function(){var t=Fn(this),e=t.target,i=t.kind,n=t.index++;return !e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==i?{value:n,done:!1}:"values"==i?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}},"values");Xi("keys"),Xi("values"),Xi("entries");var Dn=[].join,Bn=A!=Object,Ln=an("join",",");te({target:"Array",proto:!0,forced:Bn||Ln},{join:function(t){return Dn.call(R(this),void 0===t?",":t)}});var Hn=Me.map,Mn=Di("map"),Un=Mn&&!m(function(){[].map.call({length:-1,0:1},function(t){throw t})});te({target:"Array",proto:!0,forced:!Mn||!Un},{map:function(t){return Hn(this,t,arguments.length>1?arguments[1]:void 0)}});var qn=[].reverse,zn=[1,2];te({target:"Array",proto:!0,forced:zn+""==zn.reverse()+""},{reverse:function(){return ne(this)&&(this.length=this.length),qn.call(this)}});var Wn=Ie("species"),Gn=[].slice,Kn=Math.max;te({target:"Array",proto:!0,forced:!Di("slice")},{slice:function(t,e){var i,n,o,r=R(this),a=$t(r.length),s=jt(t,a),l=jt(void 0===e?a:e,a);if(ne(r)&&(i=r.constructor,"function"!=typeof i||i!==Array&&!ne(i.prototype)?E(i)&&(i=i[Wn],null===i&&(i=void 0)):i=void 0,i===Array||void 0===i)){return Gn.call(r,s,l)}for(n=new (void 0===i?Array:i)(Kn(l-s,0)),o=0;l>s;s++,o++){s in r&&Ri(n,o,r[s])}return n.length=o,n}});var Yn=[],Jn=Yn.sort,Xn=m(function(){Yn.sort(void 0)}),Qn=m(function(){Yn.sort(null)}),Zn=an("sort"),to=Xn||!Qn||Zn;te({target:"Array",proto:!0,forced:to},{sort:function(t){return void 0===t?Jn.call(oe(this)):Jn.call(oe(this),Fe(t))}});var eo=Math.max,io=Math.min,no=9007199254740991,oo="Maximum allowed length exceeded";te({target:"Array",proto:!0,forced:!Di("splice")},{splice:function(t,e){var i,n,o,r,a,s,l=oe(this),c=$t(l.length),h=jt(t,c),u=arguments.length;if(0===u?i=n=0:1===u?(i=0,n=c-h):(i=u-2,n=io(eo(It(e),0),c-h)),c+i-n>no){throw TypeError(oo)}for(o=Be(l,n),r=0;n>r;r++){a=h+r,a in l&&Ri(o,r,l[a])}if(o.length=n,n>i){for(r=h;c-n>r;r++){a=r+n,s=r+i,a in l?l[s]=l[a]:delete l[s]}for(r=c;r>c-n+i;r--){delete l[r-1]}}else{if(i>n){for(r=c-n;r>h;r--){a=r+n-1,s=r+i-1,a in l?l[s]=l[a]:delete l[s]}}}for(r=0;i>r;r++){l[r+h]=arguments[r+2]}return l.length=c-n+i,o}});var ro=function(t,e,i){var n,o;return Cn&&"function"==typeof(n=e.constructor)&&n!==i&&E(o=n.prototype)&&o!==i.prototype&&Cn(t,o),t},ao="	\n\x0B\f\r                　\u2028\u2029\ufeff",so="["+ao+"]",lo=RegExp("^"+so+so+"*"),co=RegExp(so+so+"*$"),ho=function(t){return function(e){var i=$(e)+"";return 1&t&&(i=i.replace(lo,"")),2&t&&(i=i.replace(co,"")),i}},uo={start:ho(1),end:ho(2),trim:ho(3)},po=Ht.f,fo=M.f,go=W.f,vo=uo.trim,bo="Number",mo=b[bo],yo=mo.prototype,wo=T(me(yo))==bo,So=function(t){var e,i,n,o,r,a,s,l,c=j(t,!1);if("string"==typeof c&&c.length>2){if(c=vo(c),e=c.charCodeAt(0),43===e||45===e){if(i=c.charCodeAt(2),88===i||120===i){return NaN}}else{if(48===e){switch(c.charCodeAt(1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return +c}for(r=c.slice(2),a=r.length,s=0;a>s;s++){if(l=r.charCodeAt(s),48>l||l>o){return NaN}}return parseInt(r,n)}}}return +c};if(Qt(bo,!mo(" 0o1")||!mo("0b1")||mo("+0x1"))){for(var xo,ko=function(t){var e=arguments.length<1?0:t,i=this;return i instanceof ko&&(wo?m(function(){yo.valueOf.call(i)}):T(i)!=bo)?ro(new mo(So(e)),i,ko):So(e)},Oo=y?po(mo):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),Co=0;Oo.length>Co;Co++){N(mo,xo=Oo[Co])&&!N(ko,xo)&&go(ko,xo,fo(mo,xo))}ko.prototype=yo,yo.constructor=ko,xt(b,bo,ko)}var Po=Object.assign,To=Object.defineProperty,Io=!Po||m(function(){if(y&&1!==Po({b:1},Po(To({},"a",{enumerable:!0,get:function(){To(this,"b",{value:3,enumerable:!1})}}),{b:2})).b){return !0}var t={},e={},i=Symbol(),n="abcdefghijklmnopqrst";return t[i]=7,n.split("").forEach(function(t){e[t]=t}),7!=Po({},t)[i]||re(Po({},e)).join("")!=n})?function(t,e){for(var i=oe(t),n=arguments.length,o=1,r=Ut.f,a=O.f;n>o;){for(var s,l=A(arguments[o++]),c=r?re(l).concat(r(l)):re(l),h=c.length,u=0;h>u;){s=c[u++],(!y||a.call(l,s))&&(i[s]=l[s])}}return i}:Po;te({target:"Object",stat:!0,forced:Object.assign!==Io},{assign:Io});var Ao=O.f,$o=function(t){return function(e){for(var i,n=R(e),o=re(n),r=o.length,a=0,s=[];r>a;){i=o[a++],(!y||Ao.call(n,i))&&s.push(t?[i,n[i]]:n[i])}return s}},Ro={entries:$o(!0),values:$o(!1)},Eo=Ro.entries;te({target:"Object",stat:!0},{entries:function(t){return Eo(t)}});var jo=Ie("toStringTag"),_o={};_o[jo]="z";var No=_o+""=="[object z]",Fo=Ie("toStringTag"),Vo="Arguments"==T(function(){return arguments}()),Do=function(t,e){try{return t[e]}catch(i){}},Bo=No?T:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=Do(e=Object(t),Fo))?i:Vo?T(e):"Object"==(n=T(e))&&"function"==typeof e.callee?"Arguments":n},Lo=No?{}.toString:function(){return"[object "+Bo(this)+"]"};No||xt(Object.prototype,"toString",Lo,{unsafe:!0});var Ho=uo.trim,Mo=b.parseFloat,Uo=1/Mo(ao+"-0")!==-(1/0),qo=Uo?function(t){var e=Ho(t+""),i=Mo(e);return 0===i&&"-"==e.charAt(0)?-0:i}:Mo;te({global:!0,forced:parseFloat!=qo},{parseFloat:qo});var zo=uo.trim,Wo=b.parseInt,Go=/^[+-]?0[Xx]/,Ko=8!==Wo(ao+"08")||22!==Wo(ao+"0x16"),Yo=Ko?function(t,e){var i=zo(t+"");return Wo(i,e>>>0||(Go.test(i)?16:10))}:Wo;te({global:!0,forced:parseInt!=Yo},{parseInt:Yo});var Jo=Ie("match"),Xo=function(t){var e;return E(t)&&(void 0!==(e=t[Jo])?!!e:"RegExp"==T(t))},Qo=function(){var t=U(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Zo=m(function(){var t=i("a","y");return t.lastIndex=2,null!=t.exec("abcd")}),tr=m(function(){var t=i("^r","gy");return t.lastIndex=2,null!=t.exec("str")}),er={UNSUPPORTED_Y:Zo,BROKEN_CARET:tr},ir=Ie("species"),nr=function(t){var e=Ct(t),i=W.f;y&&e&&!e[ir]&&i(e,ir,{configurable:!0,get:function(){return this}})},or=W.f,rr=Ht.f,ar=St.set,sr=Ie("match"),lr=b.RegExp,cr=lr.prototype,hr=/a/g,ur=/a/g,dr=new lr(hr)!==hr,pr=er.UNSUPPORTED_Y,fr=y&&Qt("RegExp",!dr||pr||m(function(){return ur[sr]=!1,lr(hr)!=hr||lr(ur)==ur||"/a/i"!=lr(hr,"i")}));if(fr){for(var gr=function(t,e){var i,n=this instanceof gr,o=Xo(t),r=void 0===e;if(!n&&o&&t.constructor===gr&&r){return t}dr?o&&!r&&(t=t.source):t instanceof gr&&(r&&(e=Qo.call(t)),t=t.source),pr&&(i=!!e&&e.indexOf("y")>-1,i&&(e=e.replace(/y/g,"")));var a=ro(dr?new lr(t,e):lr(t,e),n?this:cr,gr);return pr&&i&&ar(a,{sticky:i}),a},vr=(function(t){t in gr||or(gr,t,{configurable:!0,get:function(){return lr[t]},set:function(e){lr[t]=e}})}),br=rr(lr),mr=0;br.length>mr;){vr(br[mr++])}cr.constructor=gr,gr.prototype=cr,xt(b,"RegExp",gr)}nr("RegExp");var yr=RegExp.prototype.exec,wr=String.prototype.replace,Sr=yr,xr=function(){var t=/a/,e=/b*/g;return yr.call(t,"a"),yr.call(e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),kr=er.UNSUPPORTED_Y||er.BROKEN_CARET,Or=void 0!==/()??/.exec("")[1],Cr=xr||Or||kr;Cr&&(Sr=function(t){var e,i,n,o,r=this,a=kr&&r.sticky,s=Qo.call(r),l=r.source,c=0,h=t;return a&&(s=s.replace("y",""),-1===s.indexOf("g")&&(s+="g"),h=(t+"").slice(r.lastIndex),r.lastIndex>0&&(!r.multiline||r.multiline&&"\n"!==t[r.lastIndex-1])&&(l="(?: "+l+")",h=" "+h,c++),i=RegExp("^(?:"+l+")",s)),Or&&(i=RegExp("^"+l+"$(?!\\s)",s)),xr&&(e=r.lastIndex),n=yr.call(a?i:r,h),a?n?(n.input=n.input.slice(c),n[0]=n[0].slice(c),n.index=r.lastIndex,r.lastIndex+=n[0].length):r.lastIndex=0:xr&&n&&(r.lastIndex=r.global?n.index+n[0].length:e),Or&&n&&n.length>1&&wr.call(n[0],i,function(){for(o=1;o<arguments.length-2;o++){void 0===arguments[o]&&(n[o]=void 0)}}),n});var Pr=Sr;te({target:"RegExp",proto:!0,forced:/./.exec!==Pr},{exec:Pr});var Tr="toString",Ir=RegExp.prototype,Ar=Ir[Tr],$r=m(function(){return"/a/b"!=Ar.call({source:"a",flags:"b"})}),Rr=Ar.name!=Tr;($r||Rr)&&xt(RegExp.prototype,Tr,function(){var t=U(this),e=t.source+"",i=t.flags,n=(void 0===i&&t instanceof RegExp&&!("flags" in Ir)?Qo.call(t):i)+"";return"/"+e+"/"+n},{unsafe:!0});var Er=function(t){if(Xo(t)){throw TypeError("The method doesn't accept regular expressions")}return t},jr=Ie("match"),_r=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[jr]=!1,"/./"[t](e)}catch(n){}}return !1};te({target:"String",proto:!0,forced:!_r("includes")},{includes:function(t){return !!~($(this)+"").indexOf(Er(t),arguments.length>1?arguments[1]:void 0)}});var Nr=function(t){return function(e,i){var n,o,r=$(e)+"",a=It(i),s=r.length;return 0>a||a>=s?t?"":void 0:(n=r.charCodeAt(a),55296>n||n>56319||a+1===s||(o=r.charCodeAt(a+1))<56320||o>57343?t?r.charAt(a):n:t?r.slice(a,a+2):(n-55296<<10)+(o-56320)+65536)}},Fr={codeAt:Nr(!1),charAt:Nr(!0)},Vr=Fr.charAt,Dr="String Iterator",Br=St.set,Lr=St.getterFor(Dr);jn(String,"String",function(t){Br(this,{type:Dr,string:t+"",index:0})},function(){var t,e=Lr(this),i=e.string,n=e.index;return n>=i.length?{value:void 0,done:!0}:(t=Vr(i,n),e.index+=t.length,{value:t,done:!1})});var Hr=Ie("species"),Mr=!m(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}),Ur=function(){return"$0"==="a".replace(/./,"$0")}(),qr=!m(function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2!==i.length||"a"!==i[0]||"b"!==i[1]}),zr=function(t,e,i,n){var o=Ie(t),r=!m(function(){var e={};return e[o]=function(){return 7},7!=""[t](e)}),a=r&&!m(function(){var e=!1,i=/a/;return"split"===t&&(i={},i.constructor={},i.constructor[Hr]=function(){return i},i.flags="",i[o]=/./[o]),i.exec=function(){return e=!0,null},i[o](""),!e});if(!r||!a||"replace"===t&&(!Mr||!Ur)||"split"===t&&!qr){var s=/./[o],l=i(o,""[t],function(t,e,i,n,o){return e.exec===Pr?r&&!o?{done:!0,value:s.call(e,i,n)}:{done:!0,value:t.call(i,e,n)}:{done:!1}},{REPLACE_KEEPS_$0:Ur}),c=l[0],h=l[1];xt(String.prototype,t,c),xt(RegExp.prototype,o,2==e?function(t,e){return h.call(t,this,e)}:function(t){return h.call(t,this)})}n&&G(RegExp.prototype[o],"sham",!0)},Wr=Fr.charAt,Gr=function(t,e,i){return e+(i?Wr(t,e).length:1)},Kr=function(t,e){var i=t.exec;if("function"==typeof i){var n=i.call(t,e);if("object"!=typeof n){throw TypeError("RegExp exec method returned something other than an Object or null")}return n}if("RegExp"!==T(t)){throw TypeError("RegExp#exec called on incompatible receiver")}return Pr.call(t,e)},Yr=Math.max,Jr=Math.min,Xr=Math.floor,Qr=/\$([$&'`]|\d\d?|<[^>]*>)/g,Zr=/\$([$&'`]|\d\d?)/g,ta=function(t){return void 0===t?t:t+""};zr("replace",2,function(t,e,i,n){function o(t,i,n,o,r,a){var s=n+t.length,l=o.length,c=Zr;return void 0!==r&&(r=oe(r),c=Qr),e.call(a,c,function(e,a){var c;switch(a.charAt(0)){case"$":return"$";case"&":return t;case"`":return i.slice(0,n);case"'":return i.slice(s);case"<":c=r[a.slice(1,-1)];break;default:var h=+a;if(0===h){return e}if(h>l){var u=Xr(h/10);return 0===u?e:l>=u?void 0===o[u-1]?a.charAt(1):o[u-1]+a.charAt(1):e}c=o[h-1]}return void 0===c?"":c})}return[function(i,n){var o=$(this),r=void 0==i?void 0:i[t];return void 0!==r?r.call(i,o,n):e.call(o+"",i,n)},function(t,r){if(n.REPLACE_KEEPS_$0||"string"==typeof r&&-1===r.indexOf("$0")){var a=i(e,t,this,r);if(a.done){return a.value}}var s=U(t),l=this+"",c="function"==typeof r;c||(r+="");var h=s.global;if(h){var u=s.unicode;s.lastIndex=0}for(var d=[];;){var p=Kr(s,l);if(null===p){break}if(d.push(p),!h){break}var f=p[0]+"";""===f&&(s.lastIndex=Gr(l,$t(s.lastIndex),u))}for(var g="",v=0,b=0;b<d.length;b++){p=d[b];for(var m=p[0]+"",y=Yr(Jr(It(p.index),l.length),0),w=[],S=1;S<p.length;S++){w.push(ta(p[S]))}var x=p.groups;if(c){var k=[m].concat(w,y,l);void 0!==x&&k.push(x);var O=r.apply(void 0,k)+""}else{O=o(m,l,y,w,x,r)}y>=v&&(g+=l.slice(v,y)+O,v=y+m.length)}return g+l.slice(v)}]});var ea=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e};zr("search",1,function(t,e,i){return[function(e){var i=$(this),n=void 0==e?void 0:e[t];return void 0!==n?n.call(e,i):RegExp(e)[t](i+"")},function(t){var n=i(e,t,this);if(n.done){return n.value}var o=U(t),r=this+"",a=o.lastIndex;ea(a,0)||(o.lastIndex=0);var s=Kr(o,r);return ea(o.lastIndex,a)||(o.lastIndex=a),null===s?-1:s.index}]});var ia=Ie("species"),na=function(t,e){var i,n=U(t).constructor;return void 0===n||void 0==(i=U(n)[ia])?e:Fe(i)},oa=[].push,ra=Math.min,aa=4294967295,sa=!m(function(){return !RegExp(aa,"y")});zr("split",2,function(t,e,i){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,i){var n=$(this)+"",o=void 0===i?aa:i>>>0;if(0===o){return[]}if(void 0===t){return[n]}if(!Xo(t)){return e.call(n,t,o)}for(var r,a,s,l=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),h=0,u=RegExp(t.source,c+"g");(r=Pr.call(u,n))&&(a=u.lastIndex,!(a>h&&(l.push(n.slice(h,r.index)),r.length>1&&r.index<n.length&&oa.apply(l,r.slice(1)),s=r[0].length,h=a,l.length>=o)));){u.lastIndex===r.index&&u.lastIndex++}return h===n.length?(s||!u.test(""))&&l.push(""):l.push(n.slice(h)),l.length>o?l.slice(0,o):l}:"0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:e.call(this,t,i)}:e,[function(e,i){var o=$(this),r=void 0==e?void 0:e[t];return void 0!==r?r.call(e,o,i):n.call(o+"",e,i)},function(t,o){var r=i(n,t,this,o,n!==e);if(r.done){return r.value}var a=U(t),s=this+"",l=na(a,RegExp),c=a.unicode,h=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(sa?"y":"g"),u=new l(sa?a:"^(?:"+a.source+")",h),d=void 0===o?aa:o>>>0;if(0===d){return[]}if(0===s.length){return null===Kr(u,s)?[s]:[]}for(var p=0,f=0,g=[];f<s.length;){u.lastIndex=sa?f:0;var v,b=Kr(u,sa?s:s.slice(f));if(null===b||(v=ra($t(u.lastIndex+(sa?0:f)),s.length))===p){f=Gr(s,f,c)}else{if(g.push(s.slice(p,f)),g.length===d){return g}for(var m=1;m<=b.length-1;m++){if(g.push(b[m]),g.length===d){return g}}f=p=v}}return g.push(s.slice(p)),g}]},!sa);var la="᠎",ca=function(t){return m(function(){return !!ao[t]()||la[t]()!=la||ao[t].name!==t})},ha=uo.trim;te({target:"String",proto:!0,forced:ca("trim")},{trim:function(){return ha(this)}});var ua={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},da=Me.forEach,pa=an("forEach")?function(t){return da(this,t,arguments.length>1?arguments[1]:void 0)}:[].forEach;for(var fa in ua){var ga=b[fa],va=ga&&ga.prototype;if(va&&va.forEach!==pa){try{G(va,"forEach",pa)}catch(ba){va.forEach=pa}}}var ma=Ie("iterator"),ya=Ie("toStringTag"),wa=Vn.values;for(var Sa in ua){var xa=b[Sa],ka=xa&&xa.prototype;if(ka){if(ka[ma]!==wa){try{G(ka,ma,wa)}catch(ba){ka[ma]=wa}}if(ka[ya]||G(ka,ya,Sa),ua[Sa]){for(var Oa in Vn){if(ka[Oa]!==Vn[Oa]){try{G(ka,Oa,Vn[Oa])}catch(ba){ka[Oa]=Vn[Oa]}}}}}}var Ca="1.18.0",Pa=4;try{var Ta=t.fn.dropdown.Constructor.VERSION;void 0!==Ta&&(Pa=parseInt(Ta,10))}catch(Ia){}try{var Aa=bootstrap.Tooltip.VERSION;void 0!==Aa&&(Pa=parseInt(Aa,10))}catch(Ia){}var $a={3:{iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{iconsPrefix:"fa",icons:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{iconsPrefix:"fa",icons:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[Pa],Ra={id:void 0,firstLoad:!0,height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",striped:!1,headerStyle:function(t){return{}},rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(t){return{}},searchAccentNeutralise:!1,showColumns:!1,showSearch:!1,showPageGo:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(t,e){return""},detailFilter:function(t,e){return !0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["search","paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:$a.classes.buttonsPrefix,buttonsClass:$a.classes.buttons,icons:$a.icons,iconSize:void 0,iconsPrefix:$a.iconsPrefix,loadingFontSize:"auto",loadingTemplate:function(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(t,e){return !1},onClickCell:function(t,e,i,n){return !1},onDblClickCell:function(t,e,i,n){return !1},onClickRow:function(t,e){return !1},onDblClickRow:function(t,e){return !1},onSort:function(t,e){return !1},onCheck:function(t){return !1},onUncheck:function(t){return !1},onCheckAll:function(t){return !1},onUncheckAll:function(t){return !1},onCheckSome:function(t){return !1},onUncheckSome:function(t){return !1},onLoadSuccess:function(t){return !1},onLoadError:function(t){return !1},onColumnSwitch:function(t,e){return !1},onPageChange:function(t,e){return !1},onSearch:function(t){return !1},onShowSearch:function(){return !1},onToggle:function(t){return !1},onPreBody:function(t){return !1},onPostBody:function(){return !1},onPostHeader:function(){return !1},onPostFooter:function(){return !1},onExpandRow:function(t,e,i){return !1},onCollapseRow:function(t,e){return !1},onRefreshOptions:function(t){return !1},onRefresh:function(t){return !1},onResetView:function(){return !1},onScrollBody:function(){return !1}},Ea={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,e,i,n){return void 0!==n&&n>0&&n>i?"Showing ".concat(t," to ").concat(e," of ").concat(i," rows (filtered from ").concat(n," total rows)"):"Showing ".concat(t," to ").concat(e," of ").concat(i," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatSearch:function(){return"Search"},formatShowSearch:function(){return"Show Search"},formatPageGo:function(){return"Go"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},ja={field:void 0,title:void 0,titleTooltip:void 0,"class":void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,ignore:!1,switchable:!0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:!1,events:void 0},_a=["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],Na={"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody"};Object.assign(Ra,Ea);var Fa={VERSION:Ca,THEME:"bootstrap".concat(Pa),CONSTANTS:$a,DEFAULTS:Ra,COLUMN_DEFAULTS:ja,METHODS:_a,EVENTS:Na,LOCALES:{en:Ea,"en-US":Ea}},Va=m(function(){re(1)});te({target:"Object",stat:!0,forced:Va},{keys:function(t){return re(oe(t))}});var Da=M.f,Ba="".endsWith,La=Math.min,Ha=_r("endsWith"),Ma=!Ha&&!!function(){var t=Da(String.prototype,"endsWith");return t&&!t.writable}();te({target:"String",proto:!0,forced:!Ma&&!Ha},{endsWith:function(t){var e=$(this)+"";Er(t);var i=arguments.length>1?arguments[1]:void 0,n=$t(e.length),o=void 0===i?n:La($t(i),n),r=t+"";return Ba?Ba.call(e,r,o):e.slice(o-r.length,o)===r}});var Ua=M.f,qa="".startsWith,za=Math.min,Wa=_r("startsWith"),Ga=!Wa&&!!function(){var t=Ua(String.prototype,"startsWith");return t&&!t.writable}();te({target:"String",proto:!0,forced:!Ga&&!Wa},{startsWith:function(t){var e=$(this)+"";Er(t);var i=$t(za(arguments.length>1?arguments[1]:void 0,e.length)),n=t+"";return qa?qa.call(e,n,i):e.slice(i,i+n.length)===n}});var Ka={getSearchInput:function(e){return"string"==typeof e.options.searchSelector?t(e.options.searchSelector):e.$toolbar.find(".search input")},sprintf:function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;e>n;n++){i[n-1]=arguments[n]}var o=!0,r=0,a=t.replace(/%s/g,function(){var t=i[r++];return void 0===t?(o=!1,""):t});return o?a:""},isObject:function(t){return t instanceof Object&&!Array.isArray(t)},isEmptyObject:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function(t){return !isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function(t,e){var i=!0,n=!1,o=void 0;try{for(var r,a=t[Symbol.iterator]();!(i=(r=a.next()).done);i=!0){var s=r.value;if(s.field===e){return s.title}}}catch(l){n=!0,o=l}finally{try{i||null==a["return"]||a["return"]()}finally{if(n){throw o}}}return""},setFieldIndex:function(t){var e=0,i=[],n=!0,o=!1,r=void 0;try{for(var a,s=t[0][Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var l=a.value;e+=l.colspan||1}}catch(c){o=!0,r=c}finally{try{n||null==s["return"]||s["return"]()}finally{if(o){throw r}}}for(var h=0;h<t.length;h++){i[h]=[];for(var u=0;e>u;u++){i[h][u]=!1}}for(var d=0;d<t.length;d++){var p=!0,f=!1,g=void 0;try{for(var v,b=t[d][Symbol.iterator]();!(p=(v=b.next()).done);p=!0){var m=v.value,y=m.rowspan||1,w=m.colspan||1,S=i[d].indexOf(!1);m.colspanIndex=S,1===w?(m.fieldIndex=S,void 0===m.field&&(m.field=S)):m.colspanGroup=m.colspan;for(var x=0;y>x;x++){for(var k=0;w>k;k++){i[d+x][S+k]=!0}}}}catch(c){f=!0,g=c}finally{try{p||null==b["return"]||b["return"]()}finally{if(f){throw g}}}}},normalizeAccent:function(t){return"string"!=typeof t?t:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(t){var e,i=(e=[]).concat.apply(e,l(t)),n=!0,o=!1,r=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var c=a.value,h=!0,u=!1,d=void 0;try{for(var p,f=c[Symbol.iterator]();!(h=(p=f.next()).done);h=!0){var g=p.value;if(g.colspanGroup>1){for(var v=0,b=function(t){var e=i.find(function(e){return e.fieldIndex===t
});e.visible&&v++},m=g.colspanIndex;m<g.colspanIndex+g.colspanGroup;m++){b(m)}g.colspan=v,g.visible=v>0}}}catch(y){u=!0,d=y}finally{try{h||null==f["return"]||f["return"]()}finally{if(u){throw d}}}}}catch(y){o=!0,r=y}finally{try{n||null==s["return"]||s["return"]()}finally{if(o){throw r}}}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var e=t("<div/>").addClass("fixed-table-scroll-inner"),i=t("<div/>").addClass("fixed-table-scroll-outer");i.append(e),t("body").append(i);var n=e[0].offsetWidth;i.css("overflow","scroll");var o=e[0].offsetWidth;n===o&&(o=i[0].clientWidth),i.remove(),this.cachedWidth=n-o}return this.cachedWidth},calculateObjectValue:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=arguments.length>3?arguments[3]:void 0,r=e;if("string"==typeof e){var a=e.split(".");if(a.length>1){r=window;var s=!0,c=!1,h=void 0;try{for(var u,d=a[Symbol.iterator]();!(s=(u=d.next()).done);s=!0){var p=u.value;r=r[p]}}catch(f){c=!0,h=f}finally{try{s||null==d["return"]||d["return"]()}finally{if(c){throw h}}}}else{r=window[e]}}return null!==r&&"object"===n(r)?r:"function"==typeof r?r.apply(t,i||[]):!r&&"string"==typeof e&&this.sprintf.apply(this,[e].concat(l(i)))?this.sprintf.apply(this,[e].concat(l(i))):o},compareObjects:function(t,e,i){var n=Object.keys(t),o=Object.keys(e);if(i&&n.length!==o.length){return !1}for(var r=0,a=n;r<a.length;r++){var s=a[r];if(o.includes(s)&&t[s]!==e[s]){return !1}}return !0},escapeHTML:function(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t},unescapeHTML:function(t){return"string"==typeof t?t.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#039;/g,"'").replace(/&#x60;/g,"`"):t},getRealDataAttr:function(t){for(var e=0,i=Object.entries(t);e<i.length;e++){var n=s(i[e],2),o=n[0],r=n[1],a=o.split(/(?=[A-Z])/).join("-").toLowerCase();a!==o&&(t[a]=r,delete t[o])}return t},getItemField:function(t,e,i){var n=t;if("string"!=typeof e||t.hasOwnProperty(e)){return i?this.escapeHTML(t[e]):t[e]}var o=e.split("."),r=!0,a=!1,s=void 0;try{for(var l,c=o[Symbol.iterator]();!(r=(l=c.next()).done);r=!0){var h=l.value;n=n&&n[h]}}catch(u){a=!0,s=u}finally{try{r||null==c["return"]||c["return"]()}finally{if(a){throw s}}}return i?this.escapeHTML(n):n},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(t,e){var i=!0,n=!1,o=void 0;try{for(var r,a=t[Symbol.iterator]();!(i=(r=a.next()).done);i=!0){var s=r.value;if(JSON.stringify(s)===JSON.stringify(e)){return t.indexOf(s)}}}catch(l){n=!0,o=l}finally{try{i||null==a["return"]||a["return"]()}finally{if(n){throw o}}}return -1},trToData:function(e,i){var n=this,o=[],r=[];return i.each(function(i,a){var s=t(a),l={};l._id=s.attr("id"),l._class=s.attr("class"),l._data=n.getRealDataAttr(s.data()),l._style=s.attr("style"),s.find(">td,>th").each(function(o,a){for(var s=t(a),c=+s.attr("colspan")||1,h=+s.attr("rowspan")||1,u=o;r[i]&&r[i][u];u++){}for(var d=u;u+c>d;d++){for(var p=i;i+h>p;p++){r[p]||(r[p]=[]),r[p][d]=!0}}var f=e[u].field;l[f]=s.html().trim(),l["_".concat(f,"_id")]=s.attr("id"),l["_".concat(f,"_class")]=s.attr("class"),l["_".concat(f,"_rowspan")]=s.attr("rowspan"),l["_".concat(f,"_colspan")]=s.attr("colspan"),l["_".concat(f,"_title")]=s.attr("title"),l["_".concat(f,"_data")]=n.getRealDataAttr(s.data()),l["_".concat(f,"_style")]=s.attr("style")}),o.push(l)}),o},sort:function(t,e,i,n,o,r){return(void 0===t||null===t)&&(t=""),(void 0===e||null===e)&&(e=""),n&&t===e&&(t=o,e=r),this.isNumeric(t)&&this.isNumeric(e)?(t=parseFloat(t),e=parseFloat(e),e>t?-1*i:t>e?i:0):t===e?0:("string"!=typeof t&&(t=""+t),-1===t.localeCompare(e)?-1*i:i)},getEventName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=e||"".concat(+new Date).concat(~~(1000000*Math.random())),"".concat(t,"-").concat(e)},hasDetailViewIcon:function(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function(t){var e=!0,i=!1,n=void 0;try{for(var o,r=t[Symbol.iterator]();!(e=(o=r.next()).done);e=!0){for(var a=o.value,s=0,l=Object.keys(a);s<l.length;s++){var c=l[s];if(c.startsWith("_")&&(c.endsWith("_rowspan")||c.endsWith("_colspan"))){return !0}}}}catch(h){i=!0,n=h}finally{try{e||null==r["return"]||r["return"]()}finally{if(i){throw n}}}return !1},deepCopy:function(e){return void 0===e?e:t.extend(!0,Array.isArray(e)?[]:{},e)}},Ya=50,Ja=4,Xa=function(){function t(e){var i=this;o(this,t),this.rows=e.rows,this.scrollEl=e.scrollEl,this.contentEl=e.contentEl,this.callback=e.callback,this.itemHeight=e.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,e.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var n=function(){i.lastCluster!==(i.lastCluster=i.getNum())&&(i.initDOM(i.rows),i.callback())};this.scrollEl.addEventListener("scroll",n,!1),this.destroy=function(){i.contentEl.innerHtml="",i.scrollEl.removeEventListener("scroll",n,!1)}}return a(t,[{key:"initDOM",value:function(t,e){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));var i=this.initData(t,this.getNum(e)),n=i.rows.join(""),o=this.checkChanges("data",n),r=this.checkChanges("top",i.topOffset),a=this.checkChanges("bottom",i.bottomOffset),s=[];o&&r?(i.topOffset&&s.push(this.getExtra("top",i.topOffset)),s.push(n),i.bottomOffset&&s.push(this.getExtra("bottom",i.bottomOffset)),this.contentEl.innerHTML=s.join(""),e&&(this.contentEl.scrollTop=this.cache.scrollTop)):a&&(this.contentEl.lastChild.style.height="".concat(i.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=this.itemHeight*Ya,this.clusterRows=Ya*Ja,this.clusterHeight=this.blockHeight*Ja}},{key:"getNum",value:function(t){return this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(t,e){if(t.length<Ya){return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t}}var i=Math.max((this.clusterRows-Ya)*e,0),n=i+this.clusterRows,o=Math.max(i*this.itemHeight,0),r=Math.max((t.length-n)*this.itemHeight,0),a=[],s=i;1>o&&s++;for(var l=i;n>l;l++){t[l]&&a.push(t[l])}return{topOffset:o,bottomOffset:r,rowsAbove:s,rows:a}}},{key:"checkChanges",value:function(t,e){var i=e!==this.cache[t];return this.cache[t]=e,i}},{key:"getExtra",value:function(t,e){var i=document.createElement("tr");return i.className="virtual-scroll-".concat(t),e&&(i.style.height="".concat(e,"px")),i.outerHTML}}]),t}(),Qa=function(){function e(i,n){o(this,e),this.options=n,this.$el=t(i),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return a(e,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var e=this.options;this.constants=Fa.CONSTANTS,this.constants.theme=t.fn.bootstrapTable.theme;var i=e.buttonsPrefix?"".concat(e.buttonsPrefix,"-"):"";this.constants.buttonsClass=[e.buttonsPrefix,i+e.buttonsClass,Ka.sprintf("".concat(i,"%s"),e.iconSize)].join(" ").trim(),this.buttons=Ka.calculateObjectValue(this,e.buttons,[],[])}},{key:"initLocale",value:function(){if(this.options.locale){var e=t.fn.bootstrapTable.locales,i=this.options.locale.split(/-|_/);i[0]=i[0].toLowerCase(),i[1]&&(i[1]=i[1].toUpperCase()),e[this.options.locale]?t.extend(this.options,e[this.options.locale]):e[i.join("-")]?t.extend(this.options,e[i.join("-")]):e[i[0]]&&t.extend(this.options,e[i[0]])}}},{key:"initContainer",value:function(){var e=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",i=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",n=Ka.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=t('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(e,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(n,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"><table><thead><tr></tr></thead></table></div>\n      </div>\n      ').concat(i,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=t("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var i=this,n=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=t('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each(function(e,o){var r=t(o),a=[];r.find("th").each(function(e,i){var n=t(i);void 0!==n.data("field")&&n.data("field","".concat(n.data("field"))),a.push(t.extend({},{title:n.html(),"class":n.attr("class"),titleTooltip:n.attr("title"),rowspan:n.attr("rowspan")?+n.attr("rowspan"):void 0,colspan:n.attr("colspan")?+n.attr("colspan"):void 0},n.data()))}),n.push(a),r.attr("class")&&i._headerTrClasses.push(r.attr("class")),r.attr("style")&&i._headerTrStyles.push(r.attr("style"))}),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=t.extend(!0,[],n,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],Ka.setFieldIndex(this.options.columns),this.options.columns.forEach(function(n,o){n.forEach(function(n,r){var a=t.extend({},e.COLUMN_DEFAULTS,n);void 0!==a.fieldIndex&&(i.columns[a.fieldIndex]=a,i.fieldsColumnsIndex[a.field]=a.fieldIndex),i.options.columns[o][r]=a})}),!this.options.data.length){var o=Ka.trToData(this.columns,this.$el.find(">tbody>tr"));o.length&&(this.options.data=o,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=Ka.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var e=this,i={},n=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},Ka.updateFieldGroup(this.options.columns),this.options.columns.forEach(function(t,o){n.push("<tr".concat(Ka.sprintf(' class="%s"',e._headerTrClasses[o])," ").concat(Ka.sprintf(' style="%s"',e._headerTrStyles[o]),">"));var r="";0===o&&Ka.hasDetailViewIcon(e.options)&&(r='<th class="detail" rowspan="'.concat(e.options.columns.length,'">\n          <div class="fht-cell"></div>\n          </th>')),r&&"right"!==e.options.detailViewAlign&&n.push(r),t.forEach(function(t,r){var a=Ka.sprintf(' class="%s"',t["class"]),l=t.widthUnit,c=parseFloat(t.width),h=Ka.sprintf("text-align: %s; ",t.halign?t.halign:t.align),u=Ka.sprintf("text-align: %s; ",t.align),d=Ka.sprintf("vertical-align: %s; ",t.valign);if(d+=Ka.sprintf("width: %s; ",!t.checkbox&&!t.radio||c?c?c+l:void 0:t.showSelectTitle?void 0:"36px"),void 0!==t.fieldIndex||t.visible){var p=Ka.calculateObjectValue(null,e.options.headerStyle,[t]),f=[],g="";if(p&&p.css){for(var v=0,b=Object.entries(p.css);v<b.length;v++){var m=s(b[v],2),y=m[0],w=m[1];f.push("".concat(y,": ").concat(w))}}if(p&&p.classes&&(g=Ka.sprintf(' class="%s"',t["class"]?[t["class"],p.classes].join(" "):p.classes)),void 0!==t.fieldIndex){if(e.header.fields[t.fieldIndex]=t.field,e.header.styles[t.fieldIndex]=u+d,e.header.classes[t.fieldIndex]=a,e.header.formatters[t.fieldIndex]=t.formatter,e.header.detailFormatters[t.fieldIndex]=t.detailFormatter,e.header.events[t.fieldIndex]=t.events,e.header.sorters[t.fieldIndex]=t.sorter,e.header.sortNames[t.fieldIndex]=t.sortName,e.header.cellStyles[t.fieldIndex]=t.cellStyle,e.header.searchables[t.fieldIndex]=t.searchable,!t.visible){return}if(e.options.cardView&&!t.cardVisible){return}i[t.field]=t}n.push("<th".concat(Ka.sprintf(' title="%s"',t.titleTooltip)),t.checkbox||t.radio?Ka.sprintf(' class="bs-checkbox %s"',t["class"]||""):g||a,Ka.sprintf(' style="%s"',h+d+f.join("; ")),Ka.sprintf(' rowspan="%s"',t.rowspan),Ka.sprintf(' colspan="%s"',t.colspan),Ka.sprintf(' data-field="%s"',t.field),0===r&&o>0?" data-not-first-th":"",">"),n.push(Ka.sprintf('<div class="th-inner %s">',e.options.sortable&&t.sortable?"sortable both":""));var S=e.options.escape?Ka.escapeHTML(t.title):t.title,x=S;t.checkbox&&(S="",!e.options.singleSelect&&e.options.checkboxHeader&&(S='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),e.header.stateField=t.field),t.radio&&(S="",e.header.stateField=t.field),!S&&t.showSelectTitle&&(S+=x),n.push(S),n.push("</div>"),n.push('<div class="fht-cell"></div>'),n.push("</div>"),n.push("</th>")}}),r&&"right"===e.options.detailViewAlign&&n.push(r),n.push("</tr>")}),this.$header.html(n.join("")),this.$header.find("th[data-field]").each(function(e,n){t(n).data(i[t(n).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(i){var n=t(i.currentTarget);return e.options.detailView&&!n.parent().hasClass("bs-checkbox")&&n.closest(".bootstrap-table")[0]!==e.$container[0]?!1:void (e.options.sortable&&n.parent().data().sortable&&e.onSort(i))}),this.$header.children().children().off("keypress").on("keypress",function(i){if(e.options.sortable&&t(i.currentTarget).data().sortable){var n=i.keyCode||i.which;13===n&&e.onSort(i)}});var o=Ka.getEventName("resize.bootstrap-table",this.$el.attr("id"));t(window).off(o),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),t(window).on(o,function(){return e.resetView()})),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",function(i){i.stopPropagation();var n=t(i.currentTarget).prop("checked");e[n?"checkAll":"uncheckAll"](),e.updateSelected()})}},{key:"initData",value:function(t,e){"append"===e?this.options.data=this.options.data.concat(t):"prepend"===e?this.options.data=[].concat(t).concat(this.options.data):(t=t||Ka.deepCopy(this.options.data),this.options.data=Array.isArray(t)?t:t[this.options.dataField]),this.data=l(this.options.data),this.options.sortReset&&(this.unsortedData=l(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var t=this,e=this.options.sortName,i="desc"===this.options.sortOrder?-1:1,n=this.header.fields.indexOf(this.options.sortName),o=0;-1!==n?(this.options.sortStable&&this.data.forEach(function(t,e){t.hasOwnProperty("_position")||(t._position=e)}),this.options.customSort?Ka.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort(function(o,r){t.header.sortNames[n]&&(e=t.header.sortNames[n]);var a=Ka.getItemField(o,e,t.options.escape),s=Ka.getItemField(r,e,t.options.escape),l=Ka.calculateObjectValue(t.header,t.header.sorters[n],[a,s,o,r]);return void 0!==l?t.options.sortStable&&0===l?i*(o._position-r._position):i*l:Ka.sort(a,s,i,t.options.sortStable,o._position,r._position)}),void 0!==this.options.sortClass&&(clearTimeout(o),o=setTimeout(function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)},250))):this.options.sortReset&&(this.data=l(this.unsortedData))}},{key:"onSort",value:function(e){var i=e.type,n=e.currentTarget,o="keypress"===i?t(n):t(n).parent(),r=this.$header.find("th").eq(o.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===o.data("field")){var a=this.options.sortOrder;void 0===a?this.options.sortOrder="asc":"asc"===a?this.options.sortOrder="desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else{this.options.sortName=o.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===o.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order}return this.trigger("sort",this.options.sortName,this.options.sortOrder),o.add(r).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination&&this.options.serverSort?(this.options.pageNumber=1,void this.initServer(this.options.silentSort)):(this.initSort(),void this.initBody())}},{key:"initToolbar",value:function(){var e,i=this,o=this.options,r=[],a=0,l=0;this.$toolbar.find(".bs-bars").children().length&&t("body").append(t(o.toolbar)),this.$toolbar.html(""),("string"==typeof o.toolbar||"object"===n(o.toolbar))&&t(Ka.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,o.toolbarAlign)).appendTo(this.$toolbar).append(t(o.toolbar)),r=['<div class="'.concat(["columns","columns-".concat(o.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(o.buttonsAlign)].join(" "),'">')],"string"==typeof o.icons&&(o.icons=Ka.calculateObjectValue(null,o.icons)),"string"==typeof o.buttonsOrder&&(o.buttonsOrder=o.buttonsOrder.replace(/\[|\]| |'/g,"").toLowerCase().split(",")),this.buttons=Object.assign(this.buttons,{search:{text:o.formatSearch(),icon:o.icons.search,render:!1,event:this.toggleShowSearch,attributes:{"aria-label":o.formatShowSearch(),title:o.formatShowSearch()}},paginationSwitch:{text:o.pagination?o.formatPaginationSwitchUp():o.formatPaginationSwitchDown(),icon:o.pagination?o.icons.paginationSwitchDown:o.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":o.formatPaginationSwitch(),title:o.formatPaginationSwitch()}},refresh:{text:o.formatRefresh(),icon:o.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":o.formatRefresh(),title:o.formatRefresh()}},toggle:{text:o.formatToggle(),icon:o.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":o.formatToggleOn(),title:o.formatToggleOn()}},fullscreen:{text:o.formatFullscreen(),icon:o.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":o.formatFullscreen(),title:o.formatFullscreen()}},columns:{render:!1,html:function Z(){var Z=[];if(Z.push('<div class="keep-open '.concat(i.constants.classes.buttonsDropdown,'" title="').concat(o.formatColumns(),'">\n            <button class="').concat(i.constants.buttonsClass,' dropdown-toggle" type="button" data-toggle="dropdown"\n            aria-label="Columns" title="').concat(o.formatColumns(),'">\n            ').concat(o.showButtonIcons?Ka.sprintf(i.constants.html.icon,o.iconsPrefix,o.icons.columns):"","\n            ").concat(o.showButtonText?o.formatColumns():"","\n            ").concat(i.constants.html.dropdownCaret,"\n            </button>\n            ").concat(i.constants.html.toolbarDropdown[0])),o.showColumnsSearch&&(Z.push(Ka.sprintf(i.constants.html.toolbarDropdownItem,Ka.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',i.constants.classes.input,o.formatSearch()))),Z.push(i.constants.html.toolbarDropdownSeparator)),o.showColumnsToggleAll){var t=i.getVisibleColumns().length===i.columns.filter(function(t){return !i.isSelectionColumn(t)}).length;Z.push(Ka.sprintf(i.constants.html.toolbarDropdownItem,Ka.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',t?'checked="checked"':"",o.formatColumnsToggleAll()))),Z.push(i.constants.html.toolbarDropdownSeparator)}var e=0;return i.columns.forEach(function(t,i){t.visible&&e++}),i.columns.forEach(function(t,n){if(!i.isSelectionColumn(t)&&(!o.cardView||t.cardVisible)&&!t.ignore){var r=t.visible?' checked="checked"':"",a=e<=o.minimumCountColumns&&r?' disabled="disabled"':"";t.switchable&&(Z.push(Ka.sprintf(i.constants.html.toolbarDropdownItem,Ka.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',t.field,n,r,a,t.title))),l++)}}),Z.push(i.constants.html.toolbarDropdown[1],"</div>"),Z.join("")}}});for(var c={},h=0,u=Object.entries(this.buttons);h<u.length;h++){var d=s(u[h],2),p=d[0],f=d[1],g=void 0;if(f.hasOwnProperty("html")){g=Ka.calculateObjectValue(o,f.html)}else{if(g='<button class="'.concat(this.constants.buttonsClass,'" type="button" name="').concat(p,'"'),f.hasOwnProperty("attributes")){for(var v=0,b=Object.entries(f.attributes);v<b.length;v++){var m=s(b[v],2),y=m[0],w=m[1];g+=" ".concat(y,'="').concat(Ka.calculateObjectValue(o,w),'"')}}if(g+=">",o.showButtonIcons&&f.hasOwnProperty("icon")){var S=Ka.calculateObjectValue(o,f.icon);g+=Ka.sprintf(this.constants.html.icon,o.iconsPrefix,S)+" "}o.showButtonText&&f.hasOwnProperty("text")&&(g+=Ka.calculateObjectValue(o,f.text)),g+="</button>"}c[p]=g;var x="show".concat(p.charAt(0).toUpperCase()).concat(p.substring(1)),k=o[x];!(!f.hasOwnProperty("render")||f.hasOwnProperty("render")&&f.render)||void 0!==k&&k!==!0||(o[x]=!0),o.buttonsOrder.includes(p)||o.buttonsOrder.push(p)}var O=!0,C=!1,P=void 0;try{for(var T,I=o.buttonsOrder[Symbol.iterator]();!(O=(T=I.next()).done);O=!0){var A=T.value,$=o["show".concat(A.charAt(0).toUpperCase()).concat(A.substring(1))];$&&r.push(c[A])}}catch(R){C=!0,P=R}finally{try{O||null==I["return"]||I["return"]()}finally{if(C){throw P}}}r.push("</div>"),(this.showToolbar||r.length>2)&&this.$toolbar.append(r.join("")),o.showSearch&&this.$toolbar.find('button[name="showSearch"]').off("click").on("click",function(){return i.toggleShowSearch()});for(var E=0,j=Object.entries(this.buttons);E<j.length;E++){var _=s(j[E],2),N=_[0],F=_[1];if(F.hasOwnProperty("event")){if("function"==typeof F.event||"string"==typeof F.event){var V=function(){var t="string"==typeof F.event?window[F.event]:F.event;return i.$toolbar.find('button[name="'.concat(N,'"]')).off("click").on("click",function(){return t.call(i)}),"continue"}();if("continue"===V){continue}}for(var D=function(){var t=s(L[B],2),e=t[0],n=t[1],o="string"==typeof n?window[n]:n;i.$toolbar.find('button[name="'.concat(N,'"]')).off(e).on(e,function(){return o.call(i)})},B=0,L=Object.entries(F.event);B<L.length;B++){D()}}}if(o.showColumns){e=this.$toolbar.find(".keep-open");var H=e.find('input[type="checkbox"]:not(".toggle-all")'),M=e.find('input[type="checkbox"].toggle-all');if(l<=o.minimumCountColumns&&e.find("input").prop("disabled",!0),e.find("li, label").off("click").on("click",function(t){t.stopImmediatePropagation()}),H.off("click").on("click",function(e){var n=e.currentTarget,o=t(n);i._toggleColumn(o.val(),o.prop("checked"),!1),i.trigger("column-switch",o.data("field"),o.prop("checked")),M.prop("checked",H.filter(":checked").length===i.columns.filter(function(t){return !i.isSelectionColumn(t)}).length)}),M.off("click").on("click",function(e){var n=e.currentTarget;i._toggleAllColumns(t(n).prop("checked"))}),o.showColumnsSearch){var U=e.find('[name="columnsSearch"]'),q=e.find(".dropdown-item-marker");U.on("keyup paste change",function(e){var i=e.currentTarget,n=t(i),o=n.val().toLowerCase();q.show(),H.each(function(e,i){var n=t(i),r=n.parents(".dropdown-item-marker"),a=r.text().toLowerCase();a.includes(o)||r.hide()})})}}var z=function(t){var e="keyup drop blur mouseup";t.off(e).on(e,function(t){o.searchOnEnterKey&&13!==t.keyCode||[37,38,39,40].includes(t.keyCode)||(clearTimeout(a),a=setTimeout(function(){i.onSearch({currentTarget:t.currentTarget})},o.searchTimeOut))})};if((o.search||this.showSearchClearButton)&&"string"!=typeof o.searchSelector){r=[];var W=Ka.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,o.formatSearch(),o.showButtonIcons?Ka.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.search):"",o.showButtonText?o.formatSearch():""),G=Ka.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,o.formatClearSearch(),o.showButtonIcons?Ka.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.clearSearch):"",o.showButtonText?o.formatClearSearch():""),K='<input class="'.concat(this.constants.classes.input,"\n        ").concat(Ka.sprintf(" %s%s",this.constants.classes.inputPrefix,o.iconSize),'\n        search-input" type="search" placeholder="').concat(o.formatSearch(),'" autocomplete="off">'),Y=K;if(o.showSearchButton||o.showSearchClearButton){var J=(o.showSearchButton?W:"")+(o.showSearchClearButton?G:"");Y=o.search?Ka.sprintf(this.constants.html.inputGroup,K,J):J}r.push(Ka.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(o.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),Y)),this.$toolbar.append(r.join(""));var X=Ka.getSearchInput(this);o.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",function(t){clearTimeout(a),a=setTimeout(function(){i.onSearch({currentTarget:X})},o.searchTimeOut)}),o.searchOnEnterKey&&z(X)):z(X),o.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click(function(){i.resetSearch()})}else{if("string"==typeof o.searchSelector){var Q=Ka.getSearchInput(this);z(Q)}}}},{key:"onSearch",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.currentTarget,n=e.firedByInitSearchText,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:!0;if(void 0!==i&&t(i).length&&o){var r=t(i).val().trim();if(this.options.trimOnSearch&&t(i).val()!==r&&t(i).val(r),this.searchText===r){return}(i===Ka.getSearchInput(this)[0]||t(i).hasClass("search-input"))&&(this.searchText=r,this.options.searchText=r)}n||(this.options.pageNumber=1),this.initSearch(),n?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var t=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch){return this.data=Ka.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),void (this.options.sortReset&&(this.unsortedData=l(this.data)))}var e=this.searchText&&(this.fromHtml?Ka.escapeHTML(this.searchText):this.searchText).toLowerCase(),i=Ka.isEmptyObject(this.filterColumns)?null:this.filterColumns;"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter(function(e,n){return t.filterOptions.filterAlgorithm.apply(null,[e,i])}):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=i?this.options.data.filter(function(e,n){var o=t.filterOptions.filterAlgorithm;if("and"===o){for(var r in i){if(Array.isArray(i[r])&&!i[r].includes(e[r])||!Array.isArray(i[r])&&e[r]!==i[r]){return !1}}}else{if("or"===o){var a=!1;for(var s in i){(Array.isArray(i[s])&&i[s].includes(e[s])||!Array.isArray(i[s])&&e[s]===i[s])&&(a=!0)}return a}}return !0}):l(this.options.data));var n=this.getVisibleFields();this.data=e?this.data.filter(function(i,o){for(var r=0;r<t.header.fields.length;r++){if(t.header.searchables[r]&&(!t.options.visibleSearch||-1!==n.indexOf(t.header.fields[r]))){var a=Ka.isNumeric(t.header.fields[r])?parseInt(t.header.fields[r],10):t.header.fields[r],s=t.columns[t.fieldsColumnsIndex[a]],l=void 0;if("string"==typeof a){l=i;for(var c=a.split("."),h=0;h<c.length;h++){null!==l[c[h]]&&(l=l[c[h]])}}else{l=i[a]}if(t.options.searchAccentNeutralise&&(l=Ka.normalizeAccent(l)),s&&s.searchFormatter&&(l=Ka.calculateObjectValue(s,t.header.formatters[r],[l,i,o,s.field],l)),"string"==typeof l||"number"==typeof l){if(t.options.strictSearch){if("".concat(l).toLowerCase()===e){return !0}}else{var u=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(\d+)?|(\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm,d=u.exec(e),p=!1;if(d){var f=d[1]||"".concat(d[5],"l"),g=d[2]||d[3],v=parseInt(l,10),b=parseInt(g,10);switch(f){case">":case"<l":p=v>b;break;case"<":case">l":p=b>v;break;case"<=":case"=<":case">=l":case"=>l":p=b>=v;break;case">=":case"=>":case"<=l":case"=<l":p=v>=b}}if(p||"".concat(l).toLowerCase().includes(e)){return !0}}}}}return !1}):this.data,this.options.sortReset&&(this.unsortedData=l(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var e=this,i=this.options;if(!i.pagination){return void this.$pagination.hide()}this.$pagination.show();var n,o,r,a,s,l,c,h=[],u=!1,d=this.getData({includeHiddenRows:!1}),p=i.pageList;if("string"==typeof p&&(p=p.replace(/\[|\]| /g,"").toLowerCase().split(",")),p=p.map(function(t){return"string"==typeof t?t.toLowerCase()===i.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?i.formatAllRows():+t:t}),this.paginationParts=i.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==i.sidePagination&&(i.totalRows=d.length),this.totalPages=0,i.totalRows&&(i.pageSize===i.formatAllRows()&&(i.pageSize=i.totalRows,u=!0),this.totalPages=~~((i.totalRows-1)/i.pageSize)+1,i.totalPages=this.totalPages),this.totalPages>0&&i.pageNumber>this.totalPages&&(i.pageNumber=this.totalPages),this.pageFrom=(i.pageNumber-1)*i.pageSize+1,this.pageTo=i.pageNumber*i.pageSize,this.pageTo>i.totalRows&&(this.pageTo=i.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&h.push('<div class="'.concat(this.constants.classes.pull,"-").concat(i.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var f=this.paginationParts.includes("pageInfoShort")?i.formatDetailPagination(i.totalRows):i.formatShowingRows(this.pageFrom,this.pageTo,i.totalRows,i.totalNotFiltered);h.push('<span class="pagination-info">\n      '.concat(f,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){h.push('<span class="page-list">');var g=['<span class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" data-toggle="dropdown">\n        <span class="page-size">\n        ').concat(u?i.formatAllRows():i.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];p.forEach(function(t,n){if(!i.smartDisplay||0===n||p[n-1]<i.totalRows){var o;o=u?t===i.formatAllRows()?e.constants.classes.dropdownActive:"":t===i.pageSize?e.constants.classes.dropdownActive:"",g.push(Ka.sprintf(e.constants.html.pageDropdownItem,o,t))}}),g.push("".concat(this.constants.html.pageDropdown[1],"</span>")),h.push(i.formatRecordsPerPage(g.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&h.push("</span></div>"),this.paginationParts.includes("pageList")){h.push('<div class="'.concat(this.constants.classes.pull,"-").concat(i.paginationHAlign,' pagination">'),Ka.sprintf(this.constants.html.pagination[0],Ka.sprintf(" pagination-%s",i.iconSize)),Ka.sprintf(this.constants.html.paginationItem," page-pre",i.formatSRPaginationPreText(),i.paginationPreText)),this.totalPages<i.paginationSuccessivelySize?(o=1,r=this.totalPages):(o=i.pageNumber-i.paginationPagesBySide,r=o+2*i.paginationPagesBySide),i.pageNumber<i.paginationSuccessivelySize-1&&(r=i.paginationSuccessivelySize),i.paginationSuccessivelySize>this.totalPages-o&&(o=o-(i.paginationSuccessivelySize-(this.totalPages-o))+1),1>o&&(o=1),r>this.totalPages&&(r=this.totalPages);var v=Math.round(i.paginationPagesBySide/2),b=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return Ka.sprintf(e.constants.html.paginationItem,n+(t===i.pageNumber?" ".concat(e.constants.classes.paginationActive):""),i.formatSRPaginationPageText(t),t)};if(o>1){var m=i.paginationPagesBySide;for(m>=o&&(m=o-1),n=1;m>=n;n++){h.push(b(n))}o-1===m+1?(n=o-1,h.push(b(n))):o-1>m&&(o-2*i.paginationPagesBySide>i.paginationPagesBySide&&i.paginationUseIntermediate?(n=Math.round((o-v)/2+v),h.push(b(n," page-intermediate"))):h.push(Ka.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(n=o;r>=n;n++){h.push(b(n))}if(this.totalPages>r){var y=this.totalPages-(i.paginationPagesBySide-1);for(r>=y&&(y=r+1),r+1===y-1?(n=r+1,h.push(b(n))):y>r+1&&(this.totalPages-r>2*i.paginationPagesBySide&&i.paginationUseIntermediate?(n=Math.round((this.totalPages-v-r)/2+r),h.push(b(n," page-intermediate"))):h.push(Ka.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),n=y;n<=this.totalPages;n++){h.push(b(n))}}h.push(Ka.sprintf(this.constants.html.paginationItem," page-next",i.formatSRPaginationNextText(),i.paginationNextText)),h.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(h.join(""));var w=["bottom","both"].includes(i.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";if(this.$pagination.last().find(".page-list > span").addClass(w),!i.onlyInfoPagination&&(a=this.$pagination.find(".page-list a"),s=this.$pagination.find(".page-pre"),l=this.$pagination.find(".page-next"),c=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),i.smartDisplay&&(p.length<2||i.totalRows<=p[0])&&this.$pagination.find("span.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),i.paginationLoop||(1===i.pageNumber&&s.addClass("disabled"),i.pageNumber===this.totalPages&&l.addClass("disabled")),u&&(i.pageSize=i.formatAllRows()),a.off("click").on("click",function(t){return e.onPageListChange(t)}),s.off("click").on("click",function(t){return e.onPagePre(t)}),l.off("click").on("click",function(t){return e.onPageNext(t)}),c.off("click").on("click",function(t){return e.onPageNumber(t)}),this.options.showPageGo)){var S=this,x=this.$pagination.find("ul.pagination"),k=x.find("li.pageGo");k.length||(k=t('<li class="pageGo">'+Ka.sprintf('<input type="text" class="form-control" value="%s">',this.options.pageNumber)+('<button class="btn'+Ka.sprintf(" btn-%s",this.constants.buttonsClass)+Ka.sprintf(" btn-%s",i.iconSize)+'" title="'+i.formatPageGo()+'"  type="button">'+i.formatPageGo())+"</button></li>").appendTo(x),k.find("button").click(function(){var t=parseInt(k.find("input").val())||1;(1>t||t>S.options.totalPages)&&(t=1),S.selectPage(t)}))}}},{key:"updatePagination",value:function(e){e&&t(e.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(e){e.preventDefault();var i=t(e.currentTarget);return i.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=i.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+i.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(e),!1}},{key:"onPagePre",value:function(t){return t.preventDefault(),this.options.pageNumber-1===0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1}},{key:"onPageNext",value:function(t){return t.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1}},{key:"onPageNumber",value:function(e){return e.preventDefault(),this.options.pageNumber!==+t(e.currentTarget).text()?(this.options.pageNumber=+t(e.currentTarget).text(),this.updatePagination(e),!1):void 0}},{key:"initRow",value:function(t,e,i,o){var r=this,a=[],l={},c=[],h="",u={},d=[];if(!(Ka.findIndex(this.hiddenRows,t)>-1)){if(l=Ka.calculateObjectValue(this.options,this.options.rowStyle,[t,e],l),l&&l.css){for(var p=0,f=Object.entries(l.css);p<f.length;p++){var g=s(f[p],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}}if(u=Ka.calculateObjectValue(this.options,this.options.rowAttributes,[t,e],u)){for(var m=0,y=Object.entries(u);m<y.length;m++){var w=s(y[m],2),S=w[0],x=w[1];d.push("".concat(S,'="').concat(Ka.escapeHTML(x),'"'))}}if(t._data&&!Ka.isEmptyObject(t._data)){for(var k=0,O=Object.entries(t._data);k<O.length;k++){var C=s(O[k],2),P=C[0],T=C[1];if("index"===P){return}h+=" data-".concat(P,"='").concat("object"===n(T)?JSON.stringify(T):T,"'")}}a.push("<tr",Ka.sprintf(" %s",d.length?d.join(" "):void 0),Ka.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),Ka.sprintf(' class="%s"',l.classes||(Array.isArray(t)?void 0:t._class)),Ka.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(e,'"'),Ka.sprintf(' data-uniqueid="%s"',Ka.getItemField(t,this.options.uniqueId,!1)),Ka.sprintf(' data-has-detail-view="%s"',this.options.detailView&&Ka.calculateObjectValue(null,this.options.detailFilter,[e,t])?"true":void 0),Ka.sprintf("%s",h),">"),this.options.cardView&&a.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var I="";return Ka.hasDetailViewIcon(this.options)&&(I="<td>",Ka.calculateObjectValue(null,this.options.detailFilter,[e,t])&&(I+='\n          <a class="detail-icon" href="#">\n          '.concat(Ka.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),I+="</td>"),I&&"right"!==this.options.detailViewAlign&&a.push(I),this.header.fields.forEach(function(i,n){var o="",l=Ka.getItemField(t,i,r.options.escape),h="",u="",d={},p="",f=r.header.classes[n],g="",v="",b="",m="",y="",w="",S=r.columns[n];if((!r.fromHtml&&!r.autoMergeCells||void 0!==l||S.checkbox||S.radio)&&S.visible&&(!r.options.cardView||S.cardVisible)){if(S.escape&&(l=Ka.escapeHTML(l)),c.concat([r.header.styles[n]]).length&&(v+="".concat(c.concat([r.header.styles[n]]).join("; "))),t["_".concat(i,"_style")]&&(v+="".concat(t["_".concat(i,"_style")])),v&&(g=' style="'.concat(v,'"')),t["_".concat(i,"_id")]&&(p=Ka.sprintf(' id="%s"',t["_".concat(i,"_id")])),t["_".concat(i,"_class")]&&(f=Ka.sprintf(' class="%s"',t["_".concat(i,"_class")])),t["_".concat(i,"_rowspan")]&&(m=Ka.sprintf(' rowspan="%s"',t["_".concat(i,"_rowspan")])),t["_".concat(i,"_colspan")]&&(y=Ka.sprintf(' colspan="%s"',t["_".concat(i,"_colspan")])),t["_".concat(i,"_title")]&&(w=Ka.sprintf(' title="%s"',t["_".concat(i,"_title")])),d=Ka.calculateObjectValue(r.header,r.header.cellStyles[n],[l,t,e,i],d),d.classes&&(f=' class="'.concat(d.classes,'"')),d.css){for(var x=[],k=0,O=Object.entries(d.css);k<O.length;k++){var C=s(O[k],2),P=C[0],T=C[1];x.push("".concat(P,": ").concat(T))}g=' style="'.concat(x.concat(r.header.styles[n]).join("; "),'"')}if(h=Ka.calculateObjectValue(S,r.header.formatters[n],[l,t,e,i],l),""!==r.searchText&&r.options.searchHighlight&&(h=Ka.calculateObjectValue(S,S.searchHighlightFormatter,[h,r.searchText],h.replace(RegExp("("+r.searchText+")","gim"),"<mark>$1</mark>"))),t["_".concat(i,"_data")]&&!Ka.isEmptyObject(t["_".concat(i,"_data")])){for(var I=0,A=Object.entries(t["_".concat(i,"_data")]);I<A.length;I++){var $=s(A[I],2),R=$[0],E=$[1];if("index"===R){return}b+=" data-".concat(R,'="').concat(E,'"')}}if(S.checkbox||S.radio){u=S.checkbox?"checkbox":u,u=S.radio?"radio":u;var j=S["class"]||"",_=Ka.isObject(h)&&h.hasOwnProperty("checked")?h.checked:(h===!0||l)&&h!==!1,N=!S.checkboxEnabled||h&&h.disabled;o=""+(r.options.cardView?'<div class="card-view '.concat(j,'">'):'<td class="bs-checkbox '.concat(j,'"').concat(f).concat(g,">"))+'<label>\n            <input\n            data-index="'.concat(e,'"\n            name="').concat(r.options.selectItemName,'"\n            type="').concat(u,'"\n            ').concat(Ka.sprintf('value="%s"',t[r.options.idField]),"\n            ").concat(Ka.sprintf('checked="%s"',_?"checked":void 0),"\n            ").concat(Ka.sprintf('disabled="%s"',N?"disabled":void 0)," />\n            <span></span>\n            </label>")+(r.header.formatters[n]&&"string"==typeof h?h:"")+(r.options.cardView?"</div>":"</td>"),t[r.header.stateField]=h===!0||!!l||h&&h.checked}else{if(h=void 0===h||null===h?r.options.undefinedText:h,r.options.cardView){var F=r.options.showHeader?'<span class="card-view-title"'.concat(g,">").concat(Ka.getFieldTitle(r.columns,i),"</span>"):"";o='<div class="card-view">'.concat(F,'<span class="card-view-value">').concat(h,"</span></div>"),r.options.smartDisplay&&""===h&&(o='<div class="card-view"></div>')}else{o="<td".concat(p).concat(f).concat(g).concat(b).concat(m).concat(y).concat(w,">").concat(h,"</td>")}}a.push(o)}}),I&&"right"===this.options.detailViewAlign&&a.push(I),this.options.cardView&&a.push("</div></td>"),a.push("</tr>"),a.join("")}}},{key:"initBody",value:function(e){var i=this,n=this.getData();this.trigger("pre-body",n),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=t("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=n.length);var o=[],r=t(document.createDocumentFragment()),a=!1;this.autoMergeCells=Ka.checkAutoMergeCells(n.slice(this.pageFrom-1,this.pageTo));for(var s=this.pageFrom-1;s<this.pageTo;s++){var l=n[s],c=this.initRow(l,s,n,r);a=a||!!c,c&&"string"==typeof c&&(this.options.virtualScroll?o.push(c):r.append(c))}a?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new Xa({rows:o,fixedScroll:e,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(){i.fitHeader(),i.initBodyEvent()}})):this.$body.html(r):this.$body.html('<tr class="no-records-found">'.concat(Ka.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+Ka.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),e||this.scrollTo(0),this.initBodyEvent(),this.updateSelected(),this.initFooter(),this.resetView(),"server"!==this.options.sidePagination&&(this.options.totalRows=n.length),this.trigger("post-body",n)}},{key:"initBodyEvent",value:function(){var e=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(i){var n=t(i.currentTarget),o=n.parent(),r=t(i.target).parents(".card-views").children(),a=t(i.target).parents(".card-view"),s=o.data("index"),l=e.data[s],c=e.options.cardView?r.index(a):n[0].cellIndex,h=e.getVisibleFields(),u=h[c-Ka.getDetailViewIndexOffset(e.options)],d=e.columns[e.fieldsColumnsIndex[u]],p=Ka.getItemField(l,u,e.options.escape);if(!n.find(".detail-icon").length){if(e.trigger("click"===i.type?"click-cell":"dbl-click-cell",u,p,l,n),e.trigger("click"===i.type?"click-row":"dbl-click-row",l,o,u),"click"===i.type&&e.options.clickToSelect&&d.clickToSelect&&!Ka.calculateObjectValue(e.options,e.options.ignoreClickToSelectOn,[i.target])){var f=o.find(Ka.sprintf('[name="%s"]',e.options.selectItemName));f.length&&f[0].click()}"click"===i.type&&e.options.detailViewByClick&&e.toggleDetailView(s,e.header.detailFormatters[e.fieldsColumnsIndex[u]])}}).off("mousedown").on("mousedown",function(t){e.multipleSelectRowCtrlKey=t.ctrlKey||t.metaKey,e.multipleSelectRowShiftKey=t.shiftKey}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(i){return i.preventDefault(),e.toggleDetailView(t(i.currentTarget).parent().parent().data("index")),!1}),this.$selectItem=this.$body.find(Ka.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(i){i.stopImmediatePropagation();var n=t(i.currentTarget);e._toggleCheck(n.prop("checked"),n.data("index"))}),this.header.events.forEach(function(i,n){var o=i;if(o){"string"==typeof o&&(o=Ka.calculateObjectValue(null,o));var r=e.header.fields[n],a=e.getVisibleFields().indexOf(r);if(-1!==a){a+=Ka.getDetailViewIndexOffset(e.options);var s=function(i){if(!o.hasOwnProperty(i)){return"continue"}var n=o[i];e.$body.find(">tr:not(.no-records-found)").each(function(o,s){var l=t(s),c=l.find(e.options.cardView?".card-views>.card-view":">td").eq(a),h=i.indexOf(" "),u=i.substring(0,h),d=i.substring(h+1);c.find(d).off(u).on(u,function(t){var i=l.data("index"),o=e.data[i],a=o[r];n.apply(e,[t,a,o,i])})})};for(var l in o){s(l)}}}})}},{key:"initServer",value:function(e,i,n){var o=this,r={},a=this.header.fields.indexOf(this.options.sortName),s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[a]&&(s.sortName=this.header.sortNames[a]),this.options.pagination&&"server"===this.options.sidePagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),!this.options.firstLoad&&!firstLoadTable.includes(this.options.id)){return void firstLoadTable.push(this.options.id)}if(n||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,0===s.limit&&delete s.limit)),this.options.search&&"server"===this.options.sidePagination&&this.columns.filter(function(t){return !t.searchable}).length){s.searchable=[];var l=!0,c=!1,h=void 0;try{for(var u,d=this.columns[Symbol.iterator]();!(l=(u=d.next()).done);l=!0){var p=u.value;!p.checkbox&&p.searchable&&(this.options.visibleSearch&&p.visible||!this.options.visibleSearch)&&s.searchable.push(p.field)}}catch(f){c=!0,h=f}finally{try{l||null==d["return"]||d["return"]()}finally{if(c){throw h}}}}if(Ka.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),t.extend(s,i||{}),r=Ka.calculateObjectValue(this.options,this.options.queryParams,[s],r),r!==!1){e||this.showLoading();var g=t.extend({},Ka.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(r):r,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(t,i,n){var r=Ka.calculateObjectValue(o.options,o.options.responseHandler,[t,n],t);o.load(r),o.trigger("load-success",r,n&&n.status,n),e||o.hideLoading(),"server"===o.options.sidePagination&&r[o.options.totalField]>0&&!r[o.options.dataField].length&&o.updatePagination()},error:function(t){var i=[];"server"===o.options.sidePagination&&(i={},i[o.options.totalField]=0,i[o.options.dataField]=[]),o.load(i),o.trigger("load-error",t&&t.status,t),e||o.$tableLoading.hide()}});return this.options.ajax?Ka.calculateObjectValue(this,this.options.ajax,[g],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=t.ajax(g)),r}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var t=Ka.getSearchInput(this);t.val(this.options.searchText),this.onSearch({currentTarget:t,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var e=this;this.$header.find("th").each(function(i,n){t(n).find(".sortable").removeClass("desc asc").addClass(t(n).data("field")===e.options.sortName?e.options.sortOrder:"both")})}},{key:"updateSelected",value:function(){var e=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",e),this.$selectItem.each(function(e,i){t(i).closest("tr")[t(i).prop("checked")?"addClass":"removeClass"]("selected")
})}},{key:"updateRows",value:function(){var e=this;this.$selectItem.each(function(i,n){e.data[t(n).data("index")][e.header.stateField]=t(n).prop("checked")})}},{key:"resetRows",value:function(){var t=!0,e=!1,i=void 0;try{for(var n,o=this.data[Symbol.iterator]();!(t=(n=o.next()).done);t=!0){var r=n.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(r[this.header.stateField]=!1)}}catch(a){e=!0,i=a}finally{try{t||null==o["return"]||o["return"]()}finally{if(e){throw i}}}this.initHiddenRows()}},{key:"trigger",value:function(i){for(var n,o,r="".concat(i,".bs.table"),a=arguments.length,s=Array(a>1?a-1:0),l=1;a>l;l++){s[l-1]=arguments[l]}(n=this.options)[e.EVENTS[r]].apply(n,[].concat(s,[this])),this.$el.trigger(t.Event(r,{sender:this}),s),(o=this.options).onAll.apply(o,[r].concat([].concat(s,[this]))),this.$el.trigger(t.Event("all.bs.table",{sender:this}),[r,s])}},{key:"resetHeader",value:function(){var t=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(function(){return t.fitHeader()},this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var e=this;if(this.$el.is(":hidden")){return void (this.timeoutId_=setTimeout(function(){return e.fitHeader()},100))}var i=this.$tableBody.get(0),n=i.scrollWidth>i.clientWidth&&i.scrollHeight>i.clientHeight+this.$header.outerHeight()?Ka.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var o=t(":focus");if(o.length>0){var r=o.parents("th");if(r.length>0){var a=r.attr("data-field");if(void 0!==a){var s=this.$header.find("[data-field='".concat(a,"']"));s.length>0&&s.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var l=t(".focus-temp:visible:eq(0)");l.length>0&&(l.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(i,n){e.$header_.find(Ka.sprintf('th[data-field="%s"]',t(n).data("field"))).data(t(n).data())});for(var c=this.getVisibleFields(),h=this.$header_.find("th"),u=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);u.length&&u.find('>td[colspan]:not([colspan="1"])').length;){u=u.next()}var d=u.find("> *").length;u.find("> *").each(function(i,n){var o=t(n);if(Ka.hasDetailViewIcon(e.options)&&(0===i&&"right"!==e.options.detailViewAlign||i===d-1&&"right"===e.options.detailViewAlign)){var r=h.filter(".detail"),a=r.innerWidth()-r.find(".fht-cell").width();return void r.find(".fht-cell").width(o.innerWidth()-a)}var s=i-Ka.getDetailViewIndexOffset(e.options),l=e.$header_.find(Ka.sprintf('th[data-field="%s"]',c[s]));l.length>1&&(l=t(h[o[0].cellIndex]));var u=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(o.innerWidth()-u)}),this.horizontalScroll(),this.trigger("post-header")}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var t=this.getData(),e=[],i="";Ka.hasDetailViewIcon(this.options)&&(i='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),i&&"right"!==this.options.detailViewAlign&&e.push(i);var n=!0,o=!1,r=void 0;try{for(var a,l=this.columns[Symbol.iterator]();!(n=(a=l.next()).done);n=!0){var c=a.value,h="",u="",d=[],p={},f=Ka.sprintf(' class="%s"',c["class"]);if(c.visible&&(!(this.footerData&&this.footerData.length>0)||c.field in this.footerData[0])){if(this.options.cardView&&!c.cardVisible){return}if(h=Ka.sprintf("text-align: %s; ",c.falign?c.falign:c.align),u=Ka.sprintf("vertical-align: %s; ",c.valign),p=Ka.calculateObjectValue(null,this.options.footerStyle,[c]),p&&p.css){for(var g=0,v=Object.entries(p.css);g<v.length;g++){var b=s(v[g],2),m=b[0],y=b[1];d.push("".concat(m,": ").concat(y))}}p&&p.classes&&(f=Ka.sprintf(' class="%s"',c["class"]?[c["class"],p.classes].join(" "):p.classes)),e.push("<th",f,Ka.sprintf(' style="%s"',h+u+d.concat().join("; ")));var w=0;this.footerData&&this.footerData.length>0&&(w=this.footerData[0]["_"+c.field+"_colspan"]||0),w&&e.push(' colspan="'.concat(w,'" ')),e.push(">"),e.push('<div class="th-inner">');var S="";this.footerData&&this.footerData.length>0&&(S=this.footerData[0][c.field]||""),e.push(Ka.calculateObjectValue(c,c.footerFormatter,[t,S],S)),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}}}catch(x){o=!0,r=x}finally{try{n||null==l["return"]||l["return"]()}finally{if(o){throw r}}}i&&"right"===this.options.detailViewAlign&&e.push(i),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").html(e.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var e=this;if(this.$el.is(":hidden")){return void setTimeout(function(){return e.fitFooter()},100)}var i=this.$tableBody.get(0),n=i.scrollWidth>i.clientWidth&&i.scrollHeight>i.clientHeight+this.$header.outerHeight()?Ka.getScrollBarWidth():0;this.$tableFooter.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var o=(this.getVisibleFields(),this.$tableFooter.find("th")),r=this.$body.find(">tr:first-child:not(.no-records-found)");for(o.find(".fht-cell").width("auto");r.length&&r.find('>td[colspan]:not([colspan="1"])').length;){r=r.next()}var a=r.find("> *").length;r.find("> *").each(function(i,n){var r=t(n);if(Ka.hasDetailViewIcon(e.options)&&(0===i&&"left"===e.options.detailViewAlign||i===a-1&&"right"===e.options.detailViewAlign)){var s=o.filter(".detail"),l=s.innerWidth()-s.find(".fht-cell").width();return void s.find(".fht-cell").width(r.innerWidth()-l)}var c=o.eq(i),h=c.innerWidth()-c.find(".fht-cell").width();c.find(".fht-cell").width(r.innerWidth()-h)}),this.horizontalScroll()}},{key:"horizontalScroll",value:function(){var t=this;this.$tableBody.off("scroll").on("scroll",function(){var e=t.$tableBody.scrollLeft();t.options.showHeader&&t.options.height&&t.$tableHeader.scrollLeft(e),t.options.showFooter&&!t.options.cardView&&t.$tableFooter.scrollLeft(e),t.trigger("scroll-body",t.$tableBody)})}},{key:"getVisibleFields",value:function(){var t=[],e=!0,i=!1,n=void 0;try{for(var o,r=this.header.fields[Symbol.iterator]();!(e=(o=r.next()).done);e=!0){var a=o.value,s=this.columns[this.fieldsColumnsIndex[a]];s&&s.visible&&t.push(a)}}catch(l){i=!0,n=l}finally{try{e||null==r["return"]||r["return"]()}finally{if(i){throw n}}}return t}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var e=t.extend({},this.options);return delete e.data,t.extend(!0,{},e)}},{key:"refreshOptions",value:function(e){Ka.compareObjects(this.options,e,!0)||(this.options=t.extend(this.options,e),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(t){var e=this,i=this.options.data;if(!this.searchText&&!this.options.customSearch&&void 0===this.options.sortName&&Ka.isEmptyObject(this.filterColumns)&&Ka.isEmptyObject(this.filterColumnsPartial)||t&&t.unfiltered||(i=this.data),t&&t.useCurrentPage&&(i=i.slice(this.pageFrom-1,this.pageTo)),t&&!t.includeHiddenRows){var n=this.getHiddenRows();i=i.filter(function(t){return -1===Ka.findIndex(n,t)})}return t&&t.formatted&&i.forEach(function(t){for(var i=0,n=Object.entries(t);i<n.length;i++){var o=s(n[i],2),r=o[0],a=o[1],l=e.columns[e.fieldsColumnsIndex[r]];if(!l){return}t[r]=Ka.calculateObjectValue(l,e.header.formatters[l.fieldIndex],[a,t,t.index,l.field],a)}}),i}},{key:"getSelections",value:function(){var t=this;return this.options.data.filter(function(e){return e[t.header.stateField]===!0})}},{key:"load",value:function(t){var e=!1,i=t;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=i[this.options.totalField],this.options.totalNotFiltered=i[this.options.totalNotFilteredField],this.footerData=i[this.options.footerField]?[i[this.options.footerField]]:void 0),e=i.fixedScroll,i=Array.isArray(i)?i:i[this.options.dataField],this.initData(i),this.initSearch(),this.initPagination(),this.initBody(e)}},{key:"append",value:function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(t){var e,i,n=this.options.data.length;if(t.hasOwnProperty("field")&&t.hasOwnProperty("values")){for(e=n-1;e>=0;e--){var o=!1;i=this.options.data[e],(i.hasOwnProperty(t.field)||"$index"===t.field)&&(o=i.hasOwnProperty(t.field)||"$index"!==t.field?t.values.includes(i[t.field]):t.values.includes(e),o&&(this.options.data.splice(e,1),"server"===this.options.sidePagination&&(this.options.totalRows-=1)))}n!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.options.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(e){var i=Array.isArray(e)?e:[e],n=!0,o=!1,r=void 0;try{for(var a,s=i[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var l=a.value;l.hasOwnProperty("index")&&l.hasOwnProperty("row")&&(l.hasOwnProperty("replace")&&l.replace?this.options.data[l.index]=l.row:t.extend(this.options.data[l.index],l.row))}}catch(c){o=!0,r=c}finally{try{n||null==s["return"]||s["return"]()}finally{if(o){throw r}}}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(t){var e,i,n,o=this.options.uniqueId,r=this.options.data.length,a=t,s=null;for(e=r-1;e>=0;e--){if(i=this.options.data[e],i.hasOwnProperty(o)){n=i[o]}else{if(!i._data||!i._data.hasOwnProperty(o)){continue}n=i._data[o]}if("string"==typeof n?a=""+a:"number"==typeof n&&(+n===n&&n%1===0?a=parseInt(a):n===+n&&0!==n&&(a=parseFloat(a))),n===a){s=i;break}}return s}},{key:"updateByUniqueId",value:function(e){var i=Array.isArray(e)?e:[e],n=!0,o=!1,r=void 0;try{for(var a,s=i[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var l=a.value;if(l.hasOwnProperty("id")&&l.hasOwnProperty("row")){var c=this.options.data.indexOf(this.getRowByUniqueId(l.id));-1!==c&&(l.hasOwnProperty("replace")&&l.replace?this.options.data[c]=l.row:t.extend(this.options.data[c],l.row))}}}catch(h){o=!0,r=h}finally{try{n||null==s["return"]||s["return"]()}finally{if(o){throw r}}}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"removeByUniqueId",value:function(t){var e=this.options.data.length,i=this.getRowByUniqueId(t);i&&this.options.data.splice(this.options.data.indexOf(i),1),e!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"updateCell",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,t.reinit!==!1&&(this.initSort(),this.initBody(!0)))}},{key:"updateCellByUniqueId",value:function(t){var e=this,i=Array.isArray(t)?t:[t];i.forEach(function(t){var i=t.id,n=t.field,o=t.value,r=e.options.data.indexOf(e.getRowByUniqueId(i));-1!==r&&(e.options.data[r][n]=o)}),t.reinit!==!1&&(this.initSort(),this.initBody(!0))}},{key:"showRow",value:function(t){this._toggleRow(t,!0)}},{key:"hideRow",value:function(t){this._toggleRow(t,!1)}},{key:"_toggleRow",value:function(t,e){var i;if(t.hasOwnProperty("index")?i=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(t.uniqueId)),i){var n=Ka.findIndex(this.hiddenRows,i);e||-1!==n?e&&n>-1&&this.hiddenRows.splice(n,1):this.hiddenRows.push(i),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(t){if(t){return this.initHiddenRows(),this.initBody(!0),void this.initPagination()}var e=this.getData(),i=[],n=!0,o=!1,r=void 0;try{for(var a,s=e[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var l=a.value;this.hiddenRows.includes(l)&&i.push(l)}}catch(c){o=!0,r=c}finally{try{n||null==s["return"]||s["return"]()}finally{if(o){throw r}}}return this.hiddenRows=i,i}},{key:"showColumn",value:function(t){var e=this,i=Array.isArray(t)?t:[t];i.forEach(function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!0,!0)})}},{key:"hideColumn",value:function(t){var e=this,i=Array.isArray(t)?t:[t];i.forEach(function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!1,!0)})}},{key:"_toggleColumn",value:function(t,e,i){if(-1!==t&&this.columns[t].visible!==e&&(this.columns[t].visible=e,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var n=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);i&&n.filter(Ka.sprintf('[value="%s"]',t)).prop("checked",e),n.filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var t=this;return this.columns.filter(function(e){return e.visible&&!t.isSelectionColumn(e)})}},{key:"getHiddenColumns",value:function(){return this.columns.filter(function(t){var e=t.visible;return !e})}},{key:"isSelectionColumn",value:function(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(e){var i=this,n=!0,o=!1,r=void 0;try{for(var a,s=this.columns.slice().reverse()[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var l=a.value;if(l.switchable){if(!e&&this.options.showColumns&&this.getVisibleColumns().length===this.options.minimumCountColumns){continue}l.visible=e}}}catch(c){o=!0,r=c}finally{try{n||null==s["return"]||s["return"]()}finally{if(o){throw r}}}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var h=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);e?h.prop("checked",e):h.get().reverse().forEach(function(n){h.filter(":checked").length>i.options.minimumCountColumns&&t(n).prop("checked",e)}),h.filter(":checked").length<=this.options.minimumCountColumns&&h.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(t){var e,i,n=t.index,o=this.getVisibleFields().indexOf(t.field),r=t.rowspan||1,a=t.colspan||1,s=this.$body.find(">tr");o+=Ka.getDetailViewIndexOffset(this.options);var l=s.eq(n).find(">td").eq(o);if(!(0>n||0>o||n>=this.data.length)){for(e=n;n+r>e;e++){for(i=o;o+a>i;i++){s.eq(e).find(">td").eq(i).hide()}}l.attr("rowspan",r).attr("colspan",a).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),this.updateSelected();var i=this.getSelections();return t?void this.trigger("check-all",i,e):void this.trigger("uncheck-all",i,e)}},{key:"checkInvert",value:function(){var e=this.$selectItem.filter(":enabled"),i=e.filter(":checked");e.each(function(e,i){t(i).prop("checked",!t(i).prop("checked"))}),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",i),i=this.getSelections(),this.trigger("check-some",i)}},{key:"check",value:function(t){this._toggleCheck(!0,t)}},{key:"uncheck",value:function(t){this._toggleCheck(!1,t)}},{key:"_toggleCheck",value:function(t,e){var i=this.$selectItem.filter('[data-index="'.concat(e,'"]')),n=this.options.data[e];if(i.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var o=!0,r=!1,a=void 0;try{for(var s,l=this.options.data[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;c[this.header.stateField]=!1}}catch(h){r=!0,a=h}finally{try{o||null==l["return"]||l["return"]()}finally{if(r){throw a}}}this.$selectItem.filter(":checked").not(i).prop("checked",!1)}if(n[this.header.stateField]=t,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0){for(var u=[this.multipleSelectRowLastSelectedIndex,e].sort(),d=u[0]+1;d<u[1];d++){this.data[d][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(d,'"]')).prop("checked",!0)}}this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=t?e:-1}i.prop("checked",t),this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[e],i)}},{key:"checkBy",value:function(t){this._toggleCheckBy(!0,t)}},{key:"uncheckBy",value:function(t){this._toggleCheckBy(!1,t)}},{key:"_toggleCheckBy",value:function(t,e){var i=this;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){var n=[];this.data.forEach(function(o,r){if(!o.hasOwnProperty(e.field)){return !1}if(e.values.includes(o[e.field])){var a=i.$selectItem.filter(":enabled").filter(Ka.sprintf('[data-index="%s"]',r));if(a=t?a.not(":checked"):a.filter(":checked"),!a.length){return}a.prop("checked",t),o[i.header.stateField]=t,n.push(o),i.trigger(t?"check":"uncheck",o,a)}}),this.updateSelected(),this.trigger(t?"check-some":"uncheck-some",n)}}},{key:"refresh",value:function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),table.rememberSelecteds={},table.rememberSelectedIds={},this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),t(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")}},{key:"resetView",value:function(t){var e=0;if(t&&t.height&&(this.options.height=t.height),this.$selectAll.prop("checked",this.$selectItem.length>0&&this.$selectItem.length===this.$selectItem.filter(":checked").length),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(e+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen")){this.$tableContainer.css("height",""),this.$tableContainer.css("width","")}else{if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var i=this.$toolbar.outerHeight(!0),n=this.$pagination.outerHeight(!0),o=this.options.height-i-n,r=this.$tableBody.find(">table"),a=r.outerHeight();if(this.$tableContainer.css("height","".concat(o,"px")),this.$tableBorder&&r.is(":visible")){var s=o-a-2;this.$tableBody[0].scrollWidth-this.$tableBody.innerWidth()&&(s-=Ka.getScrollBarWidth()),this.$tableBorder.css("width","".concat(r.outerWidth(),"px")),this.$tableBorder.css("height","".concat(s,"px"))}}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(e,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var t=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(t=0.04*this.$tableLoading.width(),t=Math.max(12,t),t=Math.min(32,t),t="".concat(t,"px")),this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"toggleShowSearch",value:function(){this.$el.parents(".select-table").siblings().slideToggle()}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html(Ka.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)+" "+e),this.updatePagination()}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html(Ka.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)+" "+e),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(t){var e=Ka.getSearchInput(this);e.val(t||""),this.onSearch({currentTarget:e})}},{key:"filterBy",value:function(e,i){this.filterOptions=Ka.isEmptyObject(i)?this.options.filterOptions:t.extend(this.options.filterOptions,i),this.filterColumns=Ka.isEmptyObject(e)?{}:e,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function i(e){var o={unit:"px",value:0};"object"===n(e)?o=Object.assign(o,e):"string"==typeof e&&"bottom"===e?o.value=this.$tableBody[0].scrollHeight:("string"==typeof e||"number"==typeof e)&&(o.value=e);var i=o.value;"rows"===o.unit&&(i=0,this.$body.find("> tr:lt(".concat(o.value,")")).each(function(e,n){i+=t(n).outerHeight(!0)})),this.$tableBody.scrollTop(i)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(t){t>0&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(t,e){var i=this.$body.find(Ka.sprintf('> tr[data-index="%s"]',t));i.next().is("tr.detail-view")?this.collapseRow(t):this.expandRow(t,e),this.resetView()}},{key:"expandRow",value:function(t,e){var i=this.data[t],n=this.$body.find(Ka.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(!n.next().is("tr.detail-view")){this.options.detailViewIcon&&n.find("a.detail-icon").html(Ka.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),n.after(Ka.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',n.children("td").length));var o=n.next().find("td"),r=e||this.options.detailFormatter,a=Ka.calculateObjectValue(this.options,r,[t,i,o],"");1===o.length&&o.append(a),this.trigger("expand-row",t,i,o)}}},{key:"expandRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function(t){var e=this.data[t],i=this.$body.find(Ka.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));i.next().is("tr.detail-view")&&(this.options.detailViewIcon&&i.find("a.detail-icon").html(Ka.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",t,e,i.next()),i.next().remove())}},{key:"collapseRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function(){for(var e=this.$body.find("> tr[data-index][data-has-detail-view]"),i=0;i<e.length;i++){this.expandRow(t(e[i]).data("index"))}}},{key:"collapseAllRows",value:function(){for(var e=this.$body.find("> tr[data-index][data-has-detail-view]"),i=0;i<e.length;i++){this.collapseRow(t(e[i]).data("index"))}}},{key:"updateColumnTitle",value:function(e){if(e.hasOwnProperty("field")&&e.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[e.field]].title=this.options.escape?Ka.escapeHTML(e.title):e.title,this.columns[this.fieldsColumnsIndex[e.field]].visible)){var i=void 0!==this.options.height?this.$tableHeader:this.$header;i.find("th[data-field]").each(function(i,n){return t(n).data("field")===e.field?(t(t(n).find(".th-inner")[0]).text(e.title),!1):void 0})}}},{key:"updateFormatText",value:function(t,e){/^format/.test(t)&&this.options[t]&&("string"==typeof e?this.options[t]=function(){return e}:"function"==typeof e&&(this.options[t]=e),this.initToolbar(),this.initPagination(),this.initBody())}}]),e}();return Qa.VERSION=Fa.VERSION,Qa.DEFAULTS=Fa.DEFAULTS,Qa.LOCALES=Fa.LOCALES,Qa.COLUMN_DEFAULTS=Fa.COLUMN_DEFAULTS,Qa.METHODS=Fa.METHODS,Qa.EVENTS=Fa.EVENTS,t.BootstrapTable=Qa,t.fn.bootstrapTable=function(e){for(var i=arguments.length,o=Array(i>1?i-1:0),r=1;i>r;r++){o[r-1]=arguments[r]}var a;return this.each(function(i,r){var s=t(r).data("bootstrap.table"),l=t.extend({},Qa.DEFAULTS,t(r).data(),"object"===n(e)&&e);if("string"==typeof e){var c;if(!Fa.METHODS.includes(e)){throw Error("Unknown method: ".concat(e))}if(!s){return}a=(c=s)[e].apply(c,o),"destroy"===e&&t(r).removeData("bootstrap.table")}s||(s=new t.BootstrapTable(r,l),t(r).data("bootstrap.table",s),s.init())}),void 0===a?this:a},t.fn.bootstrapTable.Constructor=Qa,t.fn.bootstrapTable.theme=Fa.THEME,t.fn.bootstrapTable.VERSION=Fa.VERSION,t.fn.bootstrapTable.defaults=Qa.DEFAULTS,t.fn.bootstrapTable.columnDefaults=Qa.COLUMN_DEFAULTS,t.fn.bootstrapTable.events=Qa.EVENTS,t.fn.bootstrapTable.locales=Qa.LOCALES,t.fn.bootstrapTable.methods=Qa.METHODS,t.fn.bootstrapTable.utils=Ka,t(function(){t('[data-toggle="table"]').bootstrapTable()}),Qa});var TABLE_EVENTS="all.bs.table click-cell.bs.table dbl-click-cell.bs.table click-row.bs.table dbl-click-row.bs.table sort.bs.table check.bs.table uncheck.bs.table onUncheck check-all.bs.table uncheck-all.bs.table check-some.bs.table uncheck-some.bs.table load-success.bs.table load-error.bs.table column-switch.bs.table page-change.bs.table search.bs.table toggle.bs.table show-search.bs.table expand-row.bs.table collapse-row.bs.table refresh-options.bs.table reset-view.bs.table refresh.bs.table",firstLoadTable=[],union=function(t,e){return $.isPlainObject(e)?addRememberRow(t,e):$.isArray(e)?$.each(e,function(e,i){$.isPlainObject(i)?addRememberRow(t,i):-1==$.inArray(i,t)&&(t[t.length]=i)}):-1==$.inArray(e,t)&&(t[t.length]=e),t},difference=function(t,e){if($.isPlainObject(e)){removeRememberRow(t,e)}else{if($.isArray(e)){$.each(e,function(e,i){if($.isPlainObject(i)){removeRememberRow(t,i)}else{var n=$.inArray(i,t);-1!=n&&t.splice(n,1)}})}else{var i=$.inArray(e,t);-1!=i&&t.splice(i,1)}}return t},_={union:union,difference:difference};