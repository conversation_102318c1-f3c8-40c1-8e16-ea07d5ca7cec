package com.ruoyi.expense.domain;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 账单接收数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public class BillReceiveDTO {
    
    /**
     * 账单元数据
     */
    @JsonProperty("账单元数据")
    private BillMetadata metadata;
    
    /**
     * 账单数据，key为账单编号（如"账单1"），value为账单详情
     */
    @JsonIgnore
    private Map<String, BillDetail> bills = new LinkedHashMap<>();
    
    /**
     * 处理除"账单元数据"之外的所有字段
     */
    @JsonAnySetter
    public void handleDynamicField(String key, Map<String, Object> value) {
        if (!"账单元数据".equals(key)) {
            BillDetail billDetail = new BillDetail();
            
            // 处理账单相关数据
            // 从JSON数据中提取账单相关数据并转换为BillData对象
            // 1. 获取账单相关数据Map
            @SuppressWarnings("unchecked")
            Map<String, String> billDataMap = (Map<String, String>) value.get("账单相关数据");
            
            // 2. 如果存在账单数据，则进行转换
            if (billDataMap != null) {
                // 3. 创建新的BillData对象
                BillData billData = new BillData();
                
                // 4. 设置账单基本信息
                billData.setExpenseType(billDataMap.get("费用类型"));        // 设置费用类型
                billData.setBillingCycle(billDataMap.get("计费周期"));       // 设置计费周期
                billData.setDepartment(billDataMap.get("划账部门"));         // 设置划账部门
                billData.setTaxIncludingTotal(billDataMap.get("含税总价"));  // 设置含税总价
                billData.setTaxExcludingTotal(billDataMap.get("不含税总价"));// 设置不含税总价
                
                // 5. 将转换后的BillData对象设置到BillDetail中
                billDetail.setBillData(billData);
            }
            
            // 处理费用明细
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> expenseDetailsList = (List<Map<String, Object>>) value.get("费用明细");
            if (expenseDetailsList != null) {
                List<ExpenseDetail> expenseDetails = new java.util.ArrayList<>();
                for (Map<String, Object> detailMap : expenseDetailsList) {
                    ExpenseDetail detail = new ExpenseDetail();
                    detail.setExpenseName((String) detailMap.get("费用名称"));
                    detail.setNumber((String) detailMap.get("编号"));
                    detail.setBrand((String) detailMap.get("品牌"));
                    detail.setSpecification((String) detailMap.get("具体规格"));
                    detail.setRemark((String) detailMap.get("备注"));
                    detail.setExpenseChangeInfo((String) detailMap.get("费用变动情况"));
                    
                    // 安全处理数量字段的类型转换
                    Object quantityObj = detailMap.get("数量");
                    if (quantityObj != null) {
                        if (quantityObj instanceof Integer) {
                            detail.setQuantity((Integer) quantityObj);
                        } else if (quantityObj instanceof Double) {
                            detail.setQuantity(((Double) quantityObj).intValue());
                        } else if (quantityObj instanceof Number) {
                            detail.setQuantity(((Number) quantityObj).intValue());
                        } else {
                            detail.setQuantity(Integer.valueOf(quantityObj.toString()));
                        }
                    }
                    
                    // 处理BigDecimal类型的字段
                    Object taxIncludingPrice = detailMap.get("含税单价");
                    if (taxIncludingPrice != null) {
                        detail.setTaxIncludingPrice(new BigDecimal(taxIncludingPrice.toString()));
                    }
                    
                    Object taxExcludingPrice = detailMap.get("不含税单价");
                    if (taxExcludingPrice != null) {
                        detail.setTaxExcludingPrice(new BigDecimal(taxExcludingPrice.toString()));
                    }
                    
                    Object taxIncludingLineTotal = detailMap.get("含税单行总价");
                    if (taxIncludingLineTotal != null) {
                        detail.setTaxIncludingLineTotal(new BigDecimal(taxIncludingLineTotal.toString()));
                    }
                    
                    Object taxExcludingLineTotal = detailMap.get("不含税单行总价");
                    if (taxExcludingLineTotal != null) {
                        detail.setTaxExcludingLineTotal(new BigDecimal(taxExcludingLineTotal.toString()));
                    }
                    
                    expenseDetails.add(detail);
                }
                billDetail.setExpenseDetails(expenseDetails);
            }
            
            bills.put(key, billDetail);
        }
    }
    
    /**
     * 账单元数据类
     */
    public static class BillMetadata {
        /**
         * 账单数量
         */
        @JsonProperty("账单数量")
        private Integer billCount;
        
        /**
         * 发布人
         */
        @JsonProperty("发布人")
        private String publisher;

        public Integer getBillCount() {
            return billCount;
        }

        public void setBillCount(Integer billCount) {
            this.billCount = billCount;
        }

        public String getPublisher() {
            return publisher;
        }

        public void setPublisher(String publisher) {
            this.publisher = publisher;
        }
    }
    
    /**
     * 账单详情类
     */
    public static class BillDetail {
        /**
         * 账单相关数据
         */
        private BillData billData;
        
        /**
         * 费用明细列表
         */
        private List<ExpenseDetail> expenseDetails;

        public BillData getBillData() {
            return billData;
        }

        public void setBillData(BillData billData) {
            this.billData = billData;
        }

        public List<ExpenseDetail> getExpenseDetails() {
            return expenseDetails;
        }

        public void setExpenseDetails(List<ExpenseDetail> expenseDetails) {
            this.expenseDetails = expenseDetails;
        }
    }
    
    /**
     * 账单数据类
     */
    public static class BillData {
        /**
         * 费用类型
         */
        private String expenseType;
        
        /**
         * 计费周期
         */
        private String billingCycle;
        
        /**
         * 划账部门
         */
        private String department;
        
        /**
         * 含税总价
         */
        private String taxIncludingTotal;
        
        /**
         * 不含税总价
         */
        private String taxExcludingTotal;

        public String getExpenseType() {
            return expenseType;
        }

        public void setExpenseType(String expenseType) {
            this.expenseType = expenseType;
        }

        public String getBillingCycle() {
            return billingCycle;
        }

        public void setBillingCycle(String billingCycle) {
            this.billingCycle = billingCycle;
        }

        public String getDepartment() {
            return department;
        }

        public void setDepartment(String department) {
            this.department = department;
        }

        public String getTaxIncludingTotal() {
            return taxIncludingTotal;
        }

        public void setTaxIncludingTotal(String taxIncludingTotal) {
            this.taxIncludingTotal = taxIncludingTotal;
        }

        public String getTaxExcludingTotal() {
            return taxExcludingTotal;
        }

        public void setTaxExcludingTotal(String taxExcludingTotal) {
            this.taxExcludingTotal = taxExcludingTotal;
        }
    }
    
    /**
     * 费用明细类
     */
    public static class ExpenseDetail {
        /**
         * 费用名称
         */
        private String expenseName;
        
        /**
         * 编号
         */
        private String number;
        
        /**
         * 品牌
         */
        private String brand;
        
        /**
         * 具体规格
         */
        private String specification;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 费用变动情况
         */
        private String expenseChangeInfo;
        
        /**
         * 数量
         */
        private Integer quantity;
        
        /**
         * 含税单价
         */
        private BigDecimal taxIncludingPrice;
        
        /**
         * 不含税单价
         */
        private BigDecimal taxExcludingPrice;
        
        /**
         * 含税单行总价
         */
        private BigDecimal taxIncludingLineTotal;
        
        /**
         * 不含税单行总价
         */
        private BigDecimal taxExcludingLineTotal;

        public String getExpenseName() {
            return expenseName;
        }

        public void setExpenseName(String expenseName) {
            this.expenseName = expenseName;
        }
        
        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public String getBrand() {
            return brand;
        }

        public void setBrand(String brand) {
            this.brand = brand;
        }

        public String getSpecification() {
            return specification;
        }

        public void setSpecification(String specification) {
            this.specification = specification;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getExpenseChangeInfo() {
            return expenseChangeInfo;
        }

        public void setExpenseChangeInfo(String expenseChangeInfo) {
            this.expenseChangeInfo = expenseChangeInfo;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public BigDecimal getTaxIncludingPrice() {
            return taxIncludingPrice;
        }

        public void setTaxIncludingPrice(BigDecimal taxIncludingPrice) {
            this.taxIncludingPrice = taxIncludingPrice;
        }

        public BigDecimal getTaxExcludingPrice() {
            return taxExcludingPrice;
        }

        public void setTaxExcludingPrice(BigDecimal taxExcludingPrice) {
            this.taxExcludingPrice = taxExcludingPrice;
        }

        public BigDecimal getTaxIncludingLineTotal() {
            return taxIncludingLineTotal;
        }

        public void setTaxIncludingLineTotal(BigDecimal taxIncludingLineTotal) {
            this.taxIncludingLineTotal = taxIncludingLineTotal;
        }

        public BigDecimal getTaxExcludingLineTotal() {
            return taxExcludingLineTotal;
        }

        public void setTaxExcludingLineTotal(BigDecimal taxExcludingLineTotal) {
            this.taxExcludingLineTotal = taxExcludingLineTotal;
        }
    }

    public BillMetadata getMetadata() {
        return metadata;
    }

    public void setMetadata(BillMetadata metadata) {
        this.metadata = metadata;
    }

    public Map<String, BillDetail> getBills() {
        return bills;
    }

    public void setBills(Map<String, BillDetail> bills) {
        this.bills = bills;
    }
} 