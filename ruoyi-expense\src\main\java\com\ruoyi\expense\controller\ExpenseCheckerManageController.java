package com.ruoyi.expense.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.expense.domain.ExpenseCheckerManage;
import com.ruoyi.expense.service.IExpenseCheckerManageService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/expense/checker_manage")
public class ExpenseCheckerManageController extends BaseController {
    
    @Autowired
    private IExpenseCheckerManageService checkerManageService;

//    @RequiresPermissions("expense:checker_manage:view")
    @GetMapping()
    public String checkerManage() {
        return "expense/ExpenseCheckerManage/checkerManage";
    }

//    @RequiresPermissions("expense:checker_manage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExpenseCheckerManage checkerManage) {
        startPage();
        List<ExpenseCheckerManage> list = checkerManageService.selectCheckerManageList(checkerManage);
        return getDataTable(list);
    }

//    @RequiresPermissions("expense:checker_manage:add")
    @GetMapping("/add")
    public String add() {
        return "expense/ExpenseCheckerManage/add";
    }

    /**
     * 显示编辑核对人页面
     */
//    @RequiresPermissions("expense:checker_manage:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        ExpenseCheckerManage checkerManage = checkerManageService.selectCheckerManageById(id);
        mmap.put("checkerManage", checkerManage);
        return "expense/ExpenseCheckerManage/edit";
    }

//    @RequiresPermissions("expense:checker_manage:add")
    @Log(title = "部门核对人管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody ExpenseCheckerManage checkerManage) {
        int result = checkerManageService.insertCheckerManage(checkerManage);
        if (result == -1) {
            return AjaxResult.error("该姓名和EHR号已存在，不能重复添加");
        }
        if (result == -2) {
            return AjaxResult.error("部门名称不存在于字典中，请重新输入！");
        }
        if (result == -3) {
            return AjaxResult.error("未查到该EHR号对应的系统用户，请检查EHR号是否正确！");
        }
        if (result == -4) {
            return AjaxResult.error("姓名与EHR号不匹配，请检查输入信息是否正确！");
        }
        return toAjax(result);
    }

    /**
     * 更新核对人信息
     */
//    @RequiresPermissions("expense:checker_manage:edit")
    @Log(title = "部门核对人管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody ExpenseCheckerManage checkerManage) {
        int result = checkerManageService.updateCheckerManage(checkerManage);
        if (result == -2) {
            return AjaxResult.error("部门名称不存在于字典中，请重新输入！");
        }
        return toAjax(result);
    }

//    @RequiresPermissions("expense:checker_manage:remove")
    @Log(title = "部门核对人管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        int result = checkerManageService.deleteCheckerManageByIds(ids);
        if (result == -3) {
            return AjaxResult.error("存在核对人未查到系统用户，请检查EHR号是否正确！");
        }
        return toAjax(result);
    }

    /**
     * 校验用户姓名和EHR号是否存在且匹配
     */
//    @RequiresPermissions("expense:checker_manage:add")
    @PostMapping("/validateUser")
    @ResponseBody
    public AjaxResult validateUser(@RequestParam String name, @RequestParam String ehrNumber) {
        int result = checkerManageService.validateUserInfo(name, ehrNumber);
        if (result == -3) {
            return AjaxResult.error("未查到该EHR号对应的系统用户，请检查EHR号是否正确！");
        }
        if (result == -4) {
            return AjaxResult.error("姓名与EHR号不匹配，请检查输入信息是否正确！");
        }
        return AjaxResult.success("用户信息校验通过");
    }
} 