package com.ruoyi.line.domain;

import java.util.ArrayList;
import java.util.List;

/**
 * 账单推送结果类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class BillPushResult {
    
    /**
     * 推送是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果推送失败）
     */
    private String errorMessage;
    
    /**
     * 总共选择的机构数量
     */
    private int totalInstitutions;
    
    /**
     * 实际发送账单的机构数量
     */
    private int actualSentCount;
    
    /**
     * 有账单的机构信息列表
     */
    private List<InstitutionInfo> institutionsWithBills;
    
    /**
     * 无账单的机构信息列表
     */
    private List<InstitutionInfo> institutionsWithoutBills;
    
    /**
     * 机构信息内部类
     */
    public static class InstitutionInfo {
        /**
         * 机构ID
         */
        private String institutionId;
        
        /**
         * 机构名称
         */
        private String institutionName;
        
        /**
         * 账单数量（线路数量）
         */
        private int billCount;
        
        public InstitutionInfo() {}
        
        public InstitutionInfo(String institutionId, String institutionName, int billCount) {
            this.institutionId = institutionId;
            this.institutionName = institutionName;
            this.billCount = billCount;
        }
        
        public String getInstitutionId() {
            return institutionId;
        }
        
        public void setInstitutionId(String institutionId) {
            this.institutionId = institutionId;
        }
        
        public String getInstitutionName() {
            return institutionName;
        }
        
        public void setInstitutionName(String institutionName) {
            this.institutionName = institutionName;
        }
        
        public int getBillCount() {
            return billCount;
        }
        
        public void setBillCount(int billCount) {
            this.billCount = billCount;
        }
    }
    
    // 构造函数
    public BillPushResult() {
        this.institutionsWithBills = new ArrayList<>();
        this.institutionsWithoutBills = new ArrayList<>();
    }
    
    // Getter和Setter方法
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public int getTotalInstitutions() {
        return totalInstitutions;
    }
    
    public void setTotalInstitutions(int totalInstitutions) {
        this.totalInstitutions = totalInstitutions;
    }
    
    public int getActualSentCount() {
        return actualSentCount;
    }
    
    public void setActualSentCount(int actualSentCount) {
        this.actualSentCount = actualSentCount;
    }
    
    public List<InstitutionInfo> getInstitutionsWithBills() {
        return institutionsWithBills;
    }
    
    public void setInstitutionsWithBills(List<InstitutionInfo> institutionsWithBills) {
        this.institutionsWithBills = institutionsWithBills;
    }
    
    public List<InstitutionInfo> getInstitutionsWithoutBills() {
        return institutionsWithoutBills;
    }
    
    public void setInstitutionsWithoutBills(List<InstitutionInfo> institutionsWithoutBills) {
        this.institutionsWithoutBills = institutionsWithoutBills;
    }
    
    /**
     * 添加有账单的机构
     */
    public void addInstitutionWithBills(String institutionId, String institutionName, int billCount) {
        this.institutionsWithBills.add(new InstitutionInfo(institutionId, institutionName, billCount));
    }
    
    /**
     * 添加无账单的机构
     */
    public void addInstitutionWithoutBills(String institutionId, String institutionName) {
        this.institutionsWithoutBills.add(new InstitutionInfo(institutionId, institutionName, 0));
    }
    
    /**
     * 获取详细的推送结果信息
     */
    public String getDetailedMessage() {
        if (!success) {
            return errorMessage;
        }
        
        StringBuilder message = new StringBuilder();
        message.append("账单推送成功！");
        message.append("\n共选择 ").append(totalInstitutions).append(" 个机构，");
        message.append("实际发送 ").append(actualSentCount).append(" 份账单。");
        
        if (!institutionsWithBills.isEmpty()) {
            message.append("\n\n有账单的机构：");
            for (InstitutionInfo info : institutionsWithBills) {
                message.append("\n• ").append(info.getInstitutionName());
            }
        }
        
        if (!institutionsWithoutBills.isEmpty()) {
            message.append("\n\n无账单的机构：");
            for (InstitutionInfo info : institutionsWithoutBills) {
                message.append("\n• ").append(info.getInstitutionName());
            }
        }
        
        return message.toString();
    }
    
    /**
     * 获取简化的推送结果信息（适用于机构数量较多的情况）
     */
    public String getSimplifiedMessage() {
        if (!success) {
            return errorMessage;
        }
        
        StringBuilder message = new StringBuilder();
        message.append("账单推送成功！");
        message.append("\n共选择 ").append(totalInstitutions).append(" 个机构，");
        message.append("实际发送 ").append(actualSentCount).append(" 份账单。");
        
        if (!institutionsWithBills.isEmpty()) {
            message.append("\n有账单的机构：").append(institutionsWithBills.size()).append(" 个");
        }
        
        if (!institutionsWithoutBills.isEmpty()) {
            message.append("\n无账单的机构：").append(institutionsWithoutBills.size()).append(" 个");
        }
        
        return message.toString();
    }
    
    /**
     * 根据机构数量决定使用详细信息还是简化信息
     */
    public String getOptimalMessage() {
        // 如果总机构数量超过10个，使用简化信息
        if (totalInstitutions > 10) {
            return getSimplifiedMessage();
        } else {
            return getDetailedMessage();
        }
    }
} 