package com.ruoyi.line.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.line.service.IWorkOrderService;
import com.ruoyi.line.service.ILineAlldataService;
import com.ruoyi.line.domain.WorkOrderDTO;
import com.ruoyi.line.domain.WorkOrderExcel;
import com.ruoyi.line.domain.LineAlldata;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.line.utils.WorkOrderWordUtil;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.StringUtils;

/**
 * 工单生成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class WorkOrderServiceImpl implements IWorkOrderService {
    
    @Autowired
    private ILineAlldataService lineAlldataService;
    
    /**
     * 生成运营商工单
     */
    @Override
    public AjaxResult generateWorkOrder(WorkOrderDTO workOrderDTO) {
        try {
            // 验证工单数据
            AjaxResult validateResult = validateWorkOrderData(workOrderDTO);
            if ((Integer)validateResult.get("code") != 0) {
                return validateResult;
            }
            
            // 根据操作类型生成工单数据
            List<WorkOrderExcel> workOrderList = new ArrayList<>();
            String operatorName = "";
            String operationType = workOrderDTO.getOperationType();
            
            switch (operationType) {
                case "1": // 新增
                    workOrderList = generateNewLineWorkOrder(workOrderDTO);
                    operatorName = DictUtils.getDictLabel("line_operator", workOrderDTO.getOperator());
                    
                    if (workOrderList.isEmpty()) {
                        return AjaxResult.error("没有生成工单数据");
                    }
                    
                    // 生成Word文件
                    String fileName = generateFileName(operationType, operatorName);
                    AjaxResult result = WorkOrderWordUtil.generateWorkOrderWord(workOrderList, operatorName, getOperationTypeName(operationType), fileName, workOrderDTO.getRemarks());
                    
                    result.put("operatorName", operatorName);
                    result.put("operationType", getOperationTypeName(operationType));
                    result.put("workOrderCount", workOrderList.size());
                    result.put("fileCount", 1);
                    
                    return result;
                    
                case "2": // 撤销
                case "3": // 移机  
                case "4": // 扩容
                case "5": // 停用
                    // 按运营商分组生成多个工单
                    return generateMultiOperatorWorkOrders(workOrderDTO, operationType);
                    
                default:
                    return AjaxResult.error("不支持的工单类型");
            }
        } catch (Exception e) {
            return AjaxResult.error("生成工单失败：" + e.getMessage());
        }
    }
    
    /**
     * 验证工单数据
     */
    @Override
    public AjaxResult validateWorkOrderData(WorkOrderDTO workOrderDTO) {
        if (workOrderDTO == null) {
            return AjaxResult.error("工单数据不能为空");
        }
        
        String operationType = workOrderDTO.getOperationType();
        if (StringUtils.isEmpty(operationType)) {
            return AjaxResult.error("请选择工单操作类型");
        }
        
        switch (operationType) {
            case "1": // 新增
                return validateNewLineData(workOrderDTO);
            case "2": // 撤销
                return validateSelectedLinesData(workOrderDTO);
            case "3": // 移机
                return validateRelocateData(workOrderDTO);
            case "4": // 扩容
                return validateExpandData(workOrderDTO);
            case "5": // 停用
                return validateDisableData(workOrderDTO);
            default:
                return AjaxResult.error("不支持的工单类型");
        }
    }
    
    /**
     * 生成新增线路工单
     */
    private List<WorkOrderExcel> generateNewLineWorkOrder(WorkOrderDTO workOrderDTO) {
        List<WorkOrderExcel> workOrderList = new ArrayList<>();
        
        WorkOrderExcel workOrder = new WorkOrderExcel();
        workOrder.setLineNumber("新增申请");
        workOrder.setName("新增线路");
        
        String operatorName = DictUtils.getDictLabel("line_operator", workOrderDTO.getOperator());
        String type = "新增线路";
        workOrder.setType(type);
        
        String addressAndContact = String.format("地址: %s\n联系人: %s\n带宽: %s", 
            workOrderDTO.getAddress(), 
            workOrderDTO.getContact(),
            workOrderDTO.getBandwidth());
        workOrder.setAddressAndContact(addressAndContact);
        
        workOrderList.add(workOrder);
        return workOrderList;
    }
    
    /**
     * 生成撤销工单
     */
    private List<WorkOrderExcel> generateCancelWorkOrder(WorkOrderDTO workOrderDTO) {
        List<WorkOrderExcel> workOrderList = new ArrayList<>();
        
        for (Long lineId : workOrderDTO.getSelectedLineIds()) {
            LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
            if (lineData != null) {
                WorkOrderExcel workOrder = new WorkOrderExcel();
                workOrder.setLineNumber(lineData.getLineNumber());
                workOrder.setName(lineData.getLineName());
                workOrder.setType("撤销");
                
                String addressAndContact = String.format("地址: %s\n联系人: %s", 
                    lineData.getAddress(), 
                    lineData.getOppositeContact());
                workOrder.setAddressAndContact(addressAndContact);
                
                workOrderList.add(workOrder);
            }
        }
        
        return workOrderList;
    }
    
    /**
     * 生成移机工单
     */
    private List<WorkOrderExcel> generateRelocateWorkOrder(WorkOrderDTO workOrderDTO) {
        List<WorkOrderExcel> workOrderList = new ArrayList<>();
        
        for (Long lineId : workOrderDTO.getSelectedLineIds()) {
            LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
            if (lineData != null) {
                WorkOrderExcel workOrder = new WorkOrderExcel();
                workOrder.setLineNumber(lineData.getLineNumber());
                workOrder.setName(lineData.getLineName());
                workOrder.setType("新增线路");
                
                String addressAndContact = String.format("原地址: %s\n新地址: %s\n联系人: %s", 
                    lineData.getAddress(), 
                    workOrderDTO.getNewAddress(),
                    lineData.getOppositeContact());
                workOrder.setAddressAndContact(addressAndContact);
                
                workOrderList.add(workOrder);
            }
        }
        
        return workOrderList;
    }
    
    /**
     * 生成扩容工单
     */
    private List<WorkOrderExcel> generateExpandWorkOrder(WorkOrderDTO workOrderDTO) {
        List<WorkOrderExcel> workOrderList = new ArrayList<>();
        
        for (Long lineId : workOrderDTO.getSelectedLineIds()) {
            LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
            if (lineData != null) {
                WorkOrderExcel workOrder = new WorkOrderExcel();
                workOrder.setLineNumber(lineData.getLineNumber());
                workOrder.setName(lineData.getLineName());
                workOrder.setType("线路扩容");
                
                String addressAndContact = String.format("地址: %s\n联系人: %s\n原带宽: %s\n新带宽: %s", 
                    lineData.getAddress(), 
                    lineData.getOppositeContact(),
                    lineData.getLineBandwidth(),
                    workOrderDTO.getBandwidth());
                workOrder.setAddressAndContact(addressAndContact);
                
                workOrderList.add(workOrder);
            }
        }
        
        return workOrderList;
    }
    
    /**
     * 生成停用工单
     */
    private List<WorkOrderExcel> generateDisableWorkOrder(WorkOrderDTO workOrderDTO) {
        List<WorkOrderExcel> workOrderList = new ArrayList<>();
        
        for (Long lineId : workOrderDTO.getSelectedLineIds()) {
            LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
            if (lineData != null) {
                WorkOrderExcel workOrder = new WorkOrderExcel();
                workOrder.setLineNumber(lineData.getLineNumber());
                workOrder.setName(lineData.getLineName());
                workOrder.setType("线路停用");
                
                String addressAndContact = String.format("地址: %s\n联系人: %s", 
                    lineData.getAddress(), 
                    lineData.getOppositeContact());
                workOrder.setAddressAndContact(addressAndContact);
                
                workOrderList.add(workOrder);
            }
        }
        
        return workOrderList;
    }
    
    /**
     * 按运营商分组生成多个工单
     */
    private AjaxResult generateMultiOperatorWorkOrders(WorkOrderDTO workOrderDTO, String operationType) {
        if (workOrderDTO.getSelectedLineIds() == null || workOrderDTO.getSelectedLineIds().isEmpty()) {
            return AjaxResult.error("请选择要操作的线路");
        }
        
        // 按运营商分组线路
        Map<String, List<LineAlldata>> operatorGroups = groupLinesByOperator(workOrderDTO.getSelectedLineIds());
        
        if (operatorGroups.isEmpty()) {
            return AjaxResult.error("没有找到有效的线路数据");
        }
        
        List<String> generatedFiles = new ArrayList<>();
        List<String> operatorNames = new ArrayList<>();
        int totalWorkOrderCount = 0;
        
        // 为每个运营商生成工单
        for (Map.Entry<String, List<LineAlldata>> entry : operatorGroups.entrySet()) {
            String operatorCode = entry.getKey();
            List<LineAlldata> operatorLines = entry.getValue();
            String operatorName = DictUtils.getDictLabel("line_operator", operatorCode);
            
            // 创建该运营商的工单数据
            WorkOrderDTO operatorWorkOrderDTO = createOperatorWorkOrderDTO(workOrderDTO, operatorLines);
            List<WorkOrderExcel> workOrderList = new ArrayList<>();
            
            // 根据操作类型生成工单数据
            switch (operationType) {
                case "2": // 撤销
                    workOrderList = generateCancelWorkOrder(operatorWorkOrderDTO);
                    break;
                case "3": // 移机
                    workOrderList = generateRelocateWorkOrder(operatorWorkOrderDTO);
                    break;
                case "4": // 扩容
                    workOrderList = generateExpandWorkOrder(operatorWorkOrderDTO);
                    break;
                case "5": // 停用
                    workOrderList = generateDisableWorkOrder(operatorWorkOrderDTO);
                    break;
            }
            
            if (!workOrderList.isEmpty()) {
                // 生成Word文件
                String fileName = generateFileName(operationType, operatorName);
                AjaxResult fileResult = WorkOrderWordUtil.generateWorkOrderWord(workOrderList, operatorName, getOperationTypeName(operationType), fileName, operatorWorkOrderDTO.getRemarks());
                
                if (fileResult != null && fileResult.get("code") != null && (Integer)fileResult.get("code") == 0) {
                    generatedFiles.add((String)fileResult.get("msg"));
                    operatorNames.add(operatorName);
                    totalWorkOrderCount += workOrderList.size();
                    
                    // 更新线路状态
                    updateLineStatusAfterWorkOrder(operatorWorkOrderDTO, operationType);
                }
            }
        }
        
        if (generatedFiles.isEmpty()) {
            return AjaxResult.error("没有成功生成任何工单");
        }
        
        // 返回多文件结果
        AjaxResult result = AjaxResult.success();
        result.put("fileList", generatedFiles);
        result.put("operatorNames", operatorNames);
        result.put("operationType", getOperationTypeName(operationType));
        result.put("workOrderCount", totalWorkOrderCount);
        result.put("fileCount", generatedFiles.size());
        result.put("message", String.format("成功生成 %d 个工单文件，涉及运营商：%s", 
                   generatedFiles.size(), String.join("、", operatorNames)));
        
        return result;
    }
    
    /**
     * 按运营商分组线路
     */
    private Map<String, List<LineAlldata>> groupLinesByOperator(List<Long> selectedLineIds) {
        Map<String, List<LineAlldata>> operatorGroups = new HashMap<>();
        
        for (Long lineId : selectedLineIds) {
            LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
            if (lineData != null && StringUtils.isNotEmpty(lineData.getOperator())) {
                String operator = lineData.getOperator();
                operatorGroups.computeIfAbsent(operator, k -> new ArrayList<>()).add(lineData);
            }
        }
        
        return operatorGroups;
    }
    
    /**
     * 为特定运营商创建工单DTO
     */
    private WorkOrderDTO createOperatorWorkOrderDTO(WorkOrderDTO original, List<LineAlldata> operatorLines) {
        WorkOrderDTO operatorDTO = new WorkOrderDTO();
        operatorDTO.setOperationType(original.getOperationType());
        operatorDTO.setOperator(original.getOperator());
        operatorDTO.setBandwidth(original.getBandwidth());
        operatorDTO.setAddress(original.getAddress());
        operatorDTO.setContact(original.getContact());
        operatorDTO.setNewAddress(original.getNewAddress());
        operatorDTO.setRemarks(original.getRemarks());
        operatorDTO.setWorkOrderDate(original.getWorkOrderDate());
        operatorDTO.setSenderUnit(original.getSenderUnit());
        operatorDTO.setReceiverUnit(original.getReceiverUnit());
        
        // 设置该运营商的线路ID列表
        List<Long> operatorLineIds = new ArrayList<>();
        for (LineAlldata line : operatorLines) {
            operatorLineIds.add(line.getLineCode());
        }
        operatorDTO.setSelectedLineIds(operatorLineIds);
        
        return operatorDTO;
    }
    
    /**
     * 从选中的线路中获取运营商名称
     */
    private String getOperatorFromSelectedLines(List<Long> selectedLineIds) {
        if (selectedLineIds == null || selectedLineIds.isEmpty()) {
            return "";
        }
        
        LineAlldata lineData = lineAlldataService.selectLineAlldataById(selectedLineIds.get(0));
        if (lineData != null) {
            return DictUtils.getDictLabel("line_operator", lineData.getOperator());
        }
        
        return "";
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String operationType, String operatorName) {
        String operationName = getOperationTypeName(operationType);
        String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        return String.format("通信线路变动通知_%s_%s_%s", operatorName, operationName, date);
    }
    
    /**
     * 获取操作类型名称
     */
    private String getOperationTypeName(String operationType) {
        switch (operationType) {
            case "1": return "新增";
            case "2": return "撤销";
            case "3": return "移机";
            case "4": return "扩容";
            case "5": return "停用";
            default: return "未知";
        }
    }
    
    /**
     * 验证新增线路数据
     */
    private AjaxResult validateNewLineData(WorkOrderDTO workOrderDTO) {
        if (StringUtils.isEmpty(workOrderDTO.getOperator())) {
            return AjaxResult.error("请选择运营商");
        }
        if (StringUtils.isEmpty(workOrderDTO.getBandwidth())) {
            return AjaxResult.error("请填写带宽信息");
        }
        if (StringUtils.isEmpty(workOrderDTO.getAddress())) {
            return AjaxResult.error("请填写地址信息");
        }
        if (StringUtils.isEmpty(workOrderDTO.getContact())) {
            return AjaxResult.error("请填写联系人信息");
        }
        return AjaxResult.success();
    }
    
    /**
     * 验证选中线路数据
     */
    private AjaxResult validateSelectedLinesData(WorkOrderDTO workOrderDTO) {
        if (workOrderDTO.getSelectedLineIds() == null || workOrderDTO.getSelectedLineIds().isEmpty()) {
            return AjaxResult.error("请选择要操作的线路");
        }
        return AjaxResult.success();
    }
    
    /**
     * 验证移机数据
     */
    private AjaxResult validateRelocateData(WorkOrderDTO workOrderDTO) {
        AjaxResult selectedResult = validateSelectedLinesData(workOrderDTO);
        if ((Integer)selectedResult.get("code") != 0) {
            return selectedResult;
        }
        if (StringUtils.isEmpty(workOrderDTO.getNewAddress())) {
            return AjaxResult.error("请填写新地址信息");
        }
        return AjaxResult.success();
    }
    
    /**
     * 验证扩容数据
     */
    private AjaxResult validateExpandData(WorkOrderDTO workOrderDTO) {
        AjaxResult selectedResult = validateSelectedLinesData(workOrderDTO);
        if ((Integer)selectedResult.get("code") != 0) {
            return selectedResult;
        }
        if (StringUtils.isEmpty(workOrderDTO.getBandwidth())) {
            return AjaxResult.error("请填写新带宽信息");
        }
        return AjaxResult.success();
    }
    
    /**
     * 验证停用数据
     */
    private AjaxResult validateDisableData(WorkOrderDTO workOrderDTO) {
        AjaxResult selectedResult = validateSelectedLinesData(workOrderDTO);
        if ((Integer)selectedResult.get("code") != 0) {
            return selectedResult;
        }
        if (StringUtils.isEmpty(workOrderDTO.getRemarks())) {
            return AjaxResult.error("请填写停用备注信息");
        }
        return AjaxResult.success();
    }
    
    /**
     * 根据工单类型更新线路状态
     */
    private void updateLineStatusAfterWorkOrder(WorkOrderDTO workOrderDTO, String operationType) {
        try {
            if ("2".equals(operationType)) { // 撤销工单
                updateLineStatusForCancel(workOrderDTO);
            } else if ("4".equals(operationType)) { // 扩容工单
                updateLineRemarkForExpand(workOrderDTO);
            } else if ("5".equals(operationType)) { // 停用工单
                updateLineRemarkForDisable(workOrderDTO);
            }
        } catch (Exception e) {
            // 记录错误日志，但不影响工单生成的结果
            System.err.println("更新线路状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 撤销工单后更新线路状态
     */
    private void updateLineStatusForCancel(WorkOrderDTO workOrderDTO) {
        if (workOrderDTO.getSelectedLineIds() == null || workOrderDTO.getSelectedLineIds().isEmpty()) {
            return;
        }
        
        Date currentDate = new Date();
        for (Long lineId : workOrderDTO.getSelectedLineIds()) {
            try {
                LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
                if (lineData != null) {
                    // 设置线路状态为"撤销中"
                    lineData.setLineStatus("2");
                    // 设置撤销日期为当前日期
                    lineData.setLineCancelDate(currentDate);
                    // 更新数据库
                    lineAlldataService.updateLineAlldata(lineData);
                }
            } catch (Exception e) {
                System.err.println("更新线路ID " + lineId + " 的撤销状态失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 扩容工单后更新备注
     */
    private void updateLineRemarkForExpand(WorkOrderDTO workOrderDTO) {
        if (workOrderDTO.getSelectedLineIds() == null || workOrderDTO.getSelectedLineIds().isEmpty()) {
            return;
        }
        
        String expandInfo = "扩容变动：新带宽 " + workOrderDTO.getBandwidth() +
                          "（" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + "）";
        
        for (Long lineId : workOrderDTO.getSelectedLineIds()) {
            try {
                LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
                if (lineData != null) {
                    String originalRemark = lineData.getRemark() != null ? lineData.getRemark() : "";
                    // 在原备注基础上追加扩容信息
                    String newRemark = originalRemark.isEmpty() ? expandInfo : originalRemark + "; " + expandInfo;
                    lineData.setRemark(newRemark);
                    // 更新数据库
                    lineAlldataService.updateLineAlldata(lineData);
                }
            } catch (Exception e) {
                System.err.println("更新线路ID " + lineId + " 的扩容备注失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 停用工单后更新备注
     */
    private void updateLineRemarkForDisable(WorkOrderDTO workOrderDTO) {
        if (workOrderDTO.getSelectedLineIds() == null || workOrderDTO.getSelectedLineIds().isEmpty()) {
            return;
        }
        
        if (StringUtils.isEmpty(workOrderDTO.getRemarks())) {
            return;
        }
        
        String disableInfo = "停用原因：" + workOrderDTO.getRemarks() +
                          "（" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + "）";
        
        for (Long lineId : workOrderDTO.getSelectedLineIds()) {
            try {
                LineAlldata lineData = lineAlldataService.selectLineAlldataById(lineId);
                if (lineData != null) {
                    String originalRemark = lineData.getRemark() != null ? lineData.getRemark() : "";
                    // 在原备注基础上追加停用信息
                    String newRemark = originalRemark.isEmpty() ? disableInfo : originalRemark + "; " + disableInfo;
                    lineData.setRemark(newRemark);
                    // 更新数据库
                    lineAlldataService.updateLineAlldata(lineData);
                }
            } catch (Exception e) {
                System.err.println("更新线路ID " + lineId + " 的停用备注失败: " + e.getMessage());
            }
        }
    }
} 