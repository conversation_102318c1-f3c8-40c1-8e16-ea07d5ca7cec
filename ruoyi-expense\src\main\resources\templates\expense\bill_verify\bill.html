<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('账单核对')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li><label>费用类型：</label><input type="text" name="expense_type"/></li>
                            <li><label>计费周期：</label><input type="text" name="billing_cycle"/></li>
                            <li><label>账单状态：</label><input type="text" name="status"/></li>
                            <li><label>账单上传人：</label><input type="text" name="bill_publisher"/></li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="confirmBill()" shiro:hasPermission="expense:bill_verify:confirm">
                    <i class="fa fa-check"></i> 确认账单
                </a>
                <a class="btn btn-danger" onclick="returnBill()" shiro:hasPermission="expense:bill_verify:return">
                    <i class="fa fa-reply"></i> 退回账单
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        // 后端渲染当前用户角色和用户名
        var roles = /*[[${roles}]]*/ [];
        var currentUser = '/*[[${currentUser}]]*/';
        var prefix = ctx + "expense/bill_verify";
        
        // 防重复请求机制
        var isLoading = false;
        var lastRequestTime = 0;
        var REQUEST_INTERVAL = 2000; // 2秒内不允许重复请求
        
        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "账单",
                cache: false,
                sidePagination: "server",
                pagination: true,
                search: false,
                showRefresh: true,
                showToggle: false,
                showColumns: true,
                sortable: true,
                sortOrder: "desc",
                pageSize: 10,
                pageList: [10, 25, 50],
                strictSearch: true,
                clickToSelect: false,
                uniqueId: "bill_id",
                queryParams: function(params) {
                    // 防重复请求检查
                    var currentTime = new Date().getTime();
                    if (isLoading || (currentTime - lastRequestTime) < REQUEST_INTERVAL) {
                        return false;
                    }
                    isLoading = true;
                    lastRequestTime = currentTime;
                    
                    var temp = {
                        limit: params.limit,
                        offset: params.offset,
                        search: params.search,
                        sort: params.sort,
                        order: params.order,
                        expense_type: $('input[name="expense_type"]').val(),
                        billing_cycle: $('input[name="billing_cycle"]').val(),
                        status: $('input[name="status"]').val(),
                        bill_publisher: $('input[name="bill_publisher"]').val()
                    };
                    return temp;
                },
                responseHandler: function(res) {
                    isLoading = false; // 请求完成，重置状态
                    return {
                        "total": res.total,
                        "rows": res.rows
                    };
                },
                onLoadError: function() {
                    isLoading = false; // 请求出错，重置状态
                },
                onRefresh: function() {
                    lastRequestTime = 0; // 重置时间，允许刷新
                },
                columns: [
                    { checkbox: true },
                    { field: 'expense_type', title: '费用类型' },
                    { field: 'billing_cycle', title: '计费周期' },
                    { field: 'transfer_department', title: '划账部门' },
                    { field: 'status', title: '账单状态', formatter: statusFormat },
                    { field: 'total_price_with_tax', title: '含税总价', formatter: priceFormatter },
                    { field: 'total_price_without_tax', title: '不含税总价', formatter: priceFormatter },
                    { field: 'bill_publisher', title: '账单上传人' },
                    { field: 'bill_confirmer', title: '账单确认人' },
                    { field: 'bill_returner', title: '账单退回人' },
                    { field: 'bill_refuse_comment', title: '账单退回意见' },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewBill(' + row.bill_id + ')"><i class="fa fa-eye"></i>查看</a> ');
                            actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="exportBill(' + row.bill_id + ')"><i class="fa fa-download"></i>导出明细</a> ');
                            if (row.status == '1') {
                                actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="confirmBill(' + row.bill_id + ')"><i class="fa fa-check"></i>确认账单</a> ');
                                actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="returnBill(' + row.bill_id + ')"><i class="fa fa-reply"></i>退回账单</a> ');
                            }
                            return actions.join('');
                        }
                    }
                ],
                rowStyle: function(row, index) {
                    if (row.status == '0') {
                        return {classes: 'table-imported'};
                    } else if (row.status == '1') {
                        return {classes: 'table-released'};
                    } else if (row.status == '2') {
                        return {classes: 'table-confirmed'};
                    } else if (row.status == '3') {
                        return {classes: 'table-returned'};
                    }
                    return {};
                }
            };
            $.table.init(options);
        });
        
        // 重写搜索函数，添加防重复请求
        $.table.search = function() {
            if (isLoading) {
                $.modal.msgWarning('数据加载中，请稍候...');
                return;
            }
            lastRequestTime = 0; // 重置时间，允许搜索
            $('#bootstrap-table').bootstrapTable('refresh');
        };
        
        function statusFormat(value) {
            switch(value) {
                case '0': return '已入库';
                case '1': return '已发布';
                case '2': return '已确认';
                case '3': return '已退回';
                default: return value;
            }
        }
        function priceFormatter(value) {
            if (value == null || value == undefined || value == '') {
                return '0.00';
            }
            // 保留两位小数
            return parseFloat(value).toFixed(2);
        }
        function viewBill(id) {
            $.modal.openOptions({
                title: '账单详情',
                url: prefix + '/detail/' + id,
                width: 1200,
                height: 600,
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        }
        function confirmBill(id) {
            var ids = id ? [id] : $.table.selectColumns('bill_id');
            if (ids.length == 0) { $.modal.msgWarning('请选择要确认的账单'); return; }
            $.modal.confirm('确定要确认选中的账单吗？', function() {
                $.operate.post(prefix + '/confirm', { billIds: ids }, function(result) {
                    if (result.code == 200) {
                        // 操作成功后刷新表格
                        lastRequestTime = 0;
                        $('#bootstrap-table').bootstrapTable('refresh');
                    }
                });
            });
        }
        function returnBill(id) {
            var ids = id ? [id] : $.table.selectColumns('bill_id');
            if (ids.length == 0) { $.modal.msgWarning('请选择要退回的账单'); return; }
            
            layer.prompt({
                formType: 2,
                title: '请输入退回意见',
                area: ['400px', '200px']
            }, function(value, index, elem) {
                $.operate.post(prefix + '/return', { billIds: ids, refuseComment: value }, function(result) {
                    if (result.code == 200) {
                        // 操作成功后刷新表格
                        lastRequestTime = 0;
                        $('#bootstrap-table').bootstrapTable('refresh');
                    }
                });
                layer.close(index);
            });
        }

        // 导出账单关联的费用明细
        function exportBill(id) {
            $.modal.confirm("是否导出该账单的费用明细？", function() {
                // 使用GET请求下载，直接通过窗口打开URL
                var url = prefix + "/export/" + id;
                window.open(url, "_blank");
            });
        }
    </script>
    <style>
        .table-imported { background-color: #ffffff !important; }
        .table-released { background-color: #fffbcd !important; }
        .table-confirmed { background-color: #d4edda !important; }
        .table-returned { background-color: #f8d7da !important; }
    </style>
</body>
</html> 