package com.ruoyi.expense.controller;

import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.domain.ExpenseDetails;
import com.ruoyi.expense.service.IExpenseDetailsService;
import com.ruoyi.expense.service.IExpenseBillService;
import com.ruoyi.expense.service.IExpenseBillNotificationService;
import com.ruoyi.expense.service.IExpenseAllocationTableService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.common.core.domain.entity.SysDictData;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

/**
 * 账单管理Controller
 *
 * <AUTHOR>
 * @date 2023-07-24
 */
@Controller
@RequestMapping("/expense/bill_manage")
public class ExpenseBillManageController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(ExpenseBillManageController.class);

    private String prefix = "expense/bill_manage";

    @Autowired
    private IExpenseBillService expenseBillService;

    @Autowired
    private IExpenseDetailsService expenseDetailsService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private IExpenseBillNotificationService expenseBillNotificationService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private IExpenseAllocationTableService expenseAllocationTableService;

    /**
     * 查看账单详情（弹窗）
     */
    @RequiresPermissions("expense:bill_manage:detail")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") Long id, ModelMap mmap) {
        ExpenseBill bill = expenseBillService.selectExpenseBillById(id);
        mmap.put("bill", bill);
        // 查询费用明细
        ExpenseDetails query = new ExpenseDetails();
        query.setExpenseType(bill.getExpense_type());
        query.setBillingCycle(bill.getBilling_cycle());
        query.setTransferDepartment(bill.getTransfer_department());
        List<ExpenseDetails> detailsList = expenseDetailsService.selectExpenseDetailsList(query);
        mmap.put("detailsList", detailsList);
        return "expense/bill_manage/billDetail";
    }

    /**
     * 获取字典数据 - 为账单管理页面搜索功能提供数据
     */
    // @RequiresPermissions("expense:bill_manage:view")
    @PostMapping("/getDictData")
    @ResponseBody
    public AjaxResult getDictData(@RequestParam("dictType") String dictType) {
        log.info("获取字典数据请求，字典类型: {}", dictType);
        
        try {
            // 创建查询条件
            SysDictData dictData = new SysDictData();
            dictData.setDictType(dictType);
            dictData.setStatus("0"); // 只查询正常状态的字典数据
            
            // 查询字典数据
            List<SysDictData> list = dictDataService.selectDictDataList(dictData);
            
            log.info("查询到字典数据 [{}] 共 {} 条", dictType, list.size());
            
            // 返回符合前端期望的数据格式
            return AjaxResult.success("查询成功", list);
            
        } catch (Exception e) {
            String errorMsg = "获取字典数据异常：" + e.getMessage();
            log.error(errorMsg, e);
            return AjaxResult.error(errorMsg);
        }
    }

    /**
     * 账单管理主页面跳转
     */
    // @RequiresPermissions("expense:bill_manage:view")
    @GetMapping()
    public String billManage(ModelMap mmap) {
        mmap.put("currentUser", ShiroUtils.getLoginName());
        return "expense/bill_manage/bill";
    }

    /**
     * 账单列表（管理员查全部，上传人查自己）
     */
    @RequiresPermissions("expense:bill_manage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExpenseBill expenseBill) {
        String loginName = ShiroUtils.getLoginName();
        Long userId = ShiroUtils.getSysUser().getUserId();
        String userName=ShiroUtils.getSysUser().getUserName();
        Set<String> roleKeys = sysRoleService.selectRoleKeys(userId);
        log.info("用户[{}]的角色列表: {}", userId, roleKeys);
        boolean isAdmin = roleKeys.contains("expense_admin");
        // 实现账单列表的权限控制：
        // 1. 系统管理员(isAdmin=true)可以查看所有账单
        // 2. 账单管理员(isAdmin=true)可以查看所有账单
        // 3. 其他用户只能查看自己发布的账单
        if (!ShiroUtils.getSysUser().isAdmin() && !isAdmin) {
            expenseBill.setBill_publisher(userName);
        }
        startPage();
        List<ExpenseBill> list = expenseBillService.selectExpenseBillList(expenseBill);
        return getDataTable(list);
    }
    
    /**
     * 发布账单（支持批量，只有已入库状态才能发布，不自动发送通知）
     */
    @RequiresPermissions("expense:bill_manage:publish")
    @Log(title = "账单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/publish")
    @ResponseBody
    public AjaxResult publish(@RequestParam(value = "billIds[]", required = false) List<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return AjaxResult.error("未选择账单");
        }
        int success = 0, fail = 0;
        StringBuilder failMsg = new StringBuilder();
        
        for (Long billId : billIds) {
            ExpenseBill bill = expenseBillService.selectExpenseBillById(billId);
            if (bill == null) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不存在; ");
                continue;
            }
            if (!"0".equals(bill.getStatus())) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不是已入库状态; ");
                continue;
            }
            bill.setStatus("1"); // 1=已发布
            int result = expenseBillService.updateExpenseBill(bill);
            if (result > 0) {
                success++;
            } else {
                fail++;
                failMsg.append("账单ID:" + billId + " 发布失败; ");
            }
        }
        
        if (fail == 0) {
            return AjaxResult.success("成功发布 " + success + " 条账单");
        } else {
            return AjaxResult.error("成功发布 " + success + " 条，失败 " + fail + " 条。" + failMsg.toString());
        }
    }

    /**
     * 撤回账单（支持批量，只有已入库或已发布状态才能撤回，保留费用明细）
     */
    @RequiresPermissions("expense:bill_manage:revoke")
    @Log(title = "账单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/revoke")
    @ResponseBody
    public AjaxResult revoke(@RequestParam(value = "billIds[]", required = false) List<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return AjaxResult.error("未选择账单");
        }
        int success = 0, fail = 0;
        StringBuilder failMsg = new StringBuilder();
        List<ExpenseBill> revokedBills = new ArrayList<>();
        
        for (Long billId : billIds) {
            ExpenseBill bill = expenseBillService.selectExpenseBillById(billId);
            if (bill == null) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不存在; ");
                continue;
            }
            if (!"0".equals(bill.getStatus()) && !"1".equals(bill.getStatus())) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不是已入库或已发布状态; ");
                continue;
            }
            bill.setStatus("3"); // 3=已退回
            int result = expenseBillService.updateExpenseBill(bill);
            // 撤回账单时保留费用明细，不删除
            if (result > 0) {
                success++;
                revokedBills.add(bill);
            } else {
                fail++;
                failMsg.append("账单ID:" + billId + " 撤回失败; ");
            }
        }
        
        // 发送撤回通知给账单上传人
        if (!revokedBills.isEmpty()) {
            try {
                log.info("开始发送账单撤回通知，共{}份账单", revokedBills.size());
                String notificationResult = expenseBillNotificationService.sendBillRevokeNotifications(revokedBills, "管理员撤回操作");
                log.info("账单撤回通知发送结果：{}", notificationResult);
            } catch (Exception e) {
                log.error("发送账单撤回通知时发生异常", e);
                // 通知发送失败不影响撤回结果，只记录日志
            }
        }
        
        if (fail == 0) {
            return AjaxResult.success("成功撤回 " + success + " 条账单");
        } else {
            return AjaxResult.error("成功撤回 " + success + " 条，失败 " + fail + " 条。" + failMsg.toString());
        }
    }

    /**
     * 删除账单（支持批量，删除账单和相关明细）
     */
    @RequiresPermissions("expense:bill_manage:delete")
    @Log(title = "账单管理", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    @ResponseBody
    public AjaxResult delete(@RequestParam(value = "billIds[]", required = false) List<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return AjaxResult.error("未选择账单");
        }
        int success = 0, fail = 0;
        StringBuilder failMsg = new StringBuilder();
        for (Long billId : billIds) {
            ExpenseBill bill = expenseBillService.selectExpenseBillById(billId);
            if (bill == null) {
                fail++;
                failMsg.append("账单ID:" + billId + " 不存在; ");
                continue;
            }
            
            // 删除关联的费用明细
            ExpenseDetails query = new ExpenseDetails();
            query.setExpenseType(bill.getExpense_type());
            query.setBillingCycle(bill.getBilling_cycle());
            query.setTransferDepartment(bill.getTransfer_department());
            List<ExpenseDetails> detailsList = expenseDetailsService.selectExpenseDetailsList(query);
            for (ExpenseDetails d : detailsList) {
                expenseDetailsService.deleteExpenseDetailsById(d.getId());
            }
            
            // 删除账单本身
            int result = expenseBillService.deleteExpenseBillById(billId);
            if (result > 0) {
                success++;
            } else {
                fail++;
                failMsg.append("账单ID:" + billId + " 删除失败; ");
            }
        }
        if (fail == 0) {
            return AjaxResult.success("成功删除 " + success + " 条账单");
        } else {
            return AjaxResult.error("成功删除 " + success + " 条，失败 " + fail + " 条。" + failMsg.toString());
        }
    }



    /**
     * 生成分摊表页面
     */
//    @RequiresPermissions("expense:allocation_table:generate")
    @GetMapping("/generateAllocationTableDialog")
    public String generateAllocationTableDialog() {
        return "expense/bill_manage/generateAllocationTableDialog";
    }



    /**
     * 批量通知对账（通知所有已发布状态的账单）
     */
    @RequiresPermissions("expense:bill_manage:notify")
    @Log(title = "账单管理", businessType = BusinessType.OTHER)
    @PostMapping("/notifyAll")
    @ResponseBody
    public AjaxResult notifyAllBills() {
        // 查询所有已发布状态的账单
        ExpenseBill queryBill = new ExpenseBill();
        queryBill.setStatus("1"); // 1=已发布
        List<ExpenseBill> publishedBills = expenseBillService.selectExpenseBillList(queryBill);
        
        if (publishedBills == null || publishedBills.isEmpty()) {
            return AjaxResult.error("当前没有已发布状态的账单需要通知");
        }
        
        try {
            log.info("开始批量通知对账，共{}份已发布账单", publishedBills.size());
            String notificationResult = expenseBillNotificationService.sendBillNotifications(publishedBills);
            log.info("批量通知对账结果：{}", notificationResult);
            
            return AjaxResult.success("批量通知对账完成，共通知 " + publishedBills.size() + " 份账单。" + notificationResult);
        } catch (Exception e) {
            log.error("批量通知对账时发生异常", e);
            return AjaxResult.error("批量通知对账失败：" + e.getMessage());
        }
    }

    /**
     * 单独通知对账（通知指定的单个账单）
     */
    @RequiresPermissions("expense:bill_manage:notify")
    @Log(title = "账单管理", businessType = BusinessType.OTHER)
    @PostMapping("/notifySingle")
    @ResponseBody
    public AjaxResult notifySingle(@RequestParam("billId") Long billId) {
        if (billId == null) {
            return AjaxResult.error("账单ID不能为空");
        }
        
        ExpenseBill bill = expenseBillService.selectExpenseBillById(billId);
        if (bill == null) {
            return AjaxResult.error("账单不存在");
        }
        
        if (!"1".equals(bill.getStatus())) {
            return AjaxResult.error("只能通知已发布状态的账单");
        }
        
        try {
            log.info("开始单独通知对账，账单ID：{}", billId);
            String notificationResult = expenseBillNotificationService.sendSingleBillNotification(bill);
            log.info("单独通知对账结果：{}", notificationResult);
            
            if (notificationResult.contains("发送成功")) {
                return AjaxResult.success("通知对账成功：" + notificationResult);
            } else {
                return AjaxResult.error("通知对账失败：" + notificationResult);
            }
        } catch (Exception e) {
            log.error("单独通知对账时发生异常", e);
            return AjaxResult.error("通知对账失败：" + e.getMessage());
        }
    }


} 