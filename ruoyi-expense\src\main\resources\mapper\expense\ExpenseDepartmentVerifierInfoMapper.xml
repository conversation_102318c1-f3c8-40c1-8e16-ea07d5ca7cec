<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.expense.mapper.ExpenseDepartmentVerifierInfoMapper">
    
    <resultMap type="com.ruoyi.expense.domain.ExpenseDepartmentVerifierInfo" id="ExpenseDepartmentVerifierInfoResult">
        <id     property="id"               column="id"                />
        <result property="name"             column="name"              />
        <result property="departmentName"   column="department_name"   />
        <result property="ehrNumber"        column="ehr_number"        />
        <result property="remarks"          column="remarks"           />
    </resultMap>
    
    <sql id="selectVerifierInfoVo">
        select id, name, department_name, ehr_number, remarks
        from expense_departmentverifierinfo
    </sql>
    
    <select id="selectVerifierInfoList" parameterType="com.ruoyi.expense.domain.ExpenseDepartmentVerifierInfo" resultMap="ExpenseDepartmentVerifierInfoResult">
        <include refid="selectVerifierInfoVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="departmentName != null and departmentName != ''">
                AND department_name like concat('%', #{departmentName}, '%')
            </if>
            <if test="ehrNumber != null and ehrNumber != ''">
                AND ehr_number = #{ehrNumber}
            </if>
        </where>
    </select>
    
    <select id="selectVerifierInfoById" parameterType="Integer" resultMap="ExpenseDepartmentVerifierInfoResult">
        <include refid="selectVerifierInfoVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVerifierInfoByName" resultMap="ExpenseDepartmentVerifierInfoResult">
        <include refid="selectVerifierInfoVo"/>
        where name = #{name}
    </select>
    
    <select id="selectVerifierInfoByDepartmentName" resultMap="ExpenseDepartmentVerifierInfoResult">
        <include refid="selectVerifierInfoVo"/>
        where department_name = #{departmentName}
    </select>
    
    <select id="selectVerifierInfoByEhrNumber" resultMap="ExpenseDepartmentVerifierInfoResult">
        <include refid="selectVerifierInfoVo"/>
        where ehr_number = #{ehrNumber}
    </select>
    
</mapper> 