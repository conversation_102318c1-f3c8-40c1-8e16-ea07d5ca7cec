<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.expense.mapper.ExpenseAllocationTableMapper">
    
    <resultMap type="ExpenseAllocationTable" id="ExpenseAllocationTableResult">
        <result property="id"    column="id"    />
        <result property="tableName"    column="table_name"    />
        <result property="billingCycle"    column="billing_cycle"    />
        <result property="expenseType"    column="expense_type"    />
        <result property="preparer"    column="preparer"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="comments"    column="comments"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="confirmTime"    column="confirm_time"    />
    </resultMap>

    <sql id="selectExpenseAllocationTableVo">
        select id, table_name, billing_cycle, expense_type, preparer, reviewer, responsible_person, comments, status, create_time, confirm_time from expense_allocation_table
    </sql>

    <select id="selectExpenseAllocationTableList" parameterType="ExpenseAllocationTable" resultMap="ExpenseAllocationTableResult">
        <include refid="selectExpenseAllocationTableVo"/>
        <where>  
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="billingCycle != null  and billingCycle != ''"> and billing_cycle = #{billingCycle}</if>
            <if test="expenseType != null  and expenseType != ''"> and expense_type = #{expenseType}</if>
            <if test="preparer != null  and preparer != ''"> and preparer = #{preparer}</if>
            <if test="reviewer != null  and reviewer != ''"> and reviewer = #{reviewer}</if>
            <if test="responsiblePerson != null  and responsiblePerson != ''"> and responsible_person = #{responsiblePerson}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        ORDER BY 
        CASE status
            WHEN '审核中' THEN 1  -- 审核中优先显示
            WHEN '已拒绝' THEN 2  -- 已拒绝其次
            WHEN '已审核' THEN 3  -- 已审核最后
            ELSE 4
        END ASC, 
        id DESC
    </select>
    
    <select id="selectExpenseAllocationTableById" parameterType="Long" resultMap="ExpenseAllocationTableResult">
        <include refid="selectExpenseAllocationTableVo"/>
        where id = #{id}
    </select>

    <select id="selectByExpenseTypeAndBillingCycle" resultMap="ExpenseAllocationTableResult">
        <include refid="selectExpenseAllocationTableVo"/>
        where expense_type = #{expenseType} and billing_cycle = #{billingCycle}
    </select>

    <select id="checkExistByExpenseTypeAndBillingCycle" resultType="int">
        select count(1) from expense_allocation_table 
        where expense_type = #{expenseType} and billing_cycle = #{billingCycle}
        and status != '已拒绝'
    </select>
        
    <insert id="insertExpenseAllocationTable" parameterType="ExpenseAllocationTable" useGeneratedKeys="true" keyProperty="id">
        insert into expense_allocation_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">table_name,</if>
            <if test="billingCycle != null and billingCycle != ''">billing_cycle,</if>
            <if test="expenseType != null and expenseType != ''">expense_type,</if>
            <if test="preparer != null and preparer != ''">preparer,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="comments != null">comments,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="confirmTime != null">confirm_time,</if>
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">#{tableName},</if>
            <if test="billingCycle != null and billingCycle != ''">#{billingCycle},</if>
            <if test="expenseType != null and expenseType != ''">#{expenseType},</if>
            <if test="preparer != null and preparer != ''">#{preparer},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="comments != null">#{comments},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
         </trim>
    </insert>

    <update id="updateExpenseAllocationTable" parameterType="ExpenseAllocationTable">
        update expense_allocation_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="billingCycle != null and billingCycle != ''">billing_cycle = #{billingCycle},</if>
            <if test="expenseType != null and expenseType != ''">expense_type = #{expenseType},</if>
            <if test="preparer != null and preparer != ''">preparer = #{preparer},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="comments != null">comments = #{comments},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExpenseAllocationTableById" parameterType="Long">
        delete from expense_allocation_table where id = #{id}
    </delete>

    <delete id="deleteExpenseAllocationTableByIds" parameterType="String">
        delete from expense_allocation_table where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 