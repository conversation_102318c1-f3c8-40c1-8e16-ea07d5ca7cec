package com.ruoyi.line.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.line.domain.WorkOrderExcel;

/**
 * 工单Excel生成工具类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public class WorkOrderExcelUtil {
    
    private static final Logger log = LoggerFactory.getLogger(WorkOrderExcelUtil.class);
    
    /**
     * 生成工单Excel
     * 
     * @param workOrderList 工单数据列表
     * @param operatorName 运营商名称
     * @param operationType 操作类型名称
     * @param fileName 文件名
     * @return 生成结果
     */
    public static AjaxResult generateWorkOrderExcel(List<WorkOrderExcel> workOrderList, 
            String operatorName, String operationType, String fileName) {
        
        Workbook workbook = null;
        FileOutputStream out = null;
        
        try {
            workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("通信线路变动通知");
            
            // 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle contentStyle = createContentStyle(workbook);
            CellStyle tableHeaderStyle = createTableHeaderStyle(workbook);
            CellStyle tableCellStyle = createTableCellStyle(workbook);
            
            int rowIndex = 0;
            
            // 第一行：机构标题（宁波市分行金融科技部）
            Row titleRow = sheet.createRow(rowIndex++);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("宁波市分行金融科技部");
            titleCell.setCellStyle(titleStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 0, 3));
            
            // 空行
            sheet.createRow(rowIndex++);
            
            // 标题行
            Row mainTitleRow = sheet.createRow(rowIndex++);
            Cell mainTitleCell = mainTitleRow.createCell(0);
            mainTitleCell.setCellValue("关于通信线路变动的通知");
            mainTitleCell.setCellStyle(headerStyle);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 0, 3));
            
            // 空行
            sheet.createRow(rowIndex++);
            
            // 收件人行
            Row receiverRow = sheet.createRow(rowIndex++);
            Cell receiverCell = receiverRow.createCell(0);
            receiverCell.setCellValue(getReceiverName(operatorName) + "：");
            receiverCell.setCellStyle(contentStyle);
            
            // 空行
            sheet.createRow(rowIndex++);
            
            // 说明文字行
            Row descRow = sheet.createRow(rowIndex++);
            Cell descCell = descRow.createCell(0);
            descCell.setCellValue("因业务需要，申请变动以下线路，请贵公司尽快落实。");
            descCell.setCellStyle(contentStyle);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 0, 3));
            
            // 空行
            sheet.createRow(rowIndex++);
            
            // 表格标题行
            Row tableHeaderRow = sheet.createRow(rowIndex++);
            String[] headers = {"线路编号", "名称", "类型", "地址及联系人"};
            for (int i = 0; i < headers.length; i++) {
                Cell headerCell = tableHeaderRow.createCell(i);
                headerCell.setCellValue(headers[i]);
                headerCell.setCellStyle(tableHeaderStyle);
            }
            
            // 数据行
            for (WorkOrderExcel workOrder : workOrderList) {
                Row dataRow = sheet.createRow(rowIndex++);
                
                Cell lineNumberCell = dataRow.createCell(0);
                lineNumberCell.setCellValue(workOrder.getLineNumber() != null ? workOrder.getLineNumber() : "");
                lineNumberCell.setCellStyle(tableCellStyle);
                
                Cell nameCell = dataRow.createCell(1);
                nameCell.setCellValue(workOrder.getName() != null ? workOrder.getName() : "");
                nameCell.setCellStyle(tableCellStyle);
                
                Cell typeCell = dataRow.createCell(2);
                typeCell.setCellValue(workOrder.getType() != null ? workOrder.getType() : "");
                typeCell.setCellStyle(tableCellStyle);
                
                Cell addressCell = dataRow.createCell(3);
                addressCell.setCellValue(workOrder.getAddressAndContact() != null ? workOrder.getAddressAndContact() : "");
                addressCell.setCellStyle(tableCellStyle);
            }
            
            // 空行若干
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            
            // 特此通知行
            Row noticeRow = sheet.createRow(rowIndex++);
            Cell noticeCell = noticeRow.createCell(0);
            noticeCell.setCellValue("特此通知！");
            noticeCell.setCellStyle(contentStyle);
            
            // 空行若干
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            
            // 发送单位行
            Row senderRow = sheet.createRow(rowIndex++);
            Cell senderCell = senderRow.createCell(2);
            senderCell.setCellValue("中国银行股份有限公司宁波市分行");
            senderCell.setCellStyle(contentStyle);
            
            // 日期行
            Row dateRow = sheet.createRow(rowIndex++);
            Cell dateCell = dateRow.createCell(2);
            String currentDate = new SimpleDateFormat("yyyy年M月d日").format(new Date());
            dateCell.setCellValue(currentDate);
            dateCell.setCellStyle(contentStyle);
            
            // 设置列宽
            sheet.setColumnWidth(0, 20 * 256); // 线路编号
            sheet.setColumnWidth(1, 30 * 256); // 名称
            sheet.setColumnWidth(2, 20 * 256); // 类型
            sheet.setColumnWidth(3, 50 * 256); // 地址及联系人
            
            // 保存文件
            String encodedFileName = encodingFilename(fileName);
            String filePath = getAbsoluteFile(encodedFileName);
            out = new FileOutputStream(filePath);
            workbook.write(out);
            
            return AjaxResult.success(encodedFileName);
            
        } catch (Exception e) {
            log.error("生成工单Excel异常: {}", e.getMessage(), e);
            throw new BusinessException("生成工单Excel失败，请联系网站管理员！");
        } finally {
            try {
                if (workbook != null) {
                    workbook.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                log.error("关闭资源失败: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 根据运营商名称获取收件人名称
     */
    private static String getReceiverName(String operatorName) {
        if ("电信".equals(operatorName)) {
            return "中国电信政企部";
        } else if ("移动".equals(operatorName)) {
            return "中国移动政企部";
        } else if ("联通".equals(operatorName)) {
            return "中国联通政企部";
        } else {
            return operatorName + "政企部";
        }
    }
    
    /**
     * 创建标题样式
     */
    private static CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 16);
        font.setBold(true);
        style.setFont(font);
        
        return style;
    }
    
    /**
     * 创建标题样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 14);
        font.setBold(true);
        style.setFont(font);
        
        return style;
    }
    
    /**
     * 创建内容样式
     */
    private static CellStyle createContentStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        
        return style;
    }
    
    /**
     * 创建表格标题样式
     */
    private static CellStyle createTableHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        style.setFont(font);
        
        return style;
    }
    
    /**
     * 创建表格单元格样式
     */
    private static CellStyle createTableCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);
        
        return style;
    }
    
    /**
     * 编码文件名
     */
    private static String encodingFilename(String filename) {
        return UUID.randomUUID().toString() + "_" + filename + ".xlsx";
    }
    
    /**
     * 获取下载路径
     */
    private static String getAbsoluteFile(String filename) {
        String downloadPath = RuoYiConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }
} 