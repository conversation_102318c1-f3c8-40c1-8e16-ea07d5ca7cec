<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分摊表基本信息 - 浅绿色主题</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 分摊表基本信息样式 - 浅绿色主题 */
        .allocation-detail-container {
            background: #ffffff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            border: 1px solid #e9ecef;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .allocation-detail-header {
            background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
            color: white;
            padding: 8px 12px;
            margin: 0;
            border-bottom: 1px solid #388e3c;
        }

        .allocation-detail-header h5 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .allocation-detail-header h5 i {
            margin-right: 6px;
            font-size: 12px;
        }

        .allocation-detail-content {
            padding: 8px;
            background: #fafbfc;
        }

        .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 6px;
            background: white;
            border-radius: 3px;
            padding: 6px 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
            border-left: 2px solid #66bb6a;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-item {
            flex: 1;
            min-width: 150px;
            margin-bottom: 4px;
            padding-right: 8px;
        }

        .info-item:last-child {
            padding-right: 0;
        }

        .info-item.quarter-width {
            flex: 1 1 25%;
            min-width: 180px;
        }

        .info-item.third-width {
            flex: 1 1 33.333%;
            min-width: 140px;
        }

        .info-item.half-width {
            flex: 1 1 50%;
            min-width: 200px;
        }

        .info-label {
            display: inline-block;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 2px;
            font-size: 12px;
            min-width: 60px;
            position: relative;
        }

        .info-label::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 18px;
            height: 1px;
            background: #66bb6a;
            border-radius: 1px;
        }

        .info-value {
            display: flex;
            align-items: center;
            color: #2d3748;
            font-size: 13px;
            line-height: 1.3;
            padding: 4px 6px;
            background: #f7fafc;
            border-radius: 3px;
            border: 1px solid #e2e8f0;
            min-height: 24px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
        }

        .status-badge.status-pending {
            background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
            color: #2e7d32;
            border: 1px solid #81c784;
        }

        .status-badge.status-approved {
            background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
            color: #ffffff;
            border: 1px solid #4caf50;
        }

        .status-badge.status-rejected {
            background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
            color: #c62828;
            border: 1px solid #e57373;
        }

        .info-icon {
            margin-right: 4px;
            color: #66bb6a;
            font-size: 11px;
        }

        .basic-info .info-value {
            background: #e8f5e8;
            border-color: #66bb6a;
            color: #2e7d32;
        }

        .person-info .info-value {
            background: #f1f8e9;
            border-color: #81c784;
            color: #388e3c;
        }

        .time-info .info-value {
            background: #e8f5e8;
            border-color: #66bb6a;
            color: #2e7d32;
            font-family: 'Courier New', monospace;
        }

        .comments-section {
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-left: 2px solid #ff9800;
            border-radius: 3px;
            padding: 6px 8px;
            margin-top: 6px;
        }

        .comments-section .info-label {
            color: #e65100;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .comments-section .info-value {
            background: #ffffff;
            border: 1px solid #ffcc02;
            color: #bf360c;
            font-style: italic;
            min-height: auto;
            padding: 4px 6px;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .info-item.quarter-width,
            .info-item.third-width,
            .info-item.half-width {
                flex: 1 1 100%;
                min-width: 100%;
            }
        }

        .theme-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 1px solid #66bb6a;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            color: #2e7d32;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="theme-info">
            <i class="fa fa-leaf" style="margin-right: 8px; font-size: 16px;"></i>
            分摊表基本信息 - 浅绿色主题展示
        </div>
        
        <!-- 分摊表基本信息 -->
        <div class="allocation-detail-container">
            <div class="allocation-detail-header">
                <h5><i class="fa fa-info-circle"></i>分摊表基本信息</h5>
            </div>
            <div class="allocation-detail-content">
                <!-- 基本信息行（合并前两行为一行四列） -->
                <div class="info-row basic-info">
                    <div class="info-item quarter-width">
                        <span class="info-label"><i class="fa fa-file-text info-icon"></i>分摊表名称</span>
                        <span class="info-value">202506-电子设备配件-分摊表</span>
                    </div>
                    <div class="info-item quarter-width">
                        <span class="info-label"><i class="fa fa-flag info-icon"></i>状态</span>
                        <span class="info-value">
                            <span class="status-badge status-pending">审核中</span>
                        </span>
                    </div>
                    <div class="info-item quarter-width">
                        <span class="info-label"><i class="fa fa-tags info-icon"></i>费用类型</span>
                        <span class="info-value">电子设备配件</span>
                    </div>
                    <div class="info-item quarter-width">
                        <span class="info-label"><i class="fa fa-calendar info-icon"></i>计费周期</span>
                        <span class="info-value">202506</span>
                    </div>
                </div>

                <!-- 人员信息行 -->
                <div class="info-row person-info">
                    <div class="info-item third-width">
                        <span class="info-label"><i class="fa fa-user info-icon"></i>制表人</span>
                        <span class="info-value">王海东</span>
                    </div>
                    <div class="info-item third-width">
                        <span class="info-label"><i class="fa fa-user-check info-icon"></i>复核人</span>
                        <span class="info-value">若依</span>
                    </div>
                    <div class="info-item third-width">
                        <span class="info-label"><i class="fa fa-user-cog info-icon"></i>负责人</span>
                        <span class="info-value">张经理</span>
                    </div>
                </div>

                <!-- 时间信息行 -->
                <div class="info-row time-info">
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-clock-o info-icon"></i>创建时间</span>
                        <span class="info-value">2025.08.01 17:05:00</span>
                    </div>
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-check-circle info-icon"></i>确认时间</span>
                        <span class="info-value">未确认</span>
                    </div>
                </div>

                <!-- 修改意见 -->
                <div class="comments-section">
                    <div class="info-item full-width">
                        <span class="info-label"><i class="fa fa-comment info-icon"></i>修改意见</span>
                        <span class="info-value">请核实部分账单的分摊部门信息，确保准确性。</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态展示示例 -->
        <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-palette" style="margin-right: 8px;"></i>状态徽章展示</h4>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <span class="status-badge status-pending">审核中</span>
                    <div style="margin-top: 5px; font-size: 12px; color: #666;">审核中状态</div>
                </div>
                <div style="text-align: center;">
                    <span class="status-badge status-approved">已审核</span>
                    <div style="margin-top: 5px; font-size: 12px; color: #666;">已审核状态</div>
                </div>
                <div style="text-align: center;">
                    <span class="status-badge status-rejected">已拒绝</span>
                    <div style="margin-top: 5px; font-size: 12px; color: #666;">已拒绝状态</div>
                </div>
            </div>
        </div>

        <!-- 色彩说明 -->
        <div style="margin-top: 20px; padding: 15px; background: #f1f8e9; border: 1px solid #81c784; border-radius: 8px;">
            <h5 style="color: #2e7d32; margin-bottom: 10px;"><i class="fa fa-info-circle" style="margin-right: 6px;"></i>浅绿色主题说明</h5>
            <ul style="margin: 0; color: #388e3c; font-size: 13px;">
                <li><strong>主色调：</strong>浅绿色系 (#66bb6a, #4caf50, #81c784)</li>
                <li><strong>背景色：</strong>淡绿色背景 (#e8f5e8, #f1f8e9)</li>
                <li><strong>文字色：</strong>深绿色文字 (#2e7d32, #388e3c)</li>
                <li><strong>装饰元素：</strong>绿色边框、图标、下划线</li>
                <li><strong>状态徽章：</strong>绿色渐变，保持良好的对比度</li>
            </ul>
        </div>
    </div>
</body>
</html>
