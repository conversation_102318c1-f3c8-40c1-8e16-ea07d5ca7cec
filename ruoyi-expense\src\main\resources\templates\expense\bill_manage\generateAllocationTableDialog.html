<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('生成分摊表')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-generate-allocation">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">费用类型：</label>
                <div class="col-sm-8">
                    <select name="expenseType" id="expenseType" class="form-control" required>
                        <option value="">请选择费用类型</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">计费周期：</label>
                <div class="col-sm-8">
                    <input name="billingCycle" id="billingCycle" placeholder="请输入计费周期，如：202501 或 202501-06" class="form-control" type="text" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 格式：YYYYMM（如：202501）或 YYYYMM-MM（如：202501-06）</span>
                </div>
            </div>


        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        /*<![CDATA[*/
        var prefix = ctx + "expense/bill_manage";
        /*]]>*/
        
        // 自定义验证规则：计费周期格式验证
        $.validator.addMethod("billingCycleFormat", function(value, element) {
            // 单月格式：YYYYMM（如：202501）
            var singleMonthPattern = /^\d{6}$/;
            // 跨月格式：YYYYMM-MM（如：202501-06）
            var rangeMonthPattern = /^\d{6}-\d{2}$/;
            
            return this.optional(element) || singleMonthPattern.test(value) || rangeMonthPattern.test(value);
        }, "计费周期格式不正确，请输入YYYYMM格式（如：202501）或YYYYMM-MM格式（如：202501-06）");

        $("#form-generate-allocation").validate({
            onkeyup: false,
            rules: {
                expenseType: {
                    required: true
                },
                billingCycle: {
                    required: true,
                    billingCycleFormat: true
                }
            },
            messages: {
                expenseType: {
                    required: "请选择费用类型"
                },
                billingCycle: {
                    required: "请输入计费周期"
                }
            },
            focusCleanup: true
        });
        
        function submitHandler() {
            if ($.validate.form()) {
                var expenseType = $("#expenseType").val();
                var billingCycle = $("#billingCycle").val();
                
                // 直接调用生成分摊表接口，无需二次确认
                $.post(ctx + "expense/allocation_table/generate", {
                    expenseType: expenseType,
                    billingCycle: billingCycle
                }, function(result) {
                    if (result.code == 0) {
                        // 成功提示 - 使用更明显的提示框
                        parent.layer.alert(result.msg, {
                            icon: 1,
                            title: '生成分摊表成功',
                            btn: ['确定'],
                            yes: function(alertIndex) {
                                // 先关闭当前提示框
                                parent.layer.close(alertIndex);
                                // 刷新父页面
                                parent.$.table.refresh();
                                // 关闭弹窗
                                var frameIndex = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(frameIndex);
                            }
                        });
                    } else {
                        // 错误提示 - 使用更明显的提示框
                        parent.layer.alert(result.msg, {
                            icon: 2,
                            title: '生成分摊表失败',
                            btn: ['确定'],
                            yes: function(alertIndex) {
                                // 关闭提示框
                                parent.layer.close(alertIndex);
                            }
                        });
                    }
                });
            }
        }
        
        // 页面加载完成后初始化
        $(function() {
            // 加载费用类型字典
            loadDictOptions('expense_type', '#expenseType');
        });
        
        // 加载字典数据并填充下拉框
        function loadDictOptions(dictType, selectId) {
            $.ajax({
                url: prefix + '/getDictData',
                type: 'POST',
                data: {
                    dictType: dictType
                },
                success: function(result) {
                    if (result.code === 0 && result.data) {
                        var options = '<option value="">请选择</option>';
                        $.each(result.data, function(index, item) {
                            options += '<option value="' + item.dictValue + '">' + item.dictLabel + '</option>';
                        });
                        $(selectId).html(options);
                    } else {
                        console.error('加载字典数据失败：' + (result.msg || '未知错误'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载字典数据异常：' + error);
                }
            });
        }
    </script>
</body>
</html> 