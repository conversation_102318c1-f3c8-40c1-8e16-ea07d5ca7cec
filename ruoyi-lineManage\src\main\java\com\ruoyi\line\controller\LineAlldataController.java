package com.ruoyi.line.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.line.domain.LineAlldata;
import com.ruoyi.line.service.IBillPushService;
import com.ruoyi.line.service.ILineAlldataService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.line.domain.LineAlldataExport;
import java.util.ArrayList;

/**
 * 通讯线路管理Controller
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Controller
@RequestMapping("/comline/manage")
public class LineAlldataController extends BaseController
{
    /** 定义通讯线路管理页面的前缀路径 */
    private String prefix = "comline/manage";

    /** 通讯线路管理Service接口 */
    @Autowired
    private ILineAlldataService lineAlldataService;
    
    /** 账单推送Service接口 */
    @Autowired
    private IBillPushService billPushService;
    
    /** 工单生成Service接口 */
    @Autowired
    private com.ruoyi.line.service.IWorkOrderService workOrderService;

    /**
     * 跳转到通讯线路管理页面
     * 
     * @return 通讯线路管理页面路径
     */
    //  @RequiresPermissions("comline:manage:view")
    @GetMapping()
    public String manage()
    {
        System.out.println("请求已到达manage方法");
        return prefix + "/manage";
    }

    /**
     * 查询通讯线路管理列表
     * 
     * @param lineAlldata 查询条件对象
     * @return 分页数据结果集
     */
    // @RequiresPermissions("comline:manage:list")
    @PostMapping("/list") 
    @ResponseBody
    public TableDataInfo list(LineAlldata lineAlldata)
    {
        startPage();  // 开启分页
        List<LineAlldata> list = lineAlldataService.selectLineAlldataList(lineAlldata);
        return getDataTable(list);  // 返回分页数据
    }

    /**
     * 导出通讯线路管理列表
     */
    // @RequiresPermissions("comline:manage:export")
    @Log(title = "通讯线路管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(LineAlldata lineAlldata)
    {
        List<LineAlldata> list = lineAlldataService.selectLineAlldataList(lineAlldata);
        ExcelUtil<LineAlldata> util = new ExcelUtil<LineAlldata>(LineAlldata.class);
        return util.exportExcel(list, "manage");
    }

    /**
     * 导出通讯线路管理可视化数据列表
     */
    // @RequiresPermissions("comline:manage:export")
    @Log(title = "通讯线路管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportVisual")
    @ResponseBody
    public AjaxResult exportVisual(LineAlldata lineAlldata)
    {
        List<LineAlldata> list = lineAlldataService.selectLineAlldataList(lineAlldata);
        List<LineAlldataExport> exportList = new ArrayList<>();
        
        // 转换数据，将字典值转换为标签
        for (LineAlldata item : list) {
            LineAlldataExport exportItem = new LineAlldataExport();
            exportItem.setLineCode(item.getLineCode());
            exportItem.setLineNumber(item.getLineNumber());
            exportItem.setLineName(item.getLineName());
            exportItem.setLineBandwidth(item.getLineBandwidth());
            exportItem.setLineAddDate(item.getLineAddDate());
            exportItem.setLineCancelDate(item.getLineCancelDate());
            exportItem.setTaxIncludingPrice(item.getTaxIncludingPrice());
            exportItem.setLineUsageEnvironment(item.getLineUsageEnvironment());
            exportItem.setAddress(item.getAddress());
            exportItem.setOppositeContact(item.getOppositeContact());
            exportItem.setTaxRate(item.getTaxRate());
            exportItem.setTaxExcludingPrice(item.getTaxExcludingPrice());
            exportItem.setRemark(item.getRemark());
            
            // 转换字典字段
            exportItem.setInstitution(DictUtils.getDictLabel("line_institude", item.getInstitution()));
            exportItem.setOperator(DictUtils.getDictLabel("line_operator", item.getOperator()));
            exportItem.setLineType(DictUtils.getDictLabel("lineType", item.getLineType()));
            exportItem.setLineStatus(DictUtils.getDictLabel("lineStatus", item.getLineStatus()));
            
            exportList.add(exportItem);
        }
        
        ExcelUtil<LineAlldataExport> util = new ExcelUtil<LineAlldataExport>(LineAlldataExport.class);
        return util.exportExcel(exportList, "线路管理可视化数据");
    }



    /**
     * 新增通讯线路管理
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存通讯线路管理
     */
    // @RequiresPermissions("comline:manage:add")
    @Log(title = "通讯线路管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(LineAlldata lineAlldata)
    {
        return toAjax(lineAlldataService.insertLineAlldata(lineAlldata));
    }

    /**
     * 修改通讯线路管理
     */
    @GetMapping("/edit/{lineCode}")
    public String edit(@PathVariable("lineCode") Long lineCode, ModelMap mmap)
    {
        LineAlldata lineAlldata = lineAlldataService.selectLineAlldataById(lineCode);
        mmap.put("lineAlldata", lineAlldata);
        
        // 检查当前用户是否为 line_viewer 角色
        SysUser currentUser = ShiroUtils.getSysUser();
        boolean isLineViewer = ShiroUtils.getSubject().hasRole("line_viewer") && !currentUser.isAdmin();
        mmap.put("isLineViewer", isLineViewer);
        
        return prefix + "/edit";
    }

    /**
     * 修改保存通讯线路管理
     */
    // @RequiresPermissions("comline:manage:edit")
    @Log(title = "通讯线路管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(LineAlldata lineAlldata)
    {
        // 检查当前用户是否为 line_viewer 角色
        SysUser currentUser = ShiroUtils.getSysUser();
        boolean isLineViewer = ShiroUtils.getSubject().hasRole("line_viewer") && !currentUser.isAdmin();
        
        if (isLineViewer) {
            // line_viewer 角色只能更新部分字段，需要先获取原始数据
            LineAlldata originalData = lineAlldataService.selectLineAlldataById(lineAlldata.getLineCode());
            if (originalData != null) {
                // 只允许更新地址、对端联系人、备注
                originalData.setAddress(lineAlldata.getAddress());
                originalData.setOppositeContact(lineAlldata.getOppositeContact());
                originalData.setRemark(lineAlldata.getRemark());
                return toAjax(lineAlldataService.updateLineAlldata(originalData));
            }
        }
        
        return toAjax(lineAlldataService.updateLineAlldata(lineAlldata));
    }

    /**
     * 删除通讯线路管理
     */
    // @RequiresPermissions("comline:manage:remove")
    @Log(title = "通讯线路管理", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(lineAlldataService.deleteLineAlldataByIds(ids));
    }

    // @RequiresPermissions("comline:manage:calculate_exTAX")
    @Log(title = "通讯线路管理", businessType = BusinessType.UPDATE)
    @PostMapping( "/calculate_exTAX")
    @ResponseBody
    public AjaxResult calculate_exTAX(String ids)
    {
//        System.out.println("跳转到此处,重新启用电池设备！！！");
        return toAjax_line(lineAlldataService.calculateLineAlldataById(ids));
    }

    /**
     * 跳转到账单推送页面
     */
    // @RequiresPermissions("comline:manage:bill_push")
    @GetMapping("/bill_push")
    public String billPush()
    {
        return prefix + "/bill_push";
    }

    /**
     * 处理账单推送请求
     */
    // @RequiresPermissions("comline:manage:bill_push")  // 需要账单推送权限
    @Log(title = "通讯线路管理", businessType = BusinessType.OTHER)  // 记录操作日志，业务类型为其他
    @PostMapping("/bill_push")  // 处理POST请求，路径为/bill_push
    @ResponseBody  // 返回JSON格式的响应
    public AjaxResult billPushSave(String institutions, String publisher, String billingCycle)
    {
        // 验证账单发布人（必须是纯汉字）
        if (publisher == null || publisher.isEmpty()) {
            return error("账单发布人不能为空");
        }
        if (!publisher.matches("^[\\u4e00-\\u9fa5]+$")) {
            return error("账单发布人只能包含汉字");
        }
        
        // 验证计费周期（必须符合格式：YYYYMM 或 YYYYMM-MM）
        if (billingCycle == null || billingCycle.isEmpty()) {
            return error("计费周期不能为空");
        }
        if (!billingCycle.matches("^\\d{6}(-\\d{2})?$")) {
            return error("计费周期格式错误，应为格式如：202502 或 202502-06");
        }
        
        // 验证是否选择了机构
        if (institutions == null || institutions.isEmpty()) {
            return error("请至少选择一个机构");
        }
        
        try {
            com.ruoyi.line.domain.BillPushResult result = billPushService.pushBillData(institutions, publisher, billingCycle);
            if (!result.isSuccess()) {
                return error(result.getErrorMessage());
            }
            
            // 返回智能选择的推送结果信息
            AjaxResult ajaxResult = success();
            ajaxResult.put("pushResult", result);
            ajaxResult.put("detailedMessage", result.getOptimalMessage());
            return ajaxResult;
        } catch (Exception e) {
            return error("账单推送失败：" + e.getMessage());
        }
    }
    
    /**
     * 跳转到工单生成页面
     */
    // @RequiresPermissions("comline:manage:work_order")
    @GetMapping("/work_order")
    public String workOrder()
    {
        return prefix + "/work_order";
    }
    
    /**
     * 生成运营商工单
     */
    // @RequiresPermissions("comline:manage:work_order")
    @Log(title = "通讯线路管理", businessType = BusinessType.EXPORT)
    @PostMapping("/generate_work_order")
    @ResponseBody
    public AjaxResult generateWorkOrder(@org.springframework.web.bind.annotation.RequestBody com.ruoyi.line.domain.WorkOrderDTO workOrderDTO)
    {
        try {
            // 对选中的线路ID进行预处理，过滤掉无效值
            if (workOrderDTO.getSelectedLineIds() != null) {
                List<Long> validIds = workOrderDTO.getSelectedLineIds().stream()
                    .filter(id -> id != null && id > 0)
                    .collect(java.util.stream.Collectors.toList());
                workOrderDTO.setSelectedLineIds(validIds);
            }
            
            return workOrderService.generateWorkOrder(workOrderDTO);
        } catch (NumberFormatException e) {
            return error("工单生成失败：线路ID格式不正确，请重新选择线路");
        } catch (Exception e) {
            return error("工单生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取选中线路的详细信息（用于工单生成时显示）
     */
    // @RequiresPermissions("comline:manage:list")
    @PostMapping("/get_selected_lines")
    @ResponseBody
    public AjaxResult getSelectedLines(String ids)
    {
        try {
            if (com.ruoyi.common.utils.StringUtils.isEmpty(ids)) {
                return error("请选择线路");
            }
            
            String[] lineIds = ids.split(",");
            List<LineAlldata> selectedLines = new ArrayList<>();
            
            for (String lineId : lineIds) {
                // 过滤掉空字符串和无效的ID
                if (com.ruoyi.common.utils.StringUtils.isNotEmpty(lineId.trim())) {
                    try {
                        Long id = Long.parseLong(lineId.trim());
                        if (id > 0) {
                            LineAlldata lineData = lineAlldataService.selectLineAlldataById(id);
                            if (lineData != null) {
                                selectedLines.add(lineData);
                            }
                        }
                    } catch (NumberFormatException e) {
                        // 跳过无效的ID，继续处理其他ID
                        continue;
                    }
                }
            }
            
            if (selectedLines.isEmpty()) {
                return error("未找到选中的线路数据");
            }
            
            // 检查选中线路的运营商是否一致
            String firstOperator = selectedLines.get(0).getOperator();
            boolean operatorConsistent = selectedLines.stream()
                .allMatch(line -> firstOperator.equals(line.getOperator()));
            
            AjaxResult result = success();
            result.put("selectedLines", selectedLines);
            result.put("operatorConsistent", operatorConsistent);
            result.put("operatorName", com.ruoyi.common.utils.DictUtils.getDictLabel("line_operator", firstOperator));
            return result;
            
        } catch (Exception e) {
            return error("获取线路信息失败：" + e.getMessage());
        }
    }

}
