package com.ruoyi.expense.service;

import com.ruoyi.expense.domain.ExpenseDepartmentVerifierInfo;
import java.util.List;

/**
 * 部门账单核对人服务接口
 */
public interface IExpenseDepartmentVerifierInfoService {
    
    /**
     * 查询核对人列表
     */
    List<ExpenseDepartmentVerifierInfo> selectVerifierInfoList(ExpenseDepartmentVerifierInfo verifierInfo);
    
    /**
     * 通过ID查询单条数据
     */
    ExpenseDepartmentVerifierInfo selectVerifierInfoById(Integer id);
    
    /**
     * 根据核对人姓名查询信息
     */
    ExpenseDepartmentVerifierInfo selectVerifierInfoByName(String name);
    
    /**
     * 根据部门名称查询信息
     */
    List<ExpenseDepartmentVerifierInfo> selectVerifierInfoByDepartmentName(String departmentName);
    
    /**
     * 根据EHR编号查询信息
     */
    ExpenseDepartmentVerifierInfo selectVerifierInfoByEhrNumber(String ehrNumber);
} 