<!-- 通用CSS -->
<head th:fragment=header(title)>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="keywords" content="">
	<meta name="description" content="">
	<title th:text="${title}"></title>
	<link rel="shortcut icon" href="favicon.ico">
	<link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
	<link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
	<!-- bootstrap-table 表格插件样式 -->
	<link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css?v=20201129}" rel="stylesheet"/>
	<link th:href="@{/css/animate.css}" rel="stylesheet"/>
	<link th:href="@{/css/style.css?v=20200903}" rel="stylesheet"/>
	<link th:href="@{/ruoyi/css/ry-ui.css?v=4.6.0}" rel="stylesheet"/>
</head>

<!-- 通用JS -->
<div th:fragment="footer">
    <script th:inline="javascript"> var ctx = [[@{/}]]; var lockscreen = [[${session.lockscreen}]]; if(lockscreen){window.top.location=ctx+"lockscreen";} </script>
    <a id="scroll-up" href="#" class="btn btn-sm display"><i class="fa fa-angle-double-up"></i></a>
	<script th:src="@{/js/jquery.min.js}"></script>
	<script th:src="@{/js/bootstrap.min.js}"></script>
	<!-- bootstrap-table 表格插件 -->
	<script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table.min.js?v=20201129}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/locale/bootstrap-table-zh-CN.min.js?v=20201129}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/mobile/bootstrap-table-mobile.min.js?v=20201129}"></script>
	<!-- jquery-validate 表单验证插件 -->
	<script th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
	<script th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
	<script th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
	<!-- jquery-validate 表单树插件 -->
	<script th:src="@{/ajax/libs/bootstrap-treetable/bootstrap-treetable.js}"></script>
	<!-- 遮罩层 -->
	<script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
    <script th:src="@{/ajax/libs/iCheck/icheck.min.js}"></script>
	<script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
	<script th:src="@{/ajax/libs/layui/layui.js}"></script>
	<script th:src="@{/ruoyi/js/common.js?v=4.6.0}"></script>
	<script th:src="@{/ruoyi/js/ry-ui.js?v=4.6.0}"></script>
</div>

<!-- ztree树插件 -->
<div th:fragment="ztree-css">
    <link th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}" rel="stylesheet"/>
</div>
<div th:fragment="ztree-js">
    <script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
</div>

<!-- select2下拉框插件 -->
<div th:fragment="select2-css">
    <link th:href="@{/ajax/libs/select2/select2.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/select2/select2-bootstrap.css}" rel="stylesheet"/>
</div>
<div th:fragment="select2-js">
    <script th:src="@{/ajax/libs/select2/select2.min.js}"></script>
</div>

<!-- bootstrap-select下拉框插件 -->
<div th:fragment="bootstrap-select-css">
    <link th:href="@{/ajax/libs/bootstrap-select/bootstrap-select.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-select-js">
    <script th:src="@{/ajax/libs/bootstrap-select/bootstrap-select.js}"></script>
</div>

<!-- datetimepicker日期和时间插件 -->
<div th:fragment="datetimepicker-css">
    <link th:href="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="datetimepicker-js">
    <script th:src="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.js}"></script>
</div>

<!-- ui布局插件 -->
<div th:fragment="layout-latest-css">
    <link th:href="@{/ajax/libs/jquery-layout/jquery.layout-latest.css}" rel="stylesheet"/>
</div>
<div th:fragment="layout-latest-js">
    <script th:src="@{/ajax/libs/jquery-layout/jquery.layout-latest.js}"></script>
</div>

<!-- summernote富文本编辑器插件 -->
<div th:fragment="summernote-css">
    <link th:href="@{/ajax/libs/summernote/summernote.css}" rel="stylesheet"/>
	<link th:href="@{/ajax/libs/summernote/summernote-bs3.css}" rel="stylesheet"/>
</div>
<div th:fragment="summernote-js">
    <script th:src="@{/ajax/libs/summernote/summernote.min.js}"></script>
	<script th:src="@{/ajax/libs/summernote/summernote-zh-CN.js}"></script>
</div>

<!-- cropper图像裁剪插件 -->
<div th:fragment="cropper-css">
    <link th:href="@{/ajax/libs/cropper/cropper.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="cropper-js">
    <script th:src="@{/ajax/libs/cropper/cropper.min.js}"></script>
</div>

<!-- jasny功能扩展插件 -->
<div th:fragment="jasny-bootstrap-css">
    <link th:href="@{/ajax/libs/jasny/jasny-bootstrap.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="jasny-bootstrap-js">
    <script th:src="@{/ajax/libs/jasny/jasny-bootstrap.min.js}"></script>
</div>

<!-- fileinput文件上传插件 -->
<div th:fragment="bootstrap-fileinput-css">
    <link th:href="@{/ajax/libs/bootstrap-fileinput/fileinput.min.css?v=20201202}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-fileinput-js">
    <script th:src="@{/ajax/libs/bootstrap-fileinput/fileinput.min.js?v=20201202}"></script>
</div>

<!-- duallistbox双列表框插件 -->
<div th:fragment="bootstrap-duallistbox-css">
    <link th:href="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-duallistbox-js">
    <script th:src="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.js}"></script>
</div>

<!-- suggest搜索自动补全 -->
<div th:fragment="bootstrap-suggest-js">
    <script th:src="@{/ajax/libs/suggest/bootstrap-suggest.min.js}"></script>
</div>

<!-- typeahead搜索自动补全 -->
<div th:fragment="bootstrap-typeahead-js">
    <script th:src="@{/ajax/libs/typeahead/bootstrap3-typeahead.min.js}"></script>
</div>

<!-- 多级联动下拉 -->
<div th:fragment="jquery-cxselect-js">
    <script th:src="@{/ajax/libs/cxselect/jquery.cxselect.min.js}"></script>
</div>

<!-- jsonview格式化和语法高亮JSON格式数据查看插件 -->
<div th:fragment="jsonview-css">
    <link th:href="@{/ajax/libs/jsonview/jquery.jsonview.css}" rel="stylesheet"/>
</div>
<div th:fragment="jsonview-js">
    <script th:src="@{/ajax/libs/jsonview/jquery.jsonview.js}"></script>
</div>

<!-- jquery.smartwizard表单向导插件 -->
<div th:fragment="jquery-smartwizard-css">
    <link th:href="@{/ajax/libs/smartwizard/smart_wizard_all.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="jquery-smartwizard-js">
    <script th:src="@{/ajax/libs/smartwizard/jquery.smartWizard.min.js}"></script>
</div>

<!-- ECharts百度统计图表插件 -->
<div th:fragment="echarts-js">
    <script th:src="@{/ajax/libs/report/echarts/echarts-all.js}"></script>
</div>

<!-- peity图表组合插件 -->
<div th:fragment="peity-js">
    <script th:src="@{/ajax/libs/report/peity/jquery.peity.min.js}"></script>
</div>

<!-- sparkline线状图插件 -->
<div th:fragment="sparkline-js">
    <script th:src="@{/ajax/libs/report/sparkline/jquery.sparkline.min.js}"></script>
</div>

<!-- 表格拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder/bootstrap-table-reorder.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder/jquery.tablednd.js}"></script>
</div>

<!-- 表格列宽拖动插件 -->
<div th:fragment="bootstrap-table-resizable-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/jquery.resizableColumns.min.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/bootstrap-table-resizable.min.js?v=20201129}"></script>
</div>

<!-- 表格行内编辑插件 -->
<div th:fragment="bootstrap-editable-css">
    <link th:href="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-table-editable-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.min.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-table-editable.min.js?v=20201129}"></script>
</div>

<!-- 表格导出插件 -->
<div th:fragment="bootstrap-table-export-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/bootstrap-table-export.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/tableExport.js}"></script>
</div>

<!-- 表格冻结列插件 -->
<div th:fragment="bootstrap-table-fixed-columns-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/columns/bootstrap-table-fixed-columns.min.js?v=20201129}"></script>
</div>

<!-- 表格自动刷新插件 -->
<div th:fragment="bootstrap-table-auto-refresh-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/auto-refresh/bootstrap-table-auto-refresh.min.js?v=20201129}"></script>
</div>

<!-- 表格打印插件 -->
<div th:fragment="bootstrap-table-print-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/print/bootstrap-table-print.min.js?v=20200729}"></script>
</div>
