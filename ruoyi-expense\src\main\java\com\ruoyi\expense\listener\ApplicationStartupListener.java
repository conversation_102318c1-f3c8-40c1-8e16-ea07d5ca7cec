package com.ruoyi.expense.listener;

import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 应用启动监听器
 * 记录应用启动信息和注册的控制器
 */
@Component
public class ApplicationStartupListener implements ApplicationListener<ContextRefreshedEvent> {

    private static final Logger log = LoggerFactory.getLogger(ApplicationStartupListener.class);

    @Resource
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @Resource
    private Environment environment;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            // 记录应用属性信息
            String[] activeProfiles = environment.getActiveProfiles();
            log.info("应用启动 - 活动的配置文件: {}", Arrays.toString(activeProfiles));
            
            String serverPort = environment.getProperty("server.port", "未设置");
            String contextPath = environment.getProperty("server.servlet.context-path", "");
            log.info("应用启动 - 服务器端口: {}, 上下文路径: {}", serverPort, contextPath);
            
            // 输出所有注册的URL映射
            log.info("所有注册的URL映射:");
            Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
            handlerMethods.forEach((info, method) -> {
                log.info("{} => {}.{}", info, method.getBeanType().getName(), method.getMethod().getName());
            });
        }
    }
} 