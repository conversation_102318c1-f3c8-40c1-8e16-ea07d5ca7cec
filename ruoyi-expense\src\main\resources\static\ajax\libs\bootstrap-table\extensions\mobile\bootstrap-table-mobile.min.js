/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t,e){return t(e={exports:{}},e.exports),e.exports}var r=function(t){return t&&t.Math==Math&&t},o=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),c={}.propertyIsEnumerable,a=Object.getOwnPropertyDescriptor,f={f:a&&!c.call({1:2},1)?function(t){var e=a(this,t);return!!e&&e.enumerable}:c},l=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},s={}.toString,h=function(t){return s.call(t).slice(8,-1)},p="".split,d=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==h(t)?p.call(t,""):Object(t)}:Object,y=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},g=function(t){return d(y(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},m=function(t,e){if(!v(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!v(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!v(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!v(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")},w={}.hasOwnProperty,b=function(t,e){return w.call(t,e)},O=o.document,S=v(O)&&v(O.createElement),T=function(t){return S?O.createElement(t):{}},j=!u&&!i((function(){return 7!=Object.defineProperty(T("div"),"a",{get:function(){return 7}}).a})),E=Object.getOwnPropertyDescriptor,L={f:u?E:function(t,e){if(t=g(t),e=m(e,!0),j)try{return E(t,e)}catch(t){}if(b(t,e))return l(!f.f.call(t,e),t[e])}},P=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},C=Object.defineProperty,V={f:u?C:function(t,e,n){if(P(t),e=m(e,!0),P(n),j)try{return C(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},x=u?function(t,e,n){return V.f(t,e,l(1,n))}:function(t,e,n){return t[e]=n,t},M=function(t,e){try{x(o,t,e)}catch(n){o[t]=e}return e},k="__core-js_shared__",A=o[k]||M(k,{}),_=Function.toString;"function"!=typeof A.inspectSource&&(A.inspectSource=function(t){return _.call(t)});var H,R,I,W=A.inspectSource,F=o.WeakMap,N="function"==typeof F&&/native code/.test(W(F)),D=n((function(t){(t.exports=function(t,e){return A[t]||(A[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),G=0,q=Math.random(),z=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++G+q).toString(36)},B=D("keys"),X=function(t){return B[t]||(B[t]=z(t))},K={},Q=o.WeakMap;if(N){var U=new Q,Y=U.get,J=U.has,Z=U.set;H=function(t,e){return Z.call(U,t,e),e},R=function(t){return Y.call(U,t)||{}},I=function(t){return J.call(U,t)}}else{var $=X("state");K[$]=!0,H=function(t,e){return x(t,$,e),e},R=function(t){return b(t,$)?t[$]:{}},I=function(t){return b(t,$)}}var tt,et,nt={set:H,get:R,has:I,enforce:function(t){return I(t)?R(t):H(t,{})},getterFor:function(t){return function(e){var n;if(!v(e)||(n=R(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}}},rt=n((function(t){var e=nt.get,n=nt.enforce,r=String(String).split("String");(t.exports=function(t,e,i,u){var c=!!u&&!!u.unsafe,a=!!u&&!!u.enumerable,f=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof e||b(i,"name")||x(i,"name",e),n(i).source=r.join("string"==typeof e?e:"")),t!==o?(c?!f&&t[e]&&(a=!0):delete t[e],a?t[e]=i:x(t,e,i)):a?t[e]=i:M(e,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||W(this)}))})),ot=o,it=function(t){return"function"==typeof t?t:void 0},ut=function(t,e){return arguments.length<2?it(ot[t])||it(o[t]):ot[t]&&ot[t][e]||o[t]&&o[t][e]},ct=Math.ceil,at=Math.floor,ft=function(t){return isNaN(t=+t)?0:(t>0?at:ct)(t)},lt=Math.min,st=function(t){return t>0?lt(ft(t),9007199254740991):0},ht=Math.max,pt=Math.min,dt=function(t){return function(e,n,r){var o,i=g(e),u=st(i.length),c=function(t,e){var n=ft(t);return n<0?ht(n+e,0):pt(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},yt={includes:dt(!0),indexOf:dt(!1)},gt=yt.indexOf,vt=function(t,e){var n,r=g(t),o=0,i=[];for(n in r)!b(K,n)&&b(r,n)&&i.push(n);for(;e.length>o;)b(r,n=e[o++])&&(~gt(i,n)||i.push(n));return i},mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],wt=mt.concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return vt(t,wt)}},Ot={f:Object.getOwnPropertySymbols},St=ut("Reflect","ownKeys")||function(t){var e=bt.f(P(t)),n=Ot.f;return n?e.concat(n(t)):e},Tt=function(t,e){for(var n=St(e),r=V.f,o=L.f,i=0;i<n.length;i++){var u=n[i];b(t,u)||r(t,u,o(e,u))}},jt=/#|\.prototype\./,Et=function(t,e){var n=Pt[Lt(t)];return n==Vt||n!=Ct&&("function"==typeof e?i(e):!!e)},Lt=Et.normalize=function(t){return String(t).replace(jt,".").toLowerCase()},Pt=Et.data={},Ct=Et.NATIVE="N",Vt=Et.POLYFILL="P",xt=Et,Mt=L.f,kt=function(t,e){var n,r,i,u,c,a=t.target,f=t.global,l=t.stat;if(n=f?o:l?o[a]||M(a,{}):(o[a]||{}).prototype)for(r in e){if(u=e[r],i=t.noTargetGet?(c=Mt(n,r))&&c.value:n[r],!xt(f?r:a+(l?".":"#")+r,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;Tt(u,i)}(t.sham||i&&i.sham)&&x(u,"sham",!0),rt(n,r,u,t)}},At=Array.isArray||function(t){return"Array"==h(t)},_t=function(t){return Object(y(t))},Ht=function(t,e,n){var r=m(e);r in t?V.f(t,r,l(0,n)):t[r]=n},Rt=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),It=Rt&&!Symbol.sham&&"symbol"==typeof Symbol(),Wt=D("wks"),Ft=o.Symbol,Nt=It?Ft:z,Dt=function(t){return b(Wt,t)||(Rt&&b(Ft,t)?Wt[t]=Ft[t]:Wt[t]=Nt("Symbol."+t)),Wt[t]},Gt=Dt("species"),qt=function(t,e){var n;return At(t)&&("function"!=typeof(n=t.constructor)||n!==Array&&!At(n.prototype)?v(n)&&null===(n=n[Gt])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===e?0:e)},zt=ut("navigator","userAgent")||"",Bt=o.process,Xt=Bt&&Bt.versions,Kt=Xt&&Xt.v8;Kt?et=(tt=Kt.split("."))[0]+tt[1]:zt&&(!(tt=zt.match(/Edge\/(\d+)/))||tt[1]>=74)&&(tt=zt.match(/Chrome\/(\d+)/))&&(et=tt[1]);var Qt,Ut=et&&+et,Yt=Dt("species"),Jt=Dt("isConcatSpreadable"),Zt=9007199254740991,$t="Maximum allowed index exceeded",te=Ut>=51||!i((function(){var t=[];return t[Jt]=!1,t.concat()[0]!==t})),ee=(Qt="concat",Ut>=51||!i((function(){var t=[];return(t.constructor={})[Yt]=function(){return{foo:1}},1!==t[Qt](Boolean).foo}))),ne=function(t){if(!v(t))return!1;var e=t[Jt];return void 0!==e?!!e:At(t)};kt({target:"Array",proto:!0,forced:!te||!ee},{concat:function(t){var e,n,r,o,i,u=_t(this),c=qt(u,0),a=0;for(e=-1,r=arguments.length;e<r;e++)if(ne(i=-1===e?u:arguments[e])){if(a+(o=st(i.length))>Zt)throw TypeError($t);for(n=0;n<o;n++,a++)n in i&&Ht(c,a,i[n])}else{if(a>=Zt)throw TypeError($t);Ht(c,a++,i)}return c.length=a,c}});var re,oe=Object.keys||function(t){return vt(t,mt)},ie=u?Object.defineProperties:function(t,e){P(t);for(var n,r=oe(e),o=r.length,i=0;o>i;)V.f(t,n=r[i++],e[n]);return t},ue=ut("document","documentElement"),ce=X("IE_PROTO"),ae=function(){},fe=function(t){return"<script>"+t+"</"+"script>"},le=function(){try{re=document.domain&&new ActiveXObject("htmlfile")}catch(t){}var t,e;le=re?function(t){t.write(fe("")),t.close();var e=t.parentWindow.Object;return t=null,e}(re):((e=T("iframe")).style.display="none",ue.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(fe("document.F=Object")),t.close(),t.F);for(var n=mt.length;n--;)delete le.prototype[mt[n]];return le()};K[ce]=!0;var se=Object.create||function(t,e){var n;return null!==t?(ae.prototype=P(t),n=new ae,ae.prototype=null,n[ce]=t):n=le(),void 0===e?n:ie(n,e)},he=Dt("unscopables"),pe=Array.prototype;null==pe[he]&&V.f(pe,he,{configurable:!0,value:se(null)});var de,ye=yt.includes;kt({target:"Array",proto:!0},{includes:function(t){return ye(this,t,arguments.length>1?arguments[1]:void 0)}}),de="includes",pe[he][de]=!0;var ge=Dt("match"),ve=function(t){if(function(t){var e;return v(t)&&(void 0!==(e=t[ge])?!!e:"RegExp"==h(t))}(t))throw TypeError("The method doesn't accept regular expressions");return t},me=Dt("match");kt({target:"String",proto:!0,forced:!function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[me]=!1,"/./"[t](e)}catch(t){}}return!1}("includes")},{includes:function(t){return!!~String(y(this)).indexOf(ve(t),arguments.length>1?arguments[1]:void 0)}});var we=function(t,e,n){if(function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function")}(t),void 0===e)return t;switch(n){case 0:return function(){return t.call(e)};case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,o){return t.call(e,n,r,o)}}return function(){return t.apply(e,arguments)}},be=[].push,Oe=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=5==t||i;return function(c,a,f,l){for(var s,h,p=_t(c),y=d(p),g=we(a,f,3),v=st(y.length),m=0,w=l||qt,b=e?w(c,v):n?w(c,0):void 0;v>m;m++)if((u||m in y)&&(h=g(s=y[m],m,p),t))if(e)b[m]=h;else if(h)switch(t){case 3:return!0;case 5:return s;case 6:return m;case 2:be.call(b,s)}else if(o)return!1;return i?-1:r||o?o:b}},Se={forEach:Oe(0),map:Oe(1),filter:Oe(2),some:Oe(3),every:Oe(4),find:Oe(5),findIndex:Oe(6)}.forEach,Te=function(t,e){var n=[][t];return!n||!i((function(){n.call(null,e||function(){throw 1},1)}))}("forEach")?function(t){return Se(this,t,arguments.length>1?arguments[1]:void 0)}:[].forEach;for(var je in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var Ee=o[je],Le=Ee&&Ee.prototype;if(Le&&Le.forEach!==Te)try{x(Le,"forEach",Te)}catch(t){Le.forEach=Te}}function Pe(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ce(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function Ve(t){return(Ve=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function xe(t,e){return(xe=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function Me(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function ke(t,e,n){return(ke="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Ve(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(n):o.value}})(t,e,n||t)}var Ae=function(t,e){var n=0;return function(){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];var u=function(){n=0,t.apply(void 0,o)};clearTimeout(n),n=setTimeout(u,e)}};t.extend(t.fn.bootstrapTable.defaults,{mobileResponsive:!1,minWidth:562,minHeight:void 0,heightThreshold:100,checkOnInit:!0,columnsHidden:[]}),t.BootstrapTable=function(e){function n(){return Pe(this,n),Me(this,Ve(n).apply(this,arguments))}var r,o,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&xe(t,e)}(n,e),r=n,(o=[{key:"init",value:function(){for(var e,r=this,o=arguments.length,i=new Array(o),u=0;u<o;u++)i[u]=arguments[u];if((e=ke(Ve(n.prototype),"init",this)).call.apply(e,[this].concat(i)),this.options.mobileResponsive&&this.options.minWidth){this.options.minWidth<100&&this.options.resizable&&(console.info("The minWidth when the resizable extension is active should be greater or equal than 100"),this.options.minWidth=100);var c={width:t(window).width(),height:t(window).height()};if(t(window).on("resize orientationchange",Ae((function(){var e=t(window).width(),n=t(window).height(),o=t(document.activeElement);o.length&&["INPUT","SELECT","TEXTAREA"].includes(o.prop("nodeName"))||(Math.abs(c.height-n)>r.options.heightThreshold||c.width!==e)&&(r.changeView(e,n),c={width:e,height:n})}),200)),this.options.checkOnInit){var a=t(window).width(),f=t(window).height();this.changeView(a,f),c={width:a,height:f}}}}},{key:"conditionCardView",value:function(){this.changeTableView(!1),this.showHideColumns(!1)}},{key:"conditionFullView",value:function(){this.changeTableView(!0),this.showHideColumns(!0)}},{key:"changeTableView",value:function(t){this.options.cardView=t,this.toggleView()}},{key:"showHideColumns",value:function(t){var e=this;this.options.columnsHidden.length>0&&this.columns.forEach((function(n){e.options.columnsHidden.includes(n.field)&&n.visible!==t&&e._toggleColumn(e.fieldsColumnsIndex[n.field],t,!0)}))}},{key:"changeView",value:function(t,e){this.options.minHeight?t<=this.options.minWidth&&e<=this.options.minHeight?this.conditionCardView():t>this.options.minWidth&&e>this.options.minHeight&&this.conditionFullView():t<=this.options.minWidth?this.conditionCardView():t>this.options.minWidth&&this.conditionFullView(),this.resetView()}}])&&Ce(r.prototype,o),i&&Ce(r,i),n}(t.BootstrapTable)}));