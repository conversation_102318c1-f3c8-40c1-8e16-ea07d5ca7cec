<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分摊表基本信息 - 优化预览</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* 分摊表详情页面样式优化 */

        /* 基本信息区域整体样式 */
        .allocation-detail-container {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            overflow: hidden;
            margin: 20px auto;
            max-width: 1200px;
        }

        .allocation-detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            margin: 0;
            border-bottom: 3px solid #5a67d8;
        }

        .allocation-detail-header h5 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .allocation-detail-header h5 i {
            margin-right: 10px;
            font-size: 16px;
        }

        .allocation-detail-content {
            padding: 25px;
            background: #fafbfc;
        }

        /* 信息行样式 */
        .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #667eea;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        /* 信息项样式 */
        .info-item {
            flex: 1;
            min-width: 250px;
            margin-bottom: 15px;
            padding-right: 20px;
        }

        .info-item:last-child {
            padding-right: 0;
        }

        .info-item.full-width {
            flex: 1 1 100%;
            min-width: 100%;
        }

        .info-item.half-width {
            flex: 1 1 50%;
            min-width: 300px;
        }

        .info-item.third-width {
            flex: 1 1 33.333%;
            min-width: 200px;
        }

        /* 标签样式 */
        .info-label {
            display: inline-block;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 14px;
            min-width: 80px;
            position: relative;
        }

        .info-label::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: #667eea;
            border-radius: 1px;
        }

        /* 内容样式 */
        .info-value {
            display: block;
            color: #2d3748;
            font-size: 15px;
            line-height: 1.5;
            padding: 8px 12px;
            background: #f7fafc;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            min-height: 38px;
            display: flex;
            align-items: center;
        }

        /* 状态徽章优化 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-badge.status-pending {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            color: #d63031;
            border: 1px solid #fdcb6e;
        }

        .status-badge.status-approved {
            background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);
            color: #00695c;
            border: 1px solid #00b894;
        }

        .status-badge.status-rejected {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: #ffffff;
            border: 1px solid #e84393;
        }

        /* 修改意见特殊样式 */
        .comments-section {
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-left: 4px solid #ff9800;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .comments-section .info-label {
            color: #e65100;
            font-weight: 700;
        }

        .comments-section .info-value {
            background: #ffffff;
            border: 1px solid #ffcc02;
            color: #bf360c;
            font-style: italic;
            min-height: auto;
            padding: 12px 15px;
            line-height: 1.6;
        }

        /* 时间信息特殊样式 */
        .time-info .info-value {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
            font-family: 'Courier New', monospace;
        }

        /* 人员信息样式 */
        .person-info .info-value {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1565c0;
        }

        /* 基本信息样式 */
        .basic-info .info-value {
            background: #f3e5f5;
            border-color: #9c27b0;
            color: #6a1b9a;
        }

        /* 图标样式 */
        .info-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .allocation-detail-content {
                padding: 15px;
            }
            
            .info-row {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .info-item {
                flex: 1 1 100%;
                min-width: 100%;
                padding-right: 0;
                margin-bottom: 15px;
            }
            
            .info-item.half-width,
            .info-item.third-width {
                flex: 1 1 100%;
                min-width: 100%;
            }
            
            .allocation-detail-header {
                padding: 15px 20px;
            }
            
            .allocation-detail-header h5 {
                font-size: 16px;
            }
        }

        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .container {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="allocation-detail-container">
            <div class="allocation-detail-header">
                <h5><i class="fa fa-info-circle"></i>分摊表基本信息</h5>
            </div>
            <div class="allocation-detail-content">
                <!-- 基本信息行 -->
                <div class="info-row basic-info">
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-file-text info-icon"></i>分摊表名称</span>
                        <span class="info-value">202506-电子设备配件-分摊表</span>
                    </div>
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-flag info-icon"></i>状态</span>
                        <span class="info-value">
                            <span class="status-badge status-pending">审核中</span>
                        </span>
                    </div>
                </div>

                <!-- 费用信息行 -->
                <div class="info-row basic-info">
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-tags info-icon"></i>费用类型</span>
                        <span class="info-value">电子设备配件</span>
                    </div>
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-calendar info-icon"></i>计费周期</span>
                        <span class="info-value">202506</span>
                    </div>
                </div>

                <!-- 人员信息行 -->
                <div class="info-row person-info">
                    <div class="info-item third-width">
                        <span class="info-label"><i class="fa fa-user info-icon"></i>制表人</span>
                        <span class="info-value">王海东</span>
                    </div>
                    <div class="info-item third-width">
                        <span class="info-label"><i class="fa fa-user-check info-icon"></i>复核人</span>
                        <span class="info-value">若依</span>
                    </div>
                    <div class="info-item third-width">
                        <span class="info-label"><i class="fa fa-user-cog info-icon"></i>负责人</span>
                        <span class="info-value"></span>
                    </div>
                </div>

                <!-- 时间信息行 -->
                <div class="info-row time-info">
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-clock-o info-icon"></i>创建时间</span>
                        <span class="info-value">2025.08.01 17:05:00</span>
                    </div>
                    <div class="info-item half-width">
                        <span class="info-label"><i class="fa fa-check-circle info-icon"></i>确认时间</span>
                        <span class="info-value">未确认</span>
                    </div>
                </div>

                <!-- 修改意见（条件显示） -->
                <div class="comments-section">
                    <div class="info-item full-width">
                        <span class="info-label"><i class="fa fa-comment info-icon"></i>修改意见</span>
                        <span class="info-value">请核实部分账单的分摊部门信息，确保准确性。</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
