package com.ruoyi.expense.service;

import com.ruoyi.expense.domain.BillUploadDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 账单上传服务接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface IBillUploadService {
    
    /**
     * 处理账单上传
     * 
     * @param uploadDTO 上传数据传输对象
     * @return 处理结果
     */
    String processBillUpload(BillUploadDTO uploadDTO);
    
    /**
     * 解析Excel文件
     * 
     * @param file Excel文件
     * @param uploadDTO 上传数据传输对象
     * @return 处理结果
     */
    String parseExcelFile(MultipartFile file, BillUploadDTO uploadDTO);
    
    /**
     * 验证Excel文件格式
     * 
     * @param file 上传的文件
     * @return 是否为有效的xlsx文件
     */
    boolean validateExcelFile(MultipartFile file);
    
    /**
     * 验证Excel文件内容格式
     * 
     * @param file Excel文件
     * @return 验证结果消息
     */
    String validateExcelContent(MultipartFile file);
    
    /**
     * 验证账单数量匹配
     * 
     * @param file Excel文件
     * @param expectedCount 期望的账单数量
     * @return 验证结果消息
     */
    String validateBillCount(MultipartFile file, Integer expectedCount);
    
    /**
     * 发送数据到receive接口
     * 
     * @param jsonData JSON格式的数据
     * @return 发送结果
     */
    String sendDataToReceive(String jsonData);
}