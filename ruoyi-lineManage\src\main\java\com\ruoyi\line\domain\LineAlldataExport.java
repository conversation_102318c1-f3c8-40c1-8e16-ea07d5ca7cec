package com.ruoyi.line.domain;

import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 通讯线路管理导出对象 用于导出可视化数据
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public class LineAlldataExport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 线路代码 */
    @Excel(name = "线路代码")
    private Long lineCode;

    /** 线路编号 */
    @Excel(name = "线路编号")
    private String lineNumber;

    /** 所属机构 */
    @Excel(name = "所属机构")
    private String institution;

    /** 运营商 */
    @Excel(name = "运营商")
    private String operator;

    /** 线路名称 */
    @Excel(name = "线路名称")
    private String lineName;

    /** 线路类型 */
    @Excel(name = "线路类型")
    private String lineType;

    /** 线路带宽 */
    @Excel(name = "线路带宽")
    private String lineBandwidth;

    /** 线路新增日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "线路新增日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lineAddDate;

    /** 线路撤销日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "线路撤销日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lineCancelDate;

    /** 含税价 */
    @Excel(name = "含税价")
    private BigDecimal taxIncludingPrice;

    /** 线路使用环境 */
    @Excel(name = "线路使用环境")
    private String lineUsageEnvironment;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 对端联系人 */
    @Excel(name = "对端联系人")
    private String oppositeContact;

    /** 线路状态 */
    @Excel(name = "线路状态")
    private String lineStatus;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal taxRate;

    /** 不含税价 */
    @Excel(name = "不含税价")
    private BigDecimal taxExcludingPrice;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    public void setLineCode(Long lineCode) 
    {
        this.lineCode = lineCode;
    }

    public Long getLineCode() 
    {
        return lineCode;
    }
    public void setLineNumber(String lineNumber) 
    {
        this.lineNumber = lineNumber;
    }

    public String getLineNumber() 
    {
        return lineNumber;
    }
    public void setInstitution(String institution) 
    {
        this.institution = institution;
    }

    public String getInstitution() 
    {
        return institution;
    }
    public void setOperator(String operator) 
    {
        this.operator = operator;
    }

    public String getOperator() 
    {
        return operator;
    }
    public void setLineName(String lineName) 
    {
        this.lineName = lineName;
    }

    public String getLineName() 
    {
        return lineName;
    }
    public void setLineType(String lineType) 
    {
        this.lineType = lineType;
    }

    public String getLineType() 
    {
        return lineType;
    }
    public void setLineBandwidth(String lineBandwidth) 
    {
        this.lineBandwidth = lineBandwidth;
    }

    public String getLineBandwidth() 
    {
        return lineBandwidth;
    }
    public void setLineAddDate(Date lineAddDate) 
    {
        this.lineAddDate = lineAddDate;
    }

    public Date getLineAddDate() 
    {
        return lineAddDate;
    }
    public void setLineCancelDate(Date lineCancelDate) 
    {
        this.lineCancelDate = lineCancelDate;
    }

    public Date getLineCancelDate() 
    {
        return lineCancelDate;
    }
    public void setTaxIncludingPrice(BigDecimal taxIncludingPrice) 
    {
        this.taxIncludingPrice = taxIncludingPrice;
    }

    public BigDecimal getTaxIncludingPrice() 
    {
        return taxIncludingPrice;
    }
    public void setLineUsageEnvironment(String lineUsageEnvironment) 
    {
        this.lineUsageEnvironment = lineUsageEnvironment;
    }

    public String getLineUsageEnvironment() 
    {
        return lineUsageEnvironment;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setOppositeContact(String oppositeContact) 
    {
        this.oppositeContact = oppositeContact;
    }

    public String getOppositeContact() 
    {
        return oppositeContact;
    }
    public void setLineStatus(String lineStatus) 
    {
        this.lineStatus = lineStatus;
    }

    public String getLineStatus() 
    {
        return lineStatus;
    }
    public void setTaxRate(BigDecimal taxRate) 
    {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxRate() 
    {
        return taxRate;
    }
    public void setTaxExcludingPrice(BigDecimal taxExcludingPrice) 
    {
        this.taxExcludingPrice = taxExcludingPrice;
    }

    public BigDecimal getTaxExcludingPrice() 
    {
        return taxExcludingPrice;
    }
    public void setRemark(String remark) 
    {
        this.remark = remark;
    }

    public String getRemark() 
    {
        return remark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("lineCode", getLineCode())
            .append("lineNumber", getLineNumber())
            .append("institution", getInstitution())
            .append("operator", getOperator())
            .append("lineName", getLineName())
            .append("lineType", getLineType())
            .append("lineBandwidth", getLineBandwidth())
            .append("lineAddDate", getLineAddDate())
            .append("lineCancelDate", getLineCancelDate())
            .append("taxIncludingPrice", getTaxIncludingPrice())
            .append("lineUsageEnvironment", getLineUsageEnvironment())
            .append("address", getAddress())
            .append("oppositeContact", getOppositeContact())
            .append("lineStatus", getLineStatus())
            .append("taxRate", getTaxRate())
            .append("taxExcludingPrice", getTaxExcludingPrice())
            .append("remark", getRemark())
            .toString();
    }
} 