<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增用户')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-user-add">
			<div class="form-group">
				<label class="col-sm-3 control-label">用户编号：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="userCode" id="userCode" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">用户姓名：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="userName" id="userName" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">用户性别：</label>
                <div class="col-sm-8">
                    <div class="input-group" style="width: 100%">
                        <select name="userSex" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}">
			                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
			            </select>
                    </div>
                </div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">用户手机：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="userPhone" id="userPhone">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">用户邮箱：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="userEmail" id="userEmail">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">用户状态：</label>
				<div class="col-sm-8">
				    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<script type="text/javascript">
		var prefix = ctx + "demo/operate";
		
		$("#form-user-add").validate({
			onkeyup: false,
			rules:{
				userPhone:{
					isPhone:true
				},
				userEmail:{
					email:true
				},
			},
		    focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/add", $('#form-user-add').serialize());
	        }
	    }
	</script>
</body>
</html>
