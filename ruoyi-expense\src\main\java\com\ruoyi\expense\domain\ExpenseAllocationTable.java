package com.ruoyi.expense.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

/**
 * 分摊表管理对象 expense_allocation_table
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ExpenseAllocationTable
{
    private static final long serialVersionUID = 1L;

    /** 主键ID，自增 */
    private Long id;

    /** 分摊表名称，例如202506-通讯线路-分摊表 */
    @Excel(name = "分摊表名称")
    private String tableName;

    /** 计费周期 */
    @Excel(name = "计费周期")
    private String billingCycle;

    /** 费用类型 */
    @Excel(name = "费用类型")
    private String expenseType;

    /** 制表人 */
    @Excel(name = "制表人")
    private String preparer;

    /** 复核人 */
    @Excel(name = "复核人")
    private String reviewer;

    /** 负责人 */
    @Excel(name = "负责人")
    private String responsiblePerson;

    /** 修改意见，支持较长文本 */
    @Excel(name = "修改意见")
    private String comments;

    /** 状态，如：草稿、已审核、已归档等 */
    @Excel(name = "状态", readConverterExp = "审核中=审核中,已审核=已审核,已拒绝=已拒绝")
    private String status;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 确认时间 */
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setTableName(String tableName) 
    {
        this.tableName = tableName;
    }

    public String getTableName() 
    {
        return tableName;
    }
    
    public void setBillingCycle(String billingCycle) 
    {
        this.billingCycle = billingCycle;
    }

    public String getBillingCycle() 
    {
        return billingCycle;
    }
    
    public void setExpenseType(String expenseType) 
    {
        this.expenseType = expenseType;
    }

    public String getExpenseType() 
    {
        return expenseType;
    }
    
    public void setPreparer(String preparer) 
    {
        this.preparer = preparer;
    }

    public String getPreparer() 
    {
        return preparer;
    }
    
    public void setReviewer(String reviewer) 
    {
        this.reviewer = reviewer;
    }

    public String getReviewer() 
    {
        return reviewer;
    }
    
    public void setResponsiblePerson(String responsiblePerson) 
    {
        this.responsiblePerson = responsiblePerson;
    }

    public String getResponsiblePerson() 
    {
        return responsiblePerson;
    }
    
    public void setComments(String comments) 
    {
        this.comments = comments;
    }

    public String getComments() 
    {
        return comments;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }
    
    public void setConfirmTime(Date confirmTime) 
    {
        this.confirmTime = confirmTime;
    }

    public Date getConfirmTime() 
    {
        return confirmTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tableName", getTableName())
            .append("billingCycle", getBillingCycle())
            .append("expenseType", getExpenseType())
            .append("preparer", getPreparer())
            .append("reviewer", getReviewer())
            .append("responsiblePerson", getResponsiblePerson())
            .append("comments", getComments())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("confirmTime", getConfirmTime())
            .toString();
    }
} 