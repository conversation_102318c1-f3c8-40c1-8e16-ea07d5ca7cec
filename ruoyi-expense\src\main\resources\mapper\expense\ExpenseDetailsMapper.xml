<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.expense.mapper.ExpenseDetailsMapper">
    
    <resultMap type="ExpenseDetails" id="ExpenseDetailsResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="number"    column="number"    />
        <result property="brand"    column="brand"    />
        <result property="specificSpecification"    column="specific_specification"    />
        <result property="expenseType"    column="expense_type"    />
        <result property="billingCycle"    column="billing_cycle"    />
        <result property="transferDepartment"    column="transfer_department"    />
        <result property="quantity"    column="quantity"    />
        <result property="expenseChangeStatus"    column="expense_change_status"    />
        <result property="remarks"    column="remarks"    />
        <result property="unitPriceIncludingTax"    column="unit_price_including_tax"    />
        <result property="unitPriceExcludingTax"    column="unit_price_excluding_tax"    />
        <result property="totalLinePriceIncludingTax"    column="total_line_price_including_tax"    />
        <result property="totalLinePriceExcludingTax"    column="total_line_price_excluding_tax"    />
    </resultMap>

    <sql id="selectExpenseDetailsVo">
        select id, name, number, brand, specific_specification, expense_type, billing_cycle, transfer_department, quantity, expense_change_status, remarks, unit_price_including_tax, unit_price_excluding_tax, total_line_price_including_tax, total_line_price_excluding_tax from expense_details
    </sql>

    <select id="selectExpenseDetailsList" parameterType="ExpenseDetails" resultMap="ExpenseDetailsResult">
        <include refid="selectExpenseDetailsVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="brand != null  and brand != ''"> and brand = #{brand}</if>
            <if test="specificSpecification != null  and specificSpecification != ''"> and specific_specification = #{specificSpecification}</if>
            <if test="expenseType != null  and expenseType != ''"> and expense_type = #{expenseType}</if>
            <if test="billingCycle != null  and billingCycle != ''"> and billing_cycle = #{billingCycle}</if>
            <if test="transferDepartment != null  and transferDepartment != ''"> and transfer_department = #{transferDepartment}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="expenseChangeStatus != null  and expenseChangeStatus != ''"> and expense_change_status = #{expenseChangeStatus}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="unitPriceIncludingTax != null "> and unit_price_including_tax = #{unitPriceIncludingTax}</if>
            <if test="unitPriceExcludingTax != null "> and unit_price_excluding_tax = #{unitPriceExcludingTax}</if>
            <if test="totalLinePriceIncludingTax != null "> and total_line_price_including_tax = #{totalLinePriceIncludingTax}</if>
            <if test="totalLinePriceExcludingTax != null "> and total_line_price_excluding_tax = #{totalLinePriceExcludingTax}</if>
        </where>
    </select>
    
    <select id="selectExpenseDetailsById" parameterType="Long" resultMap="ExpenseDetailsResult">
        <include refid="selectExpenseDetailsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertExpenseDetails" parameterType="ExpenseDetails">
        insert into expense_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="number != null">number,</if>
            <if test="brand != null">brand,</if>
            <if test="specificSpecification != null">specific_specification,</if>
            <if test="expenseType != null">expense_type,</if>
            <if test="billingCycle != null">billing_cycle,</if>
            <if test="transferDepartment != null">transfer_department,</if>
            <if test="quantity != null">quantity,</if>
            <if test="expenseChangeStatus != null">expense_change_status,</if>
            <if test="remarks != null">remarks,</if>
            <if test="unitPriceIncludingTax != null">unit_price_including_tax,</if>
            <if test="unitPriceExcludingTax != null">unit_price_excluding_tax,</if>
            <if test="totalLinePriceIncludingTax != null">total_line_price_including_tax,</if>
            <if test="totalLinePriceExcludingTax != null">total_line_price_excluding_tax,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="number != null">#{number},</if>
            <if test="brand != null">#{brand},</if>
            <if test="specificSpecification != null">#{specificSpecification},</if>
            <if test="expenseType != null">#{expenseType},</if>
            <if test="billingCycle != null">#{billingCycle},</if>
            <if test="transferDepartment != null">#{transferDepartment},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="expenseChangeStatus != null">#{expenseChangeStatus},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="unitPriceIncludingTax != null">#{unitPriceIncludingTax},</if>
            <if test="unitPriceExcludingTax != null">#{unitPriceExcludingTax},</if>
            <if test="totalLinePriceIncludingTax != null">#{totalLinePriceIncludingTax},</if>
            <if test="totalLinePriceExcludingTax != null">#{totalLinePriceExcludingTax},</if>
         </trim>
    </insert>

    <update id="updateExpenseDetails" parameterType="ExpenseDetails">
        update expense_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="number != null">number = #{number},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="specificSpecification != null">specific_specification = #{specificSpecification},</if>
            <if test="expenseType != null">expense_type = #{expenseType},</if>
            <if test="billingCycle != null">billing_cycle = #{billingCycle},</if>
            <if test="transferDepartment != null">transfer_department = #{transferDepartment},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="expenseChangeStatus != null">expense_change_status = #{expenseChangeStatus},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="unitPriceIncludingTax != null">unit_price_including_tax = #{unitPriceIncludingTax},</if>
            <if test="unitPriceExcludingTax != null">unit_price_excluding_tax = #{unitPriceExcludingTax},</if>
            <if test="totalLinePriceIncludingTax != null">total_line_price_including_tax = #{totalLinePriceIncludingTax},</if>
            <if test="totalLinePriceExcludingTax != null">total_line_price_excluding_tax = #{totalLinePriceExcludingTax},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExpenseDetailsById" parameterType="Long">
        delete from expense_details where id = #{id}
    </delete>

    <delete id="deleteExpenseDetailsByIds" parameterType="Long[]">
        delete from expense_details where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteExpenseDetailsByBillId" parameterType="Long">
        delete from expense_details where bill_id = #{billId}
    </delete>
    
    <delete id="deleteExpenseDetailsByUnique">
        delete from expense_details 
        where expense_type = #{expenseType} 
        and billing_cycle = #{billingCycle} 
        and transfer_department = #{transferDepartment}
    </delete>
</mapper>