package com.ruoyi.expense.mapper;

import com.ruoyi.expense.domain.ExpenseCheckerManage;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ExpenseCheckerManageMapper {
    List<ExpenseCheckerManage> selectCheckerManageList(ExpenseCheckerManage checkerManage);
    int insertCheckerManage(ExpenseCheckerManage checkerManage);
    int updateCheckerManage(ExpenseCheckerManage checkerManage);
    int deleteCheckerManageById(Integer id);
    int deleteCheckerManageByIds(@Param("ids") List<Integer> ids);
    ExpenseCheckerManage selectByNameAndEhrNumber(@Param("name") String name, @Param("ehrNumber") String ehrNumber);
    ExpenseCheckerManage selectByName(@Param("name") String name);
    ExpenseCheckerManage selectByEhrNumber(@Param("ehrNumber") String ehrNumber);
    ExpenseCheckerManage selectCheckerManageById(Integer id);
    
    /**
     * 根据部门名称查询核对人列表（支持一个部门多个核对人）
     * @param departmentName 部门名称
     * @return 核对人信息列表
     */
    List<ExpenseCheckerManage> selectByDepartmentName(@Param("departmentName") String departmentName);
} 