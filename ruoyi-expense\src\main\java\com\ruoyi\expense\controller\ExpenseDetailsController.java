package com.ruoyi.expense.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.expense.domain.ExpenseDetails;
import com.ruoyi.expense.service.IExpenseDetailsService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 费用明细Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Controller
@RequestMapping("/expense/details")
public class ExpenseDetailsController extends BaseController
{
    private String prefix = "expense/details";

    @Autowired
    private IExpenseDetailsService expenseDetailsService;

    @RequiresPermissions("expense:details:view")
    @GetMapping()
    public String details()
    {
        return prefix + "/details";
    }

    /**
     * 查询费用明细列表
     */
    @RequiresPermissions("expense:details:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExpenseDetails expenseDetails)
    {
        startPage();
        List<ExpenseDetails> list = expenseDetailsService.selectExpenseDetailsList(expenseDetails);
        return getDataTable(list);
    }

    /**
     * 导出费用明细列表
     */
    @RequiresPermissions("expense:details:export")
    @Log(title = "费用明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ExpenseDetails expenseDetails)
    {
        List<ExpenseDetails> list = expenseDetailsService.selectExpenseDetailsList(expenseDetails);
        ExcelUtil<ExpenseDetails> util = new ExcelUtil<ExpenseDetails>(ExpenseDetails.class);
        return util.exportExcel(list, "details");
    }

    /**
     * 新增费用明细
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存费用明细
     */
    @RequiresPermissions("expense:details:add")
    @Log(title = "费用明细", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ExpenseDetails expenseDetails)
    {
        return toAjax(expenseDetailsService.insertExpenseDetails(expenseDetails));
    }

    /**
     * 修改费用明细
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        ExpenseDetails expenseDetails = expenseDetailsService.selectExpenseDetailsById(id);
        mmap.put("expenseDetails", expenseDetails);
        return prefix + "/edit";
    }

    /**
     * 修改保存费用明细
     */
    @RequiresPermissions("expense:details:edit")
    @Log(title = "费用明细", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ExpenseDetails expenseDetails)
    {
        return toAjax(expenseDetailsService.updateExpenseDetails(expenseDetails));
    }

    /**
     * 删除费用明细
     */
    @RequiresPermissions("expense:details:remove")
    @Log(title = "费用明细", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(expenseDetailsService.deleteExpenseDetailsByIds(ids));
    }
}
