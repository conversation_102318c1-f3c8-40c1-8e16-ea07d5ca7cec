package com.ruoyi.line.domain;

import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 线路管理对象 line_alldata
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
public class LineAlldata extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @Excel(name = "")
    private Long lineCode;

    /**  */
    @Excel(name = "")
    private String lineNumber;

    /**  */
    @Excel(name = "")
    private String institution;

    /**  */
    @Excel(name = "")
    private String operator;

    /**  */
    @Excel(name = "")
    private String lineName;

    /**  */
    @Excel(name = "")
    private String lineType;

    /**  */
    @Excel(name = "")
    private String lineBandwidth;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lineAddDate;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lineCancelDate;

    /**  */
    @Excel(name = "")
    private BigDecimal taxIncludingPrice;

    /**  */
    @Excel(name = "")
    private String lineUsageEnvironment;

    /**  */
    @Excel(name = "")
    private String address;

    /**  */
    @Excel(name = "")
    private String oppositeContact;

    /**  */
    @Excel(name = "")
    private String lineStatus;

    /**  */
    @Excel(name = "")
    private BigDecimal taxRate;

    /**  */
    @Excel(name = "")
    private BigDecimal taxExcludingPrice;

    /** 关键词搜索字段(不映射数据库) */
    private String keyword;

    public void setLineCode(Long lineCode)
    {
        this.lineCode = lineCode;
    }

    public Long getLineCode()
    {
        return lineCode;
    }
    public void setLineNumber(String lineNumber)
    {
        this.lineNumber = lineNumber;
    }

    public String getLineNumber()
    {
        return lineNumber;
    }
    public void setInstitution(String institution)
    {
        this.institution = institution;
    }

    public String getInstitution()
    {
        return institution;
    }
    public void setOperator(String operator)
    {
        this.operator = operator;
    }

    public String getOperator()
    {
        return operator;
    }
    public void setLineName(String lineName)
    {
        this.lineName = lineName;
    }

    public String getLineName()
    {
        return lineName;
    }
    public void setLineType(String lineType)
    {
        this.lineType = lineType;
    }

    public String getLineType()
    {
        return lineType;
    }
    public void setLineBandwidth(String lineBandwidth)
    {
        this.lineBandwidth = lineBandwidth;
    }

    public String getLineBandwidth()
    {
        return lineBandwidth;
    }
    public void setLineAddDate(Date lineAddDate)
    {
        this.lineAddDate = lineAddDate;
    }

    public Date getLineAddDate()
    {
        return lineAddDate;
    }
    public void setLineCancelDate(Date lineCancelDate)
    {
        this.lineCancelDate = lineCancelDate;
    }

    public Date getLineCancelDate()
    {
        return lineCancelDate;
    }
    public void setTaxIncludingPrice(BigDecimal taxIncludingPrice)
    {
        this.taxIncludingPrice = taxIncludingPrice;
    }

    public BigDecimal getTaxIncludingPrice()
    {
        return taxIncludingPrice;
    }
    public void setLineUsageEnvironment(String lineUsageEnvironment)
    {
        this.lineUsageEnvironment = lineUsageEnvironment;
    }

    public String getLineUsageEnvironment()
    {
        return lineUsageEnvironment;
    }
    public void setAddress(String address)
    {
        this.address = address;
    }

    public String getAddress()
    {
        return address;
    }
    public void setOppositeContact(String oppositeContact)
    {
        this.oppositeContact = oppositeContact;
    }

    public String getOppositeContact()
    {
        return oppositeContact;
    }
    public void setLineStatus(String lineStatus)
    {
        this.lineStatus = lineStatus;
    }

    public String getLineStatus()
    {
        return lineStatus;
    }
    public void setTaxRate(BigDecimal taxRate)
    {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxRate()
    {
        return taxRate;
    }
    public void setTaxExcludingPrice(BigDecimal taxExcludingPrice)
    {
        this.taxExcludingPrice = taxExcludingPrice;
    }

    public BigDecimal getTaxExcludingPrice()
    {
        return taxExcludingPrice;
    }
    
    public void setKeyword(String keyword)
    {
        this.keyword = keyword;
    }

    public String getKeyword()
    {
        return keyword;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("lineCode", getLineCode())
                .append("lineNumber", getLineNumber())
                .append("institution", getInstitution())
                .append("operator", getOperator())
                .append("lineName", getLineName())
                .append("lineType", getLineType())
                .append("lineBandwidth", getLineBandwidth())
                .append("lineAddDate", getLineAddDate())
                .append("lineCancelDate", getLineCancelDate())
                .append("taxIncludingPrice", getTaxIncludingPrice())
                .append("lineUsageEnvironment", getLineUsageEnvironment())
                .append("address", getAddress())
                .append("oppositeContact", getOppositeContact())
                .append("lineStatus", getLineStatus())
                .append("taxRate", getTaxRate())
                .append("remark", getRemark())
                .append("taxExcludingPrice", getTaxExcludingPrice())
                .toString();
    }
}
