package com.ruoyi.expense.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 账单数据对象 expense_bills
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public class ExpenseBill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账单编号 */
    private Long bill_id;

    /** 费用类型 */
    @Excel(name = "费用类型")
    private String expense_type;

    /** 计费周期 */
    @Excel(name = "计费周期")
    private String billing_cycle;

    /** 划账部门或支行 */
    @Excel(name = "划账部门或支行")
    private String transfer_department;

    /** 账单状态（0已入库 1已发布 2已确认 3已退回） */
    @Excel(name = "账单状态", readConverterExp = "0=已入库,1=已发布,2=已确认,3=已退回")
    private String status;

    /** 账单发布人 */
    @Excel(name = "账单发布人")
    private String bill_publisher;

    /** 账单确认人 */
    @Excel(name = "账单确认人")
    private String bill_confirmer;

    /** 账单退回人 */
    @Excel(name = "账单退回人")
    private String bill_returner;

    /** 账单退回意见 */
    @Excel(name = "账单退回意见")
    private String bill_refuse_comment;

    /** 含税总价 */
    @Excel(name = "含税总价")
    private BigDecimal total_price_with_tax;

    /** 不含税总价 */
    @Excel(name = "不含税总价")
    private BigDecimal total_price_without_tax;

    public void setBill_id(Long bill_id) 
    {
        this.bill_id = bill_id;
    }

    public Long getBill_id() 
    {
        return bill_id;
    }
    
    public void setExpense_type(String expense_type) 
    {
        this.expense_type = expense_type;
    }

    public String getExpense_type() 
    {
        return expense_type;
    }
    
    public void setBilling_cycle(String billing_cycle) 
    {
        this.billing_cycle = billing_cycle;
    }

    public String getBilling_cycle() 
    {
        return billing_cycle;
    }
    
    public void setTransfer_department(String transfer_department) 
    {
        this.transfer_department = transfer_department;
    }

    public String getTransfer_department() 
    {
        return transfer_department;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public void setBill_publisher(String bill_publisher) 
    {
        this.bill_publisher = bill_publisher;
    }

    public String getBill_publisher() 
    {
        return bill_publisher;
    }
    
    public void setBill_confirmer(String bill_confirmer) 
    {
        this.bill_confirmer = bill_confirmer;
    }

    public String getBill_confirmer() 
    {
        return bill_confirmer;
    }
    
    public void setBill_returner(String bill_returner) 
    {
        this.bill_returner = bill_returner;
    }

    public String getBill_returner() 
    {
        return bill_returner;
    }
    
    public void setBill_refuse_comment(String bill_refuse_comment) 
    {
        this.bill_refuse_comment = bill_refuse_comment;
    }

    public String getBill_refuse_comment() 
    {
        return bill_refuse_comment;
    }
    
    public void setTotal_price_with_tax(BigDecimal total_price_with_tax) 
    {
        this.total_price_with_tax = total_price_with_tax;
    }

    public BigDecimal getTotal_price_with_tax() 
    {
        return total_price_with_tax;
    }
    
    public void setTotal_price_without_tax(BigDecimal total_price_without_tax) 
    {
        this.total_price_without_tax = total_price_without_tax;
    }

    public BigDecimal getTotal_price_without_tax() 
    {
        return total_price_without_tax;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("bill_id", getBill_id())
            .append("expense_type", getExpense_type())
            .append("billing_cycle", getBilling_cycle())
            .append("transfer_department", getTransfer_department())
            .append("status", getStatus())
            .append("bill_publisher", getBill_publisher())
            .append("bill_confirmer", getBill_confirmer())
            .append("bill_returner", getBill_returner())
            .append("bill_refuse_comment", getBill_refuse_comment())
            .append("total_price_with_tax", getTotal_price_with_tax())
            .append("total_price_without_tax", getTotal_price_without_tax())
            .toString();
    }
} 