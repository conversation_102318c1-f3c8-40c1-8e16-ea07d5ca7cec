<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增核对人')" />
    <style>
        .container-div {
            padding-bottom: 0 !important;
            margin-bottom: 0 !important;
            height: auto !important;
        }
        .form-horizontal {
            margin-bottom: 0 !important;
        }
        .form-group {
            margin-bottom: 10px !important;
        }
        body {
            padding-bottom: 0 !important;
            overflow: hidden;
        }
        
        /* 校验状态样式 */
        .is-valid {
            border-color: #28a745 !important;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
        }
        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }
        .text-success {
            color: #28a745 !important;
        }
        .text-danger {
            color: #dc3545 !important;
        }
        #validation-message {
            display: block;
            margin-top: 5px;
        }
    </style>
</head>
<body class="white-bg">
    <div class="container-div">
        <form id="form-add-checker-manage" class="form-horizontal">
            <div class="form-group">
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="col-sm-8">
                    <input type="text" name="name" class="form-control" required />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">EHR号：</label>
                <div class="col-sm-8">
                    <input type="text" name="ehrNumber" class="form-control" required />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">部门名称：</label>
                <div class="col-sm-8">
                    <select name="departmentName" class="form-control" required>
                        <option value="">请选择部门</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input type="text" name="remarks" class="form-control" />
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        // expense_institude字典列表
        var expenseInstitudeList = [];
        $(function() {
            // 获取expense_institude字典
            console.log("开始获取字典数据...");
            $.ajax({
                url: ctx + "system/dict/data/list",
                type: "post",
                data: {
                    dictType: "expense_institude"
                },
                success: function(res) {
                    console.log("字典请求成功，响应数据：", res);
                    if(res.code === 0 && res.rows && res.rows.length > 0) {
                        expenseInstitudeList = res.rows.map(function(item){ return item.dictLabel; });
                        console.log("获取到的字典列表：", expenseInstitudeList);
                        
                        // 填充部门下拉框
                        var departmentSelect = $("select[name='departmentName']");
                        departmentSelect.empty();
                        departmentSelect.append('<option value="">请选择部门</option>');
                        
                        $.each(res.rows, function(index, item) {
                            departmentSelect.append('<option value="' + item.dictLabel + '">' + item.dictLabel + '</option>');
                        });
                        console.log("下拉框选项填充完成，共 " + res.rows.length + " 个选项");
                    } else {
                        console.warn("字典数据为空");
                        var departmentSelect = $("select[name='departmentName']");
                        departmentSelect.append('<option value="">暂无部门数据</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("获取字典数据失败：", error);
                    var departmentSelect = $("select[name='departmentName']");
                    departmentSelect.append('<option value="">数据加载失败</option>');
                }
            });
            
            // 添加实时校验功能
            var validateTimeout;
            function validateUserAsync() {
                var name = $("input[name='name']").val().trim();
                var ehrNumber = $("input[name='ehrNumber']").val().trim();
                
                // 清除之前的验证状态
                $("input[name='name']").removeClass("is-valid is-invalid");
                $("input[name='ehrNumber']").removeClass("is-valid is-invalid");
                $("#validation-message").remove();
                
                if (name && ehrNumber) {
                    console.log("执行实时校验...");
                    $.ajax({
                        url: ctx + "expense/checker_manage/validateUser",
                        type: "POST",
                        data: {
                            name: name,
                            ehrNumber: ehrNumber
                        },
                        success: function(res) {
                            if (res.code === 0) {
                                // 校验成功
                                $("input[name='name']").addClass("is-valid");
                                $("input[name='ehrNumber']").addClass("is-valid");
                                $("input[name='ehrNumber']").parent().append('<small id="validation-message" class="text-success">✓ 用户信息校验通过</small>');
                            } else {
                                // 校验失败
                                $("input[name='name']").addClass("is-invalid");
                                $("input[name='ehrNumber']").addClass("is-invalid");
                                $("input[name='ehrNumber']").parent().append('<small id="validation-message" class="text-danger">✗ ' + res.msg + '</small>');
                            }
                        },
                        error: function() {
                            console.error("实时校验请求失败");
                        }
                    });
                }
            }
            
            // 绑定输入事件
            $("input[name='name'], input[name='ehrNumber']").on('input blur', function() {
                clearTimeout(validateTimeout);
                validateTimeout = setTimeout(validateUserAsync, 500); // 防抖，500ms后执行
            });
        });
        // 为适配RuoYi框架，添加submitHandler函数
        function submitHandler() {
            return submitAddCheckerManage();
        }
        
        function submitAddCheckerManage() {
            // 前端校验
            var name = $("input[name='name']").val().trim();
            var ehrNumber = $("input[name='ehrNumber']").val().trim();
            var departmentName = $("select[name='departmentName']").val();
            if (!name) {
                $.modal.msgError("姓名不能为空");
                return false;
            }
            if (!ehrNumber) {
                $.modal.msgError("EHR号不能为空");
                return false;
            }
            if (!departmentName) {
                $.modal.msgError("请选择部门");
                return false;
            }
            
            // 校验用户信息是否存在且匹配
            console.log("开始校验用户信息...");
            $.ajax({
                url: ctx + "expense/checker_manage/validateUser",
                type: "POST",
                data: {
                    name: name,
                    ehrNumber: ehrNumber
                },
                async: false,
                success: function(res) {
                    console.log("用户校验结果：", res);
                    if (res.code !== 0) {
                        $.modal.msgError(res.msg);
                        return false;
                    }
                    
                    // 用户校验通过，继续提交表单
                    console.log("用户校验通过，开始提交表单");
                    var arr = $("#form-add-checker-manage").serializeArray();
                    var data = {};
                    $.each(arr, function() {
                        data[this.name] = this.value;
                    });
                    
                    $.ajax({
                        url: ctx + "expense/checker_manage/add",
                        type: "POST",
                        contentType: "application/json",
                        data: JSON.stringify(data),
                        success: function(res) {
                            if(res.code === 0) {
                                $.modal.msgSuccess("新增成功");
                                
                                // 先刷新父页面表格
                                console.log("开始刷新父页面表格...");
                                if (parent && parent.$.table && parent.$.table.refresh) {
                                    console.log("表格刷新成功");
                                    parent.$.table.refresh();
                                } else {
                                    console.log("无法刷新表格，parent.$.table.refresh不存在");
                                }
                                
                                // 延迟关闭弹出框
                                setTimeout(function() {
                                    console.log("开始关闭弹窗...");
                                    
                                    // 使用layer.closeAll()关闭所有弹窗
                                    if (parent && parent.layer) {
                                        try {
                                            parent.layer.closeAll();
                                            console.log("✅ 弹窗关闭成功");
                                        } catch (e) {
                                            console.error("❌ 弹窗关闭失败:", e);
                                            // 兜底方案：刷新父页面
                                            if (parent && parent.location) {
                                                parent.location.reload();
                                            }
                                        }
                                    } else {
                                        console.warn("⚠️ parent.layer不存在，使用刷新页面");
                                        if (parent && parent.location) {
                                            parent.location.reload();
                                        }
                                    }
                                }, 100);
                                
                                return true;
                            } else {
                                $.modal.msgError(res.msg || "新增失败");
                                return false;
                            }
                        },
                        error: function() {
                            $.modal.msgError("系统错误");
                            return false;
                        }
                    });
                },
                error: function(xhr, status, error) {
                    console.error("用户校验失败：", error);
                    $.modal.msgError("用户信息校验失败，请检查网络连接");
                    return false;
                }
            });
            
            return false; // 阻止默认提交
        }
    </script>
</body>
</html> 