<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('账单上传')" />
    <link th:href="@{/ajax/libs/dropzone/dropzone.min.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>账单文件上传</h5>
                    </div>
                    <div class="ibox-content">
                        <form id="uploadForm" enctype="multipart/form-data">
                            <div class="form-group">
                                <label class="control-label">发布人：</label>
                                <input type="text" id="publisher" name="publisher" class="form-control" placeholder="请输入发布人姓名" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="control-label">账单数量：</label>
                                <input type="number" id="billCount" name="billCount" class="form-control" placeholder="请输入账单数量" min="1" required>
                            </div>
                            

                            
                            <div class="form-group">
                                <label class="control-label">选择Excel文件：</label>
                                <div class="file-upload-wrapper">
                                    <input type="file" id="fileInput" name="file" accept=".xlsx" class="form-control-file" required>
                                    <small class="form-text text-muted">
                                        <i class="fa fa-info-circle"></i> 
                                        请选择.xlsx格式的Excel文件。支持多个Sheet，系统会自动读取所有Sheet中的数据。
                                    </small>
                                </div>
                            </div>
                            
                            <div class="form-group" id="fileInfo" style="display: none;">
                                <div class="alert alert-info">
                                    <h5><i class="fa fa-file-excel-o"></i> 文件信息</h5>
                                    <p id="fileDetails"></p>
                                </div>
                            </div>
                            
                            <!-- 功能按钮区域 -->
                            <div class="form-group text-center">
                                <button type="button" id="validateBtn" class="btn btn-info btn-lg" style="margin-right: 10px;">
                                    <i class="fa fa-check"></i> 验证文件格式
                                </button>
                                <button type="button" id="uploadBtn" class="btn btn-primary btn-lg" style="margin-right: 10px;">
                                    <i class="fa fa-upload"></i> 开始上传
                                </button>
                                <button type="button" id="resetBtn" class="btn btn-default btn-lg">
                                    <i class="fa fa-refresh"></i> 重置表单
                                </button>
                            </div>
                            
                            <div class="form-group">
                                <div class="alert alert-warning">
                                    <div class="format-header">
                                        <h5><i class="fa fa-exclamation-triangle"></i> Excel文件格式要求</h5>
                                        <div class="format-actions">
                                            <button type="button" id="toggleFormatBtn" class="btn btn-default btn-sm">
                                                <i class="fa fa-chevron-down"></i> 展开说明
                                            </button>
                                        </div>
                                    </div>
                                    <div class="format-description" id="formatDescription" style="display: none;">
                                        <h6><strong>账单相关数据（前两行）：</strong></h6>
                                        <p>第一行是账单相关数据的字段名，第二行是数据。列结构如下：</p>
                                        <table class="table table-bordered table-sm">
                                            <thead>
                                                <tr>
                                                    <th>A列</th>
                                                    <th>B列</th>
                                                    <th>C列</th>
                                                    <th>D列</th>
                                                    <th>E列</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>费用类型</td>
                                                    <td>计费周期</td>
                                                    <td>划账部门</td>
                                                    <td>含税总价</td>
                                                    <td>不含税总价</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        
                                        <h6><strong>费用明细数据（第三行到最后一行）：</strong></h6>
                                        <p>第三行为费用明细数据的字段名，从第四行到最后一行表示数据。列结构如下：</p>
                                        <table class="table table-bordered table-sm">
                                            <thead>
                                                <tr>
                                                    <th>A列</th>
                                                    <th>B列</th>
                                                    <th>C列</th>
                                                    <th>D列</th>
                                                    <th>E列</th>
                                                    <th>F列</th>
                                                    <th>G列</th>
                                                    <th>H列</th>
                                                    <th>I列</th>
                                                    <th>J列</th>
                                                    <th>K列</th>
                                                    <th>L列</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>费用名称</td>
                                                    <td>编号</td>
                                                    <td>品牌</td>
                                                    <td>具体规格</td>
                                                    <td>备注</td>
                                                    <td>费用变动情况</td>
                                                    <td>划账部门</td>
                                                    <td>数量</td>
                                                    <td>含税单价</td>
                                                    <td>不含税单价</td>
                                                    <td>含税单行总价</td>
                                                    <td>不含税单行总价</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        
                                        <h6><strong>重要字段填写要求：</strong></h6>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="panel panel-info">
                                                    <div class="panel-heading">
                                                        <h4 class="panel-title"><i class="fa fa-tags"></i> 费用类型</h4>
                                                    </div>
                                                    <div class="panel-body">
                                                        <p><strong>填写位置：</strong>账单相关数据第2行A列</p>
                                                        <p><strong>填写要求：</strong></p>
                                                        <ul>
                                                            <li>必须是系统中已配置的费用类型</li>
                                                            <li>不能为空或自定义值</li>
                                                            <li>请从以下可用值中选择</li>
                                                        </ul>
                                                        <div class="alert alert-info alert-sm">
                                                            <i class="fa fa-list"></i> <strong>可用的费用类型：</strong>
                                                            <div id="expenseTypeList" class="dict-value-list">
                                                                <span class="text-muted">加载中...</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="panel panel-warning">
                                                    <div class="panel-heading">
                                                        <h4 class="panel-title"><i class="fa fa-calendar"></i> 计费周期</h4>
                                                    </div>
                                                    <div class="panel-body">
                                                        <p><strong>填写位置：</strong>账单相关数据第2行B列</p>
                                                        <p><strong>格式要求：</strong></p>
                                                        <ul>
                                                            <li><strong>单月格式：</strong><code>202505</code>（6位数字）</li>
                                                            <li><strong>跨月格式：</strong><code>202501-06</code>（起始月-结束月）</li>
                                                        </ul>
                                                        <div class="alert alert-warning alert-sm">
                                                            <i class="fa fa-exclamation-triangle"></i> 年份范围：2000-2099，月份：01-12
                                                        </div>
                                                        <p><strong>示例：</strong></p>
                                                        <ul>
                                                            <li>✓ <code>202505</code> - 2025年5月</li>
                                                            <li>✓ <code>202501-03</code> - 2025年1月到3月</li>
                                                            <li>✗ <code>20250</code> - 格式错误</li>
                                                            <li>✗ <code>202513</code> - 月份超出范围</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="panel panel-success">
                                                    <div class="panel-heading">
                                                        <h4 class="panel-title"><i class="fa fa-building"></i> 划账部门</h4>
                                                    </div>
                                                    <div class="panel-body">
                                                        <p><strong>填写位置：</strong>账单相关数据第2行C列</p>
                                                        <p><strong>填写要求：</strong></p>
                                                        <ul>
                                                            <li>必须是系统中已配置的部门名称</li>
                                                            <li>账单和明细中的部门必须一致</li>
                                                            <li>请从以下可用值中选择</li>
                                                        </ul>
                                                        <div class="alert alert-success alert-sm">
                                                            <i class="fa fa-building"></i> <strong>可用的划账部门：</strong>
                                                            <div id="expenseInstitudeList" class="dict-value-list">
                                                                <span class="text-muted">加载中...</span>
                                                            </div>
                                                        </div>
                                                        <p><strong>注意：</strong>如果明细中G列有部门信息，必须与此处保持一致</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        

                                        
                                        <h6><strong>其他格式要求：</strong></h6>
                                        <ul>
                                            <li>文件格式必须为.xlsx</li>
                                            <li>文件大小不超过50MB</li>
                                            <li>空行会被自动跳过</li>
                                            <li>第二行的所有字段数据不能为空</li>
                                            <li>费用明细中的以下字段不能为空：费用名称、品牌、数量、含税单价、不含税单价、含税单行总价、不含税单行总价</li>
                                            <li>数量必须大于0</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            

                            

                        </form>
                        
                        <!-- 验证结果 -->
                        <div id="validateResult" style="display: none;">
                            <div class="alert" id="validateAlert">
                                <h5 id="validateTitle"></h5>
                                <p id="validateMessage"></p>
                            </div>
                        </div>
                        
                        <!-- 上传进度 -->
                        <div id="uploadProgress" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped active" role="progressbar" style="width: 0%">
                                    <span class="sr-only">0% 完成</span>
                                </div>
                            </div>
                            <p class="text-center" id="progressText">正在处理文件...</p>
                        </div>
                        
                        <!-- 上传结果 -->
                        <div id="uploadResult" style="display: none;">
                            <div class="alert" id="resultAlert">
                                <button type="button" class="close" id="closeResultBtn" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                <h5 id="resultTitle"></h5>
                                <p id="resultMessage"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "expense/upload";
        
        $(function() {
            initUploadForm();
            loadDictionaries();
        });
        
        function initUploadForm() {
            // 初始化时禁用上传按钮
            $('#uploadBtn').prop('disabled', true).removeClass('btn-primary').addClass('btn-secondary')
                .html('<i class="fa fa-upload"></i> 开始上传');
            
            // 初始化时禁用验证按钮
            $('#validateBtn').prop('disabled', true).removeClass('btn-info').addClass('btn-secondary');
            
            // 文件选择事件
            $('#fileInput').on('change', function() {
                var file = this.files[0];
                if (file) {
                    displayFileInfo(file);
                    if (validateFile(file)) {
                        // 启用验证按钮
                        $('#validateBtn').prop('disabled', false).removeClass('btn-secondary').addClass('btn-info');
                    }
                } else {
                    hideFileInfo();
                    // 禁用验证按钮
                    $('#validateBtn').prop('disabled', true).removeClass('btn-info').addClass('btn-secondary');
                }
                // 清除之前的验证结果和成功提示
                $('#validateResult').hide();
                $('#successTip').remove();
                // 重置上传按钮状态
                $('#uploadBtn').prop('disabled', true).removeClass('btn-primary').addClass('btn-secondary')
                    .html('<i class="fa fa-upload"></i> 开始上传');
            });
            
            // 验证按钮事件
            $('#validateBtn').on('click', function() {
                validateExcelContent();
            });
            
            // 上传按钮事件
            $('#uploadBtn').on('click', function() {
                uploadBill();
            });
            
            // 重置按钮事件
            $('#resetBtn').on('click', function() {
                resetForm();
            });
            
            // 关闭上传结果按钮事件
            $('#closeResultBtn').on('click', function() {
                $('#uploadResult').fadeOut(300);
            });
            
            // 折叠/展开说明功能
            $('#toggleFormatBtn').on('click', function() {
                toggleFormatDescription();
            });
        }
        
        function displayFileInfo(file) {
            var fileSize = (file.size / 1024 / 1024).toFixed(2);
            var fileDetails = '文件名：' + file.name + '<br>' +
                            '文件大小：' + fileSize + ' MB<br>' +
                            '文件类型：' + file.type;
            
            $('#fileDetails').html(fileDetails);
            $('#fileInfo').show();
        }
        
        function hideFileInfo() {
            $('#fileInfo').hide();
        }
        
        // 加载字典数据
        function loadDictionaries() {
            // 加载费用类型字典
            loadDictionary('expense_type', 'expenseTypeList');
            
            // 加载划账部门字典
            loadDictionary('expense_institude', 'expenseInstitudeList');
        }
        
        // 加载指定字典
        function loadDictionary(dictType, targetElementId) {
            $.ajax({
                url: ctx + "expense/upload/getDictData",
                type: 'POST',
                data: {
                    dictType: dictType
                },
                success: function(result) {
                    console.log("字典请求成功，响应数据：", result);
                    if (result.code === 0 && result.data && result.data.length > 0) {
                        var html = '';
                        for (var i = 0; i < result.data.length; i++) {
                            var item = result.data[i];
                            html += '<span class="dict-value-tag">' + item.dictValue + '</span>';
                        }
                        $('#' + targetElementId).html(html);
                    } else {
                        $('#' + targetElementId).html('<span class="text-muted">暂无数据</span>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("字典加载失败：", error);
                    $('#' + targetElementId).html('<span class="text-danger">加载失败</span>');
                }
            });
        }
        
        // 切换格式说明的显示/隐藏
        function toggleFormatDescription() {
            var $description = $('#formatDescription');
            var $toggleBtn = $('#toggleFormatBtn');
            
            if ($description.is(':visible')) {
                $description.slideUp(300);
                $toggleBtn.html('<i class="fa fa-chevron-down"></i> 展开说明');
            } else {
                $description.slideDown(300);
                $toggleBtn.html('<i class="fa fa-chevron-up"></i> 折叠说明');
            }
        }
        
        function validateFile(file) {
            if (!file.name.toLowerCase().endsWith('.xlsx')) {
                $.modal.alertWarning('请选择.xlsx格式的Excel文件！');
                $('#fileInput').val('');
                hideFileInfo();
                return false;
            }
            
            if (file.size > 50 * 1024 * 1024) { // 50MB限制
                $.modal.alertWarning('文件大小不能超过50MB！');
                $('#fileInput').val('');
                hideFileInfo();
                return false;
            }
            
            return true;
        }
        
        function validateExcelContent() {
            var file = $('#fileInput')[0].files[0];
            if (!file) {
                $.modal.alertWarning('请先选择要验证的Excel文件！');
                return;
            }
            
            if (!validateFile(file)) {
                return;
            }
            
            var billCount = $('#billCount').val();
            var formData = new FormData();
            formData.append('file', file);
            if (billCount && billCount > 0) {
                formData.append('billCount', billCount);
            }
            
            // 显示验证进度
            $('#validateBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 验证中...');
            
            $.ajax({
                url: prefix + '/validate',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(result) {
                    $('#validateBtn').prop('disabled', false).html('<i class="fa fa-check"></i> 验证文件格式');
                    
                    if (result.code == 0) {
                        showValidateResult(true, '格式验证通过', result.msg || result.data);
                    } else {
                        showValidateResult(false, '格式验证失败', result.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $('#validateBtn').prop('disabled', false).html('<i class="fa fa-check"></i> 验证文件格式');
                    showValidateResult(false, '验证异常', '网络异常或服务器错误：' + error);
                }
            });
        }
        
        function showValidateResult(success, title, message) {
            var alertClass = success ? 'alert-success fade-in-success' : 'alert-danger';
            var icon = success ? 'fa-check-circle' : 'fa-times-circle';
            
            $('#validateAlert').removeClass().addClass('alert ' + alertClass);
            $('#validateTitle').html('<i class="fa ' + icon + '"></i> ' + title);
            $('#validateMessage').html(message);
            $('#validateResult').show();
            
            // 成功时添加动画效果和更长的显示时间
            if (success) {
                $('#validateResult').hide().fadeIn(500);
                
                // 成功时启用上传按钮，添加视觉反馈
                $('#uploadBtn').prop('disabled', false).removeClass('btn-secondary').addClass('btn-primary')
                    .html('<i class="fa fa-upload"></i> 开始上传 <span class="badge badge-light">已就绪</span>');
                
                // 显示额外的成功提示
                if ($('#successTip').length === 0) {
                    $('#validateResult').after('<div id="successTip" class="alert alert-info mt-3" style="display: none;">' +
                        '<i class="fa fa-lightbulb-o"></i> ' +
                        '<strong>提示：</strong>文件格式验证通过，您现在可以点击"开始上传"按钮进行账单上传。' +
                        '</div>');
                    $('#successTip').fadeIn(300);
                }
                
                // 10秒后自动隐藏成功消息和提示
                setTimeout(function() {
                    $('#validateResult').fadeOut(800);
                    $('#successTip').fadeOut(800, function() {
                        $(this).remove();
                    });
                }, 10000);
            } else {
                // 失败时禁用上传按钮，重置按钮文本
                $('#uploadBtn').prop('disabled', true).removeClass('btn-primary').addClass('btn-secondary')
                    .html('<i class="fa fa-upload"></i> 开始上传');
                
                // 移除成功提示
                $('#successTip').remove();
                
                // 失败消息不自动隐藏，需要用户手动处理
            }
        }
        
        function uploadBill() {
            // 表单验证
            if (!validateForm()) {
                return;
            }
            
            var formData = new FormData();
            formData.append('file', $('#fileInput')[0].files[0]);
            formData.append('publisher', $('#publisher').val().trim());
            formData.append('billCount', $('#billCount').val());
            
            // 显示进度条
            showUploadProgress();
            
            $.ajax({
                url: prefix + '/bill',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function(evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = evt.loaded / evt.total * 100;
                            updateProgress(percentComplete);
                        }
                    }, false);
                    return xhr;
                },
                success: function(result) {
                    hideUploadProgress();
                    
                    // 根据消息内容判断真正的成功或失败
                    var message = result.msg || result.data || '';
                    var isRealSuccess = result.code == 0 && !message.includes('数据发送失败');
                    
                    if (isRealSuccess) {
                        showUploadResult(true, '账单数据上传成功', message);
                    } else {
                        // HTTP请求成功但业务逻辑失败，或者真正的失败
                        showUploadResult(false, '账单数据上传失败', message);
                    }
                },
                error: function(xhr, status, error) {
                    hideUploadProgress();
                    showUploadResult(false, '上传异常', '网络异常或服务器错误：' + error);
                }
            });
        }
        
        function validateForm() {
            var publisher = $('#publisher').val().trim();
            var billCount = $('#billCount').val();
            var file = $('#fileInput')[0].files[0];
            
            if (!publisher) {
                $.modal.alertWarning('请填写发布人！');
                $('#publisher').focus();
                return false;
            }
            
            if (!billCount || billCount <= 0) {
                $.modal.alertWarning('请填写正确的账单数量！');
                $('#billCount').focus();
                return false;
            }
            
            if (!file) {
                $.modal.alertWarning('请选择要上传的Excel文件！');
                $('#fileInput').focus();
                return false;
            }
            
            return validateFile(file);
        }
        
        function showUploadProgress() {
            $('#uploadProgress').show();
            $('#uploadBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 上传中...');
            updateProgress(0);
        }
        
        function hideUploadProgress() {
            $('#uploadProgress').hide();
            $('#uploadBtn').prop('disabled', false).html('<i class="fa fa-upload"></i> 开始上传');
        }
        
        function updateProgress(percent) {
            var progressBar = $('#uploadProgress .progress-bar');
            progressBar.css('width', percent + '%');
            
            if (percent < 100) {
                $('#progressText').text('正在上传文件... ' + Math.round(percent) + '%');
            } else {
                $('#progressText').text('正在处理文件数据...');
            }
        }
        
        function showUploadResult(success, title, message) {
            var alertClass = success ? 'alert-success' : 'alert-danger';
            var icon = success ? 'fa-check-circle' : 'fa-times-circle';
            
            $('#resultAlert').removeClass().addClass('alert ' + alertClass);
            $('#resultTitle').html('<i class="fa ' + icon + '"></i> ' + title);
            
            // 解析和格式化消息内容
            var formattedMessage = formatUploadMessage(message, success);
            $('#resultMessage').html(formattedMessage);
            $('#uploadResult').show();
            
            // 成功时添加动画效果
            if (success) {
                $('#uploadResult').hide().fadeIn(500);
                
                // 清理验证相关的状态
                $('#validateResult').hide();
                $('#successTip').remove();
                
                // 成功消息不自动隐藏，由表单重置或用户手动关闭来隐藏
                // setTimeout(function() {
                //     $('#uploadResult').fadeOut(800);
                // }, 12000);
            } else {
                // 失败时不自动隐藏，让用户手动关闭
                $('#uploadResult').hide().fadeIn(500);
            }
        }
        
        /**
         * 格式化上传消息，使其更加用户友好
         */
        function formatUploadMessage(message, isSuccess) {
            if (!message) return '';
            
            if (isSuccess) {
                // 成功情况的处理
                return formatSuccessMessage(message);
            } else {
                // 失败情况的处理
                return formatFailureMessage(message);
            }
        }
        
        /**
         * 格式化成功消息
         */
        function formatSuccessMessage(message) {
            var html = '<div class="upload-result-detail">';
            
            // 解析消息，提取处理信息
            var uploadInfo = parseUploadInfo(message);
            
            // 总体结果状态
            html += '<div class="result-section result-summary">';
            html += '<h5><i class="fa fa-check-circle text-success"></i> 账单数据上传成功</h5>';
            html += '<p class="text-muted">文件上传和数据入库均已成功完成</p>';
            html += '</div>';
            
            // 文件上传结果部分
            html += '<div class="result-section">';
            html += '<h6><i class="fa fa-upload text-success"></i> 文件上传结果</h6>';
            html += '<ul class="list-unstyled">';
            html += '<li><i class="fa fa-check text-success"></i> 文件上传成功</li>';
            if (uploadInfo.sheetCount) {
                html += '<li><i class="fa fa-file-excel-o text-primary"></i> 解析了 ' + uploadInfo.sheetCount + ' 个Sheet</li>';
            }
            html += '</ul>';
            html += '</div>';
            
            // 数据入库结果部分
            html += '<div class="result-section">';
            html += '<h6><i class="fa fa-database text-success"></i> 数据入库结果</h6>';
            html += '<ul class="list-unstyled">';
            html += '<li><i class="fa fa-check text-success"></i> 数据入库成功</li>';
            if (uploadInfo.billCount) {
                html += '<li><i class="fa fa-list-alt text-primary"></i> 处理了 ' + uploadInfo.billCount + ' 份账单</li>';
            }
            if (uploadInfo.departments && uploadInfo.departments.length > 0) {
                html += '<li><i class="fa fa-building text-primary"></i> 涉及机构：<span class="text-info">' + uploadInfo.departments.join('、') + '</span></li>';
            }
            html += '</ul>';
            html += '</div>';
            
            // 操作指引
            html += '<div class="result-section">';
            html += '<small class="text-muted"><i class="fa fa-info-circle"></i> 您可以前往账单管理页面查看详细信息</small>';
            html += '</div>';
            
            html += '</div>';
            return html;
        }
        
        /**
         * 格式化失败消息
         */
        function formatFailureMessage(message) {
            var html = '<div class="upload-result-detail">';
            
            // 解析消息，确定失败阶段
            var failureInfo = parseFailureInfo(message);
            
            // 总体结果状态
            html += '<div class="result-section result-summary result-danger">';
            html += '<h5><i class="fa fa-times-circle text-danger"></i> 账单数据上传失败</h5>';
            if (failureInfo.uploadSuccess) {
                html += '<p class="text-muted">文件上传成功，但数据入库过程中发生错误</p>';
            } else {
                html += '<p class="text-muted">文件上传阶段发生错误</p>';
            }
            html += '</div>';
            
            // 文件上传结果部分
            html += '<div class="result-section">';
            html += '<h6><i class="fa fa-upload ' + (failureInfo.uploadSuccess ? 'text-success' : 'text-danger') + '"></i> 文件上传结果</h6>';
            html += '<ul class="list-unstyled">';
            if (failureInfo.uploadSuccess) {
                html += '<li><i class="fa fa-check text-success"></i> 文件上传成功</li>';
                if (failureInfo.sheetCount) {
                    html += '<li><i class="fa fa-file-excel-o text-primary"></i> 解析了 ' + failureInfo.sheetCount + ' 个Sheet</li>';
                }
            } else {
                html += '<li><i class="fa fa-times text-danger"></i> 文件上传失败</li>';
            }
            html += '</ul>';
            html += '</div>';
            
            // 数据入库结果部分
            html += '<div class="result-section">';
            html += '<h6><i class="fa fa-database text-danger"></i> 数据入库结果</h6>';
            html += '<ul class="list-unstyled">';
            html += '<li><i class="fa fa-times text-danger"></i> 数据入库失败</li>';
            html += '</ul>';
            
            // 错误详情
            html += '<div class="alert alert-danger" style="margin: 10px 0; padding: 10px; font-size: 14px;">';
            html += '<strong>失败原因：</strong><br>' + failureInfo.errorMessage;
            html += '</div>';
            html += '</div>';
            
            // 解决建议
            html += '<div class="result-section">';
            if (failureInfo.isDuplicateBill) {
                html += '<small class="text-warning"><i class="fa fa-exclamation-triangle"></i> 重复账单提示：</small>';
                html += '<ul class="small text-warning mt-1">';
                html += '<li>此账单已存在于系统中且状态不允许覆盖</li>';
                html += '<li>请确认账单信息是否正确</li>';
                html += '<li>如需重新发布，请先联系管理员退回原账单</li>';
                html += '<li>或者联系对应部门核对人进行退回操作</li>';
                html += '</ul>';
            } else {
                html += '<small class="text-muted"><i class="fa fa-lightbulb-o"></i> 建议：</small>';
                html += '<ul class="small text-muted mt-1">';
                html += '<li>检查Excel文件格式是否正确</li>';
                html += '<li>确认账单数量与文件Sheet数量一致</li>';
                html += '<li>验证必填字段是否完整</li>';
                html += '<li>如问题持续，请联系系统管理员</li>';
                html += '</ul>';
            }
            html += '</div>';
            
            html += '</div>';
            return html;
        }
        
        /**
         * 解析上传信息
         */
        function parseUploadInfo(message) {
            var info = {
                sheetCount: null,
                billCount: null,
                departments: []
            };
            
            // 提取Sheet数量
            var sheetMatch = message.match(/共处理(\d+)个Sheet/);
            if (sheetMatch) {
                info.sheetCount = sheetMatch[1];
            }
            
            // 提取账单数量和机构信息
            if (message.includes('数据入库成功')) {
                var billMatch = message.match(/共(\d+)份账单/);
                if (billMatch) {
                    info.billCount = billMatch[1];
                }
                
                var deptMatch = message.match(/包含以下机构：(.+)/);
                if (deptMatch) {
                    info.departments = deptMatch[1].split('、');
                }
            }
            
            return info;
        }
        
        /**
         * 解析失败信息
         */
        function parseFailureInfo(message) {
            var info = {
                uploadSuccess: false,
                sheetCount: null,
                errorMessage: message,
                isDuplicateBill: false
            };
            
            // 如果消息包含"文件处理成功"，说明上传阶段成功了
            if (message.includes('文件处理成功')) {
                info.uploadSuccess = true;
                
                // 提取Sheet数量
                var sheetMatch = message.match(/共处理(\d+)个Sheet/);
                if (sheetMatch) {
                    info.sheetCount = sheetMatch[1];
                }
                
                // 提取真正的错误信息（数据发送失败后的部分）
                var failureMatch = message.match(/数据发送失败：(.+)/);
                if (failureMatch) {
                    info.errorMessage = failureMatch[1];
                }
            }
            
            // 检查是否为重复账单错误
            if (info.errorMessage.includes('已在系统中存在') && 
                info.errorMessage.includes('账单不为已退回状态')) {
                info.isDuplicateBill = true;
            }
            
            return info;
        }
        
        function resetForm() {
            $('#uploadForm')[0].reset();
            hideFileInfo();
            $('#uploadResult').hide();
            $('#uploadProgress').hide();
            $('#validateResult').hide();
            $('#successTip').remove();
            // 重置上传按钮状态
            $('#uploadBtn').prop('disabled', true).removeClass('btn-primary').addClass('btn-secondary')
                .html('<i class="fa fa-upload"></i> 开始上传');
            // 重置验证按钮状态
            $('#validateBtn').prop('disabled', true).removeClass('btn-info').addClass('btn-secondary');
        }
    </script>
    
    <style>
        .file-upload-wrapper {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            transition: border-color 0.3s ease;
        }
        
        .file-upload-wrapper:hover {
            border-color: #1ab394;
        }
        
        .form-control-file {
            margin-bottom: 10px;
        }
        
        .progress {
            height: 20px;
            margin-bottom: 10px;
        }
        
        #fileInfo {
            margin-top: 15px;
        }
        
        .ibox-content {
            padding: 30px;
        }
        
        .btn-lg {
            margin: 0 5px;
        }
        
        .format-description {
            margin-top: 15px;
        }
        
        .format-description h6 {
            color: #333;
            margin-top: 15px;
            margin-bottom: 10px;
        }
        
        .format-description .table {
            font-size: 12px;
            margin-bottom: 15px;
        }
        
        .format-description .table th,
        .format-description .table td {
            padding: 5px 8px;
            text-align: center;
        }
        
        .format-description ul {
            margin-bottom: 0;
        }
        
        .format-description ul li {
            margin-bottom: 5px;
        }
        
        /* 验证成功状态增强样式 */
        .alert-success {
            border-left: 4px solid #5cb85c;
            box-shadow: 0 2px 5px rgba(92, 184, 92, 0.3);
        }
        
        .alert-info {
            border-left: 4px solid #5bc0de;
        }
        
        /* 按钮状态样式 */
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            opacity: 0.7;
        }
        
        .btn-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            opacity: 0.8;
            cursor: not-allowed;
        }
        
        /* 动画效果 */
        .fade-in-success {
            animation: fadeInSuccess 0.5s ease-in;
        }
        
        @keyframes fadeInSuccess {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 成功提示样式增强 */
        #successTip {
            border-left: 4px solid #5bc0de;
            background-color: #f0f9ff;
        }
        
        #successTip i {
            color: #5bc0de;
            margin-right: 5px;
        }
        
        /* 上传结果详细显示样式 */
        .upload-result-detail {
            text-align: left;
        }
        
        .upload-result-detail .result-section {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #e7e7e7;
            background-color: #f9f9f9;
        }
        
        /* 总体结果状态区域特殊样式 */
        .upload-result-detail .result-summary {
            border-left: 4px solid #1ab394;
            background-color: #f0f9ff;
            text-align: center;
            padding: 20px 15px;
        }
        
        .upload-result-detail .result-summary h5 {
            margin-bottom: 8px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .upload-result-detail .result-summary h5 i {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .upload-result-detail .result-summary p {
            margin-bottom: 0;
            font-size: 14px;
        }
        
        /* 失败状态的总体结果样式 */
        .upload-result-detail .result-summary h5 i.text-danger {
            color: #ed5565;
        }
        
        .upload-result-detail .result-summary.result-danger {
            border-left-color: #ed5565;
            background-color: #fff5f5;
        }
        
        .upload-result-detail .result-section h6 {
            margin-bottom: 10px;
            font-size: 15px;
            font-weight: bold;
        }
        
        .upload-result-detail .result-section:first-child {
            border-left-color: #1ab394;
        }
        
        .upload-result-detail .result-section:nth-child(2) {
            border-left-color: #23c6c8;
        }
        
        .upload-result-detail .result-section ul {
            margin-bottom: 0;
            margin-left: 10px;
        }
        
        .upload-result-detail .result-section li {
            margin-bottom: 6px;
            padding: 2px 0;
        }
        
        .upload-result-detail .result-section li i {
            margin-right: 8px;
            width: 16px;
        }
        
        /* 成功状态的样式 */
        .upload-result-detail .result-section h6 i.text-success {
            color: #1ab394;
        }
        
        /* 失败状态的样式 */
        .upload-result-detail .result-section h6 i.text-danger {
            color: #ed5565;
        }
        
        /* 错误详情样式增强 */
        .upload-result-detail .alert-danger {
            border-left: 4px solid #ed5565;
        }
        
        /* 格式说明头部样式 */
        .format-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .format-header h5 {
            margin: 0;
            flex: 1;
        }
        
        .format-actions {
            display: flex;
            gap: 10px;
        }
        
        .format-actions .btn {
            white-space: nowrap;
        }
        
        /* 字典值标签样式 */
        .dict-value-list {
            margin-top: 8px;
            line-height: 1.8;
        }
        
        .dict-value-tag {
            display: inline-block;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 2px 8px;
            margin: 2px 4px 2px 0;
            font-size: 12px;
            color: #495057;
        }
        
        .dict-value-tag:hover {
            background-color: #e9ecef;
            border-color: #ced4da;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .format-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .format-actions {
                margin-top: 10px;
                width: 100%;
            }
            
            .format-actions .btn {
                flex: 1;
            }
        }
    </style>
</body>
</html>