package com.ruoyi.expense.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.expense.domain.BillUploadDTO;
import com.ruoyi.expense.service.IBillUploadService;
import com.ruoyi.system.service.ISysDictDataService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 账单上传控制器
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Controller
@RequestMapping("/expense/upload")
public class BillUploadController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(BillUploadController.class);
    
    private String prefix = "expense/bill_upload";
    
    @Autowired
    private IBillUploadService billUploadService;
    
    @Autowired
    private ISysDictDataService dictDataService;
    
    /**
     * 账单上传页面
     */
    @RequiresPermissions("expense:upload:view")
    @GetMapping()
    public String upload() {
        return prefix + "/upload";
    }
    
    /**
     * 获取字典数据 - 专门为账单上传页面提供
     */
    @RequiresPermissions("expense:upload:view")
    @PostMapping("/getDictData")
    @ResponseBody
    public AjaxResult getDictData(@RequestParam("dictType") String dictType) {
        log.info("获取字典数据请求，字典类型: {}", dictType);
        
        try {
            // 创建查询条件
            SysDictData dictData = new SysDictData();
            dictData.setDictType(dictType);
            dictData.setStatus("0"); // 只查询正常状态的字典数据
            
            // 查询字典数据
            List<SysDictData> list = dictDataService.selectDictDataList(dictData);
            
            log.info("查询到字典数据 [{}] 共 {} 条", dictType, list.size());
            
            // 返回符合前端期望的数据格式
            return AjaxResult.success("查询成功", list);
            
        } catch (Exception e) {
            String errorMsg = "获取字典数据异常：" + e.getMessage();
            log.error(errorMsg, e);
            return AjaxResult.error(errorMsg);
        }
    }
    
    /**
     * 账单文件上传
     */
    @RequiresPermissions("expense:upload:bill")
    @PostMapping("/bill")
    @ResponseBody
    public AjaxResult uploadBill(
            @RequestParam("file") MultipartFile file,
            @RequestParam("publisher") String publisher,
            @RequestParam("billCount") Integer billCount) {
        
        log.info("=================== 账单上传请求 ===================");
        log.info("文件名: {}", file != null ? file.getOriginalFilename() : "null");
        log.info("文件大小: {} bytes", file != null ? file.getSize() : 0);
        log.info("发布人: {}", publisher);
        log.info("账单数量: {}", billCount);
        
        try {
            // 基本参数验证
            if (file == null || file.isEmpty()) {
                return AjaxResult.error("请选择要上传的文件");
            }
            
            if (publisher == null || publisher.trim().isEmpty()) {
                return AjaxResult.error("请填写发布人");
            }
            
            if (billCount == null || billCount <= 0) {
                return AjaxResult.error("请填写正确的账单数量");
            }
            
            // 构造上传DTO
            BillUploadDTO uploadDTO = new BillUploadDTO();
            uploadDTO.setFile(file);
            uploadDTO.setPublisher(publisher.trim());
            uploadDTO.setBillCount(billCount);
            
            // 处理上传
            String result = billUploadService.processBillUpload(uploadDTO);
            
            if (result.contains("成功")) {
                log.info("账单上传处理成功: {}", result);
                return AjaxResult.success(result);
            } else {
                log.warn("账单上传处理失败: {}", result);
                return AjaxResult.error(result);
            }
            
        } catch (Exception e) {
            String errorMsg = "账单上传处理异常：" + e.getMessage();
            log.error(errorMsg, e);
            return AjaxResult.error(errorMsg);
        } finally {
            log.info("===============================================");
        }
    }
    
    /**
     * 验证文件格式和内容
     */
    @RequiresPermissions("expense:upload:validate")
    @PostMapping("/validate")
    @ResponseBody
    public AjaxResult validateFile(@RequestParam("file") MultipartFile file,
                                   @RequestParam(value = "billCount", required = false) Integer billCount) {
        log.info("=================== 文件验证请求 ===================");
        log.info("文件名: {}", file != null ? file.getOriginalFilename() : "null");
        log.info("文件大小: {} bytes", file != null ? file.getSize() : 0);
        log.info("预期账单数量: {}", billCount);
        
        try {
            if (file == null || file.isEmpty()) {
                return AjaxResult.error("请选择要验证的文件");
            }
            
            // 验证文件格式
            boolean isValidFormat = billUploadService.validateExcelFile(file);
            if (!isValidFormat) {
                return AjaxResult.error("文件格式不正确，请上传.xlsx格式的Excel文件");
            }
            
            // 验证文件内容
            String contentValidation = billUploadService.validateExcelContent(file);
            if (!contentValidation.equals("验证通过")) {
                log.warn("文件内容验证失败: {}", contentValidation);
                return AjaxResult.error(contentValidation);
            }
            
            // 如果提供了账单数量，则进行数量匹配验证
            if (billCount != null) {
                String countValidation = billUploadService.validateBillCount(file, billCount);
                if (!countValidation.equals("验证通过")) {
                    log.warn("账单数量验证失败: {}", countValidation);
                    return AjaxResult.error(countValidation);
                }
            }
            
            log.info("文件验证通过: {}", file.getOriginalFilename());
            return AjaxResult.success("文件格式和内容验证通过，可以进行上传");
            
        } catch (Exception e) {
            String errorMsg = "文件验证异常：" + e.getMessage();
            log.error(errorMsg, e);
            return AjaxResult.error(errorMsg);
        } finally {
            log.info("===============================================");
        }
    }
}