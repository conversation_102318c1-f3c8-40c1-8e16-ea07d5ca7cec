package com.ruoyi.expense.mapper;

import java.util.List;
import com.ruoyi.expense.domain.ExpenseAllocationTable;
import org.apache.ibatis.annotations.Param;

/**
 * 分摊表管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ExpenseAllocationTableMapper 
{
    /**
     * 查询分摊表管理
     * 
     * @param id 分摊表管理主键
     * @return 分摊表管理
     */
    public ExpenseAllocationTable selectExpenseAllocationTableById(Long id);

    /**
     * 查询分摊表管理列表
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 分摊表管理集合
     */
    public List<ExpenseAllocationTable> selectExpenseAllocationTableList(ExpenseAllocationTable expenseAllocationTable);

    /**
     * 新增分摊表管理
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 结果
     */
    public int insertExpenseAllocationTable(ExpenseAllocationTable expenseAllocationTable);

    /**
     * 修改分摊表管理
     * 
     * @param expenseAllocationTable 分摊表管理
     * @return 结果
     */
    public int updateExpenseAllocationTable(ExpenseAllocationTable expenseAllocationTable);

    /**
     * 删除分摊表管理
     * 
     * @param id 分摊表管理主键
     * @return 结果
     */
    public int deleteExpenseAllocationTableById(Long id);

    /**
     * 批量删除分摊表管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExpenseAllocationTableByIds(Long[] ids);

    /**
     * 根据费用类型和计费周期查询分摊表
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @return 分摊表管理
     */
    public ExpenseAllocationTable selectByExpenseTypeAndBillingCycle(@Param("expenseType") String expenseType, 
                                                                     @Param("billingCycle") String billingCycle);

    /**
     * 检查是否存在相同费用类型和计费周期的分摊表
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @return 数量
     */
    public int checkExistByExpenseTypeAndBillingCycle(@Param("expenseType") String expenseType, 
                                                      @Param("billingCycle") String billingCycle);
} 