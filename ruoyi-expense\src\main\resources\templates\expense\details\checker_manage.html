<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('各部门账单核对人管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <button class="btn btn-success" onclick="addVerifier()">
                    <i class="fa fa-plus"></i> 新增用户
                </button>
            </div>
            <div class="col-sm-12 select-table table-striped" style="margin-top: 10px;">
                <table id="verifier-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "expense/departmentVerifierInfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                removeUrl: prefix + "/remove",
                modalName: "核对人",
                columns: [
                    { field: 'name', title: '姓名' },
                    { field: 'ehrNo', title: 'EHR号' },
                    { field: 'departmentName', title: '部门名称' },
                    { field: 'remarks', title: '备注' },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="removeVerifier(\'' + row.id + '\')"><i class="fa fa-remove"></i> 删除</a>';
                        }
                    }
                ]
            };
            $.table.init(options);
        });

        function addVerifier() {
            // 可弹窗或跳转到新增页面
            $.modal.open("新增核对人", prefix + "/add");
        }

        function removeVerifier(id) {
            $.operate.remove(id);
        }
    </script>
</body>
</html>