package com.ruoyi.expense.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.expense.domain.ExpenseCheckerManage;
import com.ruoyi.expense.mapper.ExpenseCheckerManageMapper;
import com.ruoyi.expense.service.IExpenseCheckerManageService;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExpenseCheckerManageServiceImpl implements IExpenseCheckerManageService {

    @Autowired
    private ExpenseCheckerManageMapper checkerManageMapper;

    @Autowired
    private DictService dictService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ISysRoleService sysRoleService;

    @Override
    public List<ExpenseCheckerManage> selectCheckerManageList(ExpenseCheckerManage checkerManage) {
        return checkerManageMapper.selectCheckerManageList(checkerManage);
    }

    @Override
    public ExpenseCheckerManage selectCheckerManageById(Long id) {
        return checkerManageMapper.selectCheckerManageById(id.intValue());
    }

    @Override
    public int insertCheckerManage(ExpenseCheckerManage checkerManage) {
        // 先校验用户信息是否匹配
        int validateResult = validateUserInfo(checkerManage.getName(), checkerManage.getEhrNumber());
        if (validateResult != 0) {
            return validateResult;
        }
        
        // 只要姓名或EHR号有一个重复就返回-1
        if (checkerManageMapper.selectByName(checkerManage.getName()) != null
                || checkerManageMapper.selectByEhrNumber(checkerManage.getEhrNumber()) != null) {
            return -1;
        }
        // 校验部门名称是否在expense_institude字典中
        String deptLabel = dictService.getLabel("expense_institude", checkerManage.getDepartmentName());
        if (deptLabel == null) {
            return -2;
        }
        
        // 获取用户信息用于后续角色分配
        SysUser sysUser = sysUserMapper.selectUserByLoginName(checkerManage.getEhrNumber());
        
        int insertResult = checkerManageMapper.insertCheckerManage(checkerManage);
        if (insertResult > 0) {
            // 赋予角色540
            sysRoleService.insertAuthUsers(540L, sysUser.getUserId().toString());
        }
        return insertResult;
    }

    @Override
    public int validateUserInfo(String name, String ehrNumber) {
        // 根据EHR号查询用户
        SysUser sysUser = sysUserMapper.selectUserByLoginName(ehrNumber);
        if (sysUser == null) {
            System.out.println("未查到userId的EHR号: " + ehrNumber);
            return -3;
        }
        
        // 校验姓名是否匹配
        if (!name.equals(sysUser.getUserName())) {
            System.out.println("姓名与EHR号不匹配 - 输入姓名: " + name + ", 系统用户姓名: " + sysUser.getUserName() + ", EHR号: " + ehrNumber);
            return -4;
        }
        
        return 0;
    }

    @Override
    public int deleteCheckerManageByIds(String ids) {
        String[] idArr = ids.split(",");
        List<String> userIdList = new java.util.ArrayList<>();
        boolean hasMissingUserId = false;
        StringBuilder missingEhrNumbers = new StringBuilder();
        for (String idStr : idArr) {
            Integer id = Integer.parseInt(idStr);
            ExpenseCheckerManage checker = checkerManageMapper.selectCheckerManageById(id);
            if (checker != null) {
                SysUser sysUser = sysUserMapper.selectUserByLoginName(checker.getEhrNumber());
                if (sysUser != null) {
                    userIdList.add(sysUser.getUserId().toString());
                } else {
                    hasMissingUserId = true;
                    missingEhrNumbers.append(checker.getEhrNumber()).append(",");
                }
            }
        }
        if (hasMissingUserId) {
            System.out.println("未查到userId的EHR号: " + missingEhrNumbers.toString());
            return -3;
        }
        if (!userIdList.isEmpty()) {
            System.out.println("要删除的userId: " + userIdList);
            sysRoleService.deleteAuthUsers(540L, String.join(",", userIdList));
        }
        return checkerManageMapper.deleteCheckerManageByIds(java.util.Arrays.stream(idArr).map(Integer::parseInt).collect(java.util.stream.Collectors.toList()));
    }

    @Override
    public int updateCheckerManage(ExpenseCheckerManage checkerManage) {
        // 校验部门名称是否在expense_institude字典中
        String deptLabel = dictService.getLabel("expense_institude", checkerManage.getDepartmentName());
        if (deptLabel == null) {
            return -2;
        }
        
        return checkerManageMapper.updateCheckerManage(checkerManage);
    }
} 