package com.ruoyi.line.domain;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 账单推送数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-02-20
 */
public class BillPushDTO {
    
    /**
     * 账单元数据
     */
    private BillMetadata metadata;
    
    /**
     * 账单数据，key为账单编号（如"账单1"），value为账单详情
     */
    private Map<String, BillDetail> bills;
    
    /**
     * 账单元数据类
     */
    public static class BillMetadata {
        /**
         * 账单数量
         */
        private Integer billCount;
        
        /**
         * 发布人
         */
        private String publisher;

        public Integer getBillCount() {
            return billCount;
        }

        public void setBillCount(Integer billCount) {
            this.billCount = billCount;
        }

        public String getPublisher() {
            return publisher;
        }

        public void setPublisher(String publisher) {
            this.publisher = publisher;
        }
    }
    
    /**
     * 账单详情类
     */
    public static class BillDetail {
        /**
         * 账单相关数据
         */
        private BillData billData;
        
        /**
         * 费用明细列表
         */
        private List<ExpenseDetail> expenseDetails;

        public BillData getBillData() {
            return billData;
        }

        public void setBillData(BillData billData) {
            this.billData = billData;
        }

        public List<ExpenseDetail> getExpenseDetails() {
            return expenseDetails;
        }

        public void setExpenseDetails(List<ExpenseDetail> expenseDetails) {
            this.expenseDetails = expenseDetails;
        }
    }
    
    /**
     * 账单数据类
     */
    public static class BillData {
        /**
         * 费用类型
         */
        private String expenseType;
        
        /**
         * 计费周期
         */
        private String billingCycle;
        
        /**
         * 划账部门
         */
        private String department;
        
        /**
         * 含税总价
         */
        private String taxIncludingTotal;
        
        /**
         * 不含税总价
         */
        private String taxExcludingTotal;

        public String getExpenseType() {
            return expenseType;
        }

        public void setExpenseType(String expenseType) {
            this.expenseType = expenseType;
        }

        public String getBillingCycle() {
            return billingCycle;
        }

        public void setBillingCycle(String billingCycle) {
            this.billingCycle = billingCycle;
        }

        public String getDepartment() {
            return department;
        }

        public void setDepartment(String department) {
            this.department = department;
        }

        public String getTaxIncludingTotal() {
            return taxIncludingTotal;
        }

        public void setTaxIncludingTotal(String taxIncludingTotal) {
            this.taxIncludingTotal = taxIncludingTotal;
        }

        public String getTaxExcludingTotal() {
            return taxExcludingTotal;
        }

        public void setTaxExcludingTotal(String taxExcludingTotal) {
            this.taxExcludingTotal = taxExcludingTotal;
        }
    }
    
    /**
     * 费用明细类
     */
    public static class ExpenseDetail {
        /**
         * 费用名称
         */
        private String expenseName;
        
        /**
         * 编号
         */
        private String number;
        
        /**
         * 品牌
         */
        private String brand;
        
        /**
         * 具体规格
         */
        private String specification;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 费用变动情况
         */
        private String expenseChangeInfo;
        
        /**
         * 数量
         */
        private Integer quantity;
        
        /**
         * 含税单价
         */
        private BigDecimal taxIncludingPrice;
        
        /**
         * 不含税单价
         */
        private BigDecimal taxExcludingPrice;
        
        /**
         * 含税单行总价
         */
        private BigDecimal taxIncludingLineTotal;
        
        /**
         * 不含税单行总价
         */
        private BigDecimal taxExcludingLineTotal;

        public String getExpenseName() {
            return expenseName;
        }

        public void setExpenseName(String expenseName) {
            this.expenseName = expenseName;
        }

        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public String getBrand() {
            return brand;
        }

        public void setBrand(String brand) {
            this.brand = brand;
        }

        public String getSpecification() {
            return specification;
        }

        public void setSpecification(String specification) {
            this.specification = specification;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getExpenseChangeInfo() {
            return expenseChangeInfo;
        }

        public void setExpenseChangeInfo(String expenseChangeInfo) {
            this.expenseChangeInfo = expenseChangeInfo;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public BigDecimal getTaxIncludingPrice() {
            return taxIncludingPrice;
        }

        public void setTaxIncludingPrice(BigDecimal taxIncludingPrice) {
            this.taxIncludingPrice = taxIncludingPrice;
        }

        public BigDecimal getTaxExcludingPrice() {
            return taxExcludingPrice;
        }

        public void setTaxExcludingPrice(BigDecimal taxExcludingPrice) {
            this.taxExcludingPrice = taxExcludingPrice;
        }

        public BigDecimal getTaxIncludingLineTotal() {
            return taxIncludingLineTotal;
        }

        public void setTaxIncludingLineTotal(BigDecimal taxIncludingLineTotal) {
            this.taxIncludingLineTotal = taxIncludingLineTotal;
        }

        public BigDecimal getTaxExcludingLineTotal() {
            return taxExcludingLineTotal;
        }

        public void setTaxExcludingLineTotal(BigDecimal taxExcludingLineTotal) {
            this.taxExcludingLineTotal = taxExcludingLineTotal;
        }
    }

    public BillMetadata getMetadata() {
        return metadata;
    }

    public void setMetadata(BillMetadata metadata) {
        this.metadata = metadata;
    }

    public Map<String, BillDetail> getBills() {
        return bills;
    }

    public void setBills(Map<String, BillDetail> bills) {
        this.bills = bills;
    }
} 