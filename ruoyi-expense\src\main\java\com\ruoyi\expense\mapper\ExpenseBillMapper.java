package com.ruoyi.expense.mapper;

import java.util.List;
import com.ruoyi.expense.domain.ExpenseBill;
import org.apache.ibatis.annotations.Param;

/**
 * 账单数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface ExpenseBillMapper 
{
    /**
     * 查询账单数据
     * 
     * @param id 账单数据主键
     * @return 账单数据
     */
    public ExpenseBill selectExpenseBillById(Long id);

    /**
     * 根据费用类型、计费周期和划账部门查询账单
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param transferDepartment 划账部门
     * @return 账单数据
     */
    public ExpenseBill selectExpenseBillByUniqueKey(
        @Param("expense_type") String expenseType,
        @Param("billing_cycle") String billingCycle,
        @Param("transfer_department") String transferDepartment);

    /**
     * 查询账单数据列表
     * 
     * @param expenseBill 账单数据
     * @return 账单数据集合
     */
    public List<ExpenseBill> selectExpenseBillList(ExpenseBill expenseBill);

    /**
     * 新增账单数据
     * 
     * @param expenseBill 账单数据
     * @return 结果
     */
    public int insertExpenseBill(ExpenseBill expenseBill);

    /**
     * 修改账单数据
     * 
     * @param expenseBill 账单数据
     * @return 结果
     */
    public int updateExpenseBill(ExpenseBill expenseBill);

    /**
     * 删除账单数据
     * 
     * @param id 账单数据主键
     * @return 结果
     */
    public int deleteExpenseBillById(Long id);

    /**
     * 批量删除账单数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExpenseBillByIds(String[] ids);

    /**
     * 清空退回人和退回意见
     * @param billId 账单ID
     * @return 结果
     */
    int clearReturnInfo(Long billId);
} 