package com.ruoyi.expense.domain;

import java.io.Serializable;

/**
 * 费用管理微信通知实体类
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public class Expense_wechat implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 接收人EHR号 */
    private String recvEhr;

    /** 系统代码 */
    private String sysCode;

    /** 系统名称 */
    private String sysName;

    /** 消息标题 */
    private String msgTitle;

    /** 消息内容 */
    private String msgContent;

    public String getRecvEhr() {
        return recvEhr;
    }

    public void setRecvEhr(String recvEhr) {
        this.recvEhr = recvEhr;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    public String getSysName() {
        return sysName;
    }

    public void setSysName(String sysName) {
        this.sysName = sysName;
    }

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    @Override
    public String toString() {
        return "Expense_wechat{" +
                "recvEhr='" + recvEhr + '\'' +
                ", sysCode='" + sysCode + '\'' +
                ", sysName='" + sysName + '\'' +
                ", msgTitle='" + msgTitle + '\'' +
                ", msgContent='" + msgContent + '\'' +
                '}';
    }
} 