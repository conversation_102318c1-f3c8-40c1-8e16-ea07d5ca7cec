# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

这是一个 **若依管理系统** - 基于 Java Spring Boot 的企业级应用框架，包含多个功能模块。项目基于 Spring Boot 2.2.12.RELEASE 构建，采用 Maven 多模块架构。

## 构建和开发命令

### 启动应用程序
```bash
# 使用提供的 shell 脚本 (Linux/macOS)
./ry.sh start

# 直接使用 Maven
mvn clean compile
cd ruoyi-admin
mvn spring-boot:run

# 使用编译后的 JAR
java -jar target/ruoyi-admin.jar
```

### 管理应用程序
```bash
# 检查应用程序状态
./ry.sh status

# 停止应用程序
./ry.sh stop

# 重启应用程序
./ry.sh restart
```

### 构建项目
```bash
# 清理并编译所有模块
mvn clean compile

# 打包应用程序
mvn clean package

# 构建时跳过测试（如需要）
mvn clean package -DskipTests
```

### 数据库设置
- 需要 MySQL 数据库
- 配置文件：`ruoyi-admin/src/main/resources/application-druid.yml` (生产环境) 和 `application-test.yml` (测试环境)
- SQL 脚本文件位于 `sql/` 目录

## 架构概述

### 多模块结构
- **ruoyi-admin**: 主应用模块，包含 Web 控制器和配置
- **ruoyi-framework**: 核心框架组件和 Shiro 安全配置
- **ruoyi-system**: 系统管理（用户、角色、权限、菜单）
- **ruoyi-common**: 共享工具类、常量和通用组件
- **ruoyi-expense**: 费用/账单管理业务模块，包含 Excel 处理功能
- **ruoyi-lineManage**: 通讯线路管理模块
- **ruoyi-quartz**: 定时任务管理
- **ruoyi-generator**: 代码生成工具

### 核心技术栈
- **后端**: Spring Boot 2.2.12, MyBatis, Apache Shiro
- **前端**: Thymeleaf, Bootstrap, jQuery
- **数据库**: MySQL 配合 Druid 连接池
- **文件处理**: Apache POI 进行 Excel 操作
- **安全**: Apache Shiro 用于认证和授权
- **缓存**: EhCache

### 安全和权限
- 所有控制器方法使用 `@RequiresPermissions` 注解
- 基于角色的访问控制，支持分层权限
- 费用模块中基于部门的数据访问控制
- 启用 XSS 防护

## 费用模块架构

`ruoyi-expense` 模块是最复杂的业务模块，包含以下核心组件：

### 核心实体
- **ExpenseBill**: 主要账单实体，包含工作流状态（入库→发布→确认/退回）
- **ExpenseDetails**: 每个账单的行项目明细
- **ExpenseCheckerManage**: 用户审核员管理
- **ExpenseDepartmentVerifierInfo**: 部门特定的审核员配置

### 核心功能
- **Excel 上传**: 复杂的 Excel 文件处理，支持验证和多工作表
- **工作流管理**: 多阶段审批流程与状态转换
- **通知系统**: 集成微信进行工作流通知
- **基于角色的访问**: 为管理员、费用管理员和普通用户提供不同视图
- **部门安全**: 基于部门分配的访问控制

### 重要模式
- 大量使用 DTO 处理复杂数据结构
- 基于 Thymeleaf 的模板前端
- 具有明确业务规则的工作流状态管理
- 基于部门的安全过滤

## 配置管理

### 环境配置文件
- **生产环境**: `application-druid.yml` (端口 8088)
- **测试环境**: `application-test.yml` (端口 8090)
- **基础配置**: `application.yml` 包含通用设置

### 关键配置
```yaml
# 费用模块 API 端点
expense:
  receive:
    path: /expense/receive
    base-url: http://localhost:8088  # 环境特定配置

# 文件上传限制
spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
```

## 开发指南

### 代码生成
- 使用 `ruoyi-generator` 模块进行脚手架生成
- 模板位于 `ruoyi-generator/src/main/resources/vm/`
- 生成完整的 CRUD 操作和前端模板

### 数据库操作
- 使用 MyBatis 和 XML 映射器
- 所有映射器位于 `src/main/resources/mapper/` 目录
- 遵循现有命名约定：`*Mapper.xml`

### 前端开发
- 模板位于 `src/main/resources/templates/`
- 静态资源位于 `src/main/resources/static/`
- 遵循项目中使用的 Bootstrap 和 jQuery 模式

### 测试
- 未找到特定的测试框架配置
- 建议通过 Web 界面进行手动测试
- 可以通过 Swagger UI 进行 API 测试（如果启用）

## 常见开发任务

### 添加新功能
1. 在适当的模块中定义领域实体
2. 创建 MyBatis 映射器接口和 XML
3. 实现服务接口和实现类
4. 创建具有适当权限的控制器
5. 如需要，添加前端模板
6. 更新菜单和权限配置

### 使用 Excel 功能
- 使用 Apache POI 5.2.3 进行 Excel 处理
- 遵循 `BillUploadServiceImpl` 中的文件验证模式
- 处理多工作表和复杂数据结构
- 实现适当的错误处理和用户反馈

### 权限管理
- 通过管理界面在数据库中定义权限
- 使用 `@RequiresPermissions("module:action:operation")` 模式
- 对敏感操作考虑基于部门的访问控制

## 重要注意事项

- 项目大量使用中文注释和变量名
- 费用模块具有全面的 Excel 处理能力
- 安全性高度集成 - 所有操作都需要适当的权限
- 应用程序通过 Spring DevTools 支持热部署
- 文件上传配置支持大文件（最大 50MB）