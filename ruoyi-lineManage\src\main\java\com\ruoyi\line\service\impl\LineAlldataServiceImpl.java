package com.ruoyi.line.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.ruoyi.common.annotation.DataScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.line.mapper.LineAlldataMapper;
import com.ruoyi.line.domain.LineAlldata;
import com.ruoyi.line.service.ILineAlldataService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;

/**
 * 通讯线路管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
public class LineAlldataServiceImpl implements ILineAlldataService 
{
    @Autowired
    private LineAlldataMapper lineAlldataMapper;

    /**
     * 查询通讯线路管理
     * 
     * @param lineCode 通讯线路管理ID
     * @return 通讯线路管理
     */
    @Override
    public LineAlldata selectLineAlldataById(Long lineCode)
    {
        return lineAlldataMapper.selectLineAlldataById(lineCode);
    }

    /**
     * 查询通讯线路管理列表
     * 
     * @param lineAlldata 通讯线路管理
     * @return 通讯线路管理
     */
    @Override
    @DataScope(deptAlias = "line_alldata",
            deptNoFieldName = "INSTITUTION",
            roleKey = "line_viewer")
    public List<LineAlldata> selectLineAlldataList(LineAlldata lineAlldata)
    {
        return lineAlldataMapper.selectLineAlldataList(lineAlldata);
    }

    /**
     * 新增通讯线路管理
     * 
     * @param lineAlldata 通讯线路管理
     * @return 结果
     */
    @Override
    public int insertLineAlldata(LineAlldata lineAlldata)
    {
        return lineAlldataMapper.insertLineAlldata(lineAlldata);
    }

    /**
     * 修改通讯线路管理
     * 
     * @param lineAlldata 通讯线路管理
     * @return 结果
     */
    @Override
    public int updateLineAlldata(LineAlldata lineAlldata)
    {
        return lineAlldataMapper.updateLineAlldata(lineAlldata);
    }

    /**
     * 删除通讯线路管理对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteLineAlldataByIds(String ids)
    {
        return lineAlldataMapper.deleteLineAlldataByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除通讯线路管理信息
     * 
     * @param lineCode 通讯线路管理ID
     * @return 结果
     */
    @Override
    public int deleteLineAlldataById(Long lineCode)
    {
        return lineAlldataMapper.deleteLineAlldataById(lineCode);
    }


    @Override
    @Transactional
    public int calculateLineAlldataById(String ids)
    {
        String[] strs = StringUtils.split(ids, ",");

        for (int i=0;i < strs.length;i++)
        {
            LineAlldata LineAlldata_OLD = lineAlldataMapper.selectLineAlldataById(Long.parseLong(strs[i]));   //keyUpsNo
            BigDecimal TaxIncludingPrice = LineAlldata_OLD.getTaxIncludingPrice();
            BigDecimal TaxRate = LineAlldata_OLD.getTaxRate();
            BigDecimal TaxExcludingPrice = BigDecimal.ZERO;
            String operator = LineAlldata_OLD.getOperator();
            System.out.println("运营商是"+operator);
            if (!operator.equals("1"))  //当运营商不为电信时
            {
                BigDecimal divisor = BigDecimal.ONE.add(TaxRate);
                TaxExcludingPrice = TaxIncludingPrice.divide(divisor, 2, RoundingMode.HALF_UP);
            }
            else   //当运营商为电信时
            {
                return -1;
            }

            LineAlldata_OLD.setTaxExcludingPrice(TaxExcludingPrice);      //更新数据库
            lineAlldataMapper.updateLineAlldata(LineAlldata_OLD);
        }
        System.out.println("修改成功");
        return 1;
    }
}
