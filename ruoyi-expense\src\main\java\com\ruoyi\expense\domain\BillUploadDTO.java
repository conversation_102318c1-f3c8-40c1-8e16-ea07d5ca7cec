package com.ruoyi.expense.domain;

import org.springframework.web.multipart.MultipartFile;

/**
 * 账单上传数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public class BillUploadDTO {
    
    /** 账单数量 */
    private Integer billCount;
    
    /** 发布人 */
    private String publisher;
    
    /** 上传的Excel文件 */
    private MultipartFile file;

    public Integer getBillCount() {
        return billCount;
    }

    public void setBillCount(Integer billCount) {
        this.billCount = billCount;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }



    @Override
    public String toString() {
        return "BillUploadDTO{" +
                "billCount=" + billCount +
                ", publisher='" + publisher + '\'' +
                ", file=" + (file != null ? file.getOriginalFilename() : "null") +
                '}';
    }
}