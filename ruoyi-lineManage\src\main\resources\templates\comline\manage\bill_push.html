<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('账单推送')" />
    <style>
        /* 自定义复选框样式 */
        .custom-checkbox {
            margin-right: 10px;
            margin-bottom: 5px;
            display: inline-block;
        }
        .custom-checkbox input[type="checkbox"] {
            margin-right: 5px;
            transform: scale(1.2);
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-bill-push">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">选择需要推送的账单：</label>
                <div class="col-sm-9">
                    <div id="checkboxContainer" th:with="type=${@dict.getType('line_institude')}">
                        <div class="custom-checkbox" th:each="dict : ${type}">
                            <input type="checkbox" th:id="${'inst_' + dict.dictValue}" name="institutions" th:value="${dict.dictValue}">
                            <label th:for="${'inst_' + dict.dictValue}" th:text="${dict.dictLabel}"></label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="help-block m-b-none">已选择 <span id="selectedCount">0</span> 个机构</div>
                        </div>
                        <div class="col-sm-6 text-right">
                            <button type="button" class="btn btn-default btn-xs" onclick="selectAll()"><i class="fa fa-check-square-o"></i> 全选</button>
                            <button type="button" class="btn btn-default btn-xs" onclick="unselectAll()"><i class="fa fa-square-o"></i> 取消全选</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">账单发布人：</label>
                <div class="col-sm-9">
                    <input type="text" name="publisher" class="form-control" placeholder="请输入账单发布人（仅限汉字）" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">计费周期：</label>
                <div class="col-sm-9">
                    <input type="text" name="billingCycle" class="form-control" placeholder="格式如：202502 或 202502-06" required>
                </div>
            </div>
        </form>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10">
                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>提 交</button>
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "comline/manage";
        
        // 页面加载完成后初始化
        document.addEventListener("DOMContentLoaded", function() {
            // 为每个复选框添加change事件，更新已选择计数
            var checkboxes = document.querySelectorAll('input[name="institutions"]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].addEventListener('change', updateSelectedCount);
            }
            
            // 初始更新选中计数
            updateSelectedCount();
        });
        
        // 更新已选择的计数
        function updateSelectedCount() {
            var checkedBoxes = document.querySelectorAll('input[name="institutions"]:checked');
            document.getElementById('selectedCount').textContent = checkedBoxes.length;
        }
        
        // 全选功能
        function selectAll() {
            var checkboxes = document.querySelectorAll('input[name="institutions"]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = true;
            }
            updateSelectedCount();
        }
        
        // 取消全选功能
        function unselectAll() {
            var checkboxes = document.querySelectorAll('input[name="institutions"]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = false;
            }
            updateSelectedCount();
        }
        
        // 提交表单
        function submitHandler() {
            var checkedBoxes = document.querySelectorAll('input[name="institutions"]:checked');
            if (checkedBoxes.length === 0) {
                $.modal.alertWarning("请至少选择一个机构");
                return;
            }
            
            // 验证账单发布人（必须是纯汉字）
            var publisher = document.querySelector('input[name="publisher"]').value.trim();
            if (!publisher) {
                $.modal.alertWarning("请输入账单发布人");
                return;
            }
            if (!/^[\u4e00-\u9fa5]+$/.test(publisher)) {
                $.modal.alertWarning("账单发布人只能包含汉字");
                return;
            }
            
            // 验证计费周期（必须符合格式：YYYYMM 或 YYYYMM-MM）
            var billingCycle = document.querySelector('input[name="billingCycle"]').value.trim();
            if (!billingCycle) {
                $.modal.alertWarning("请输入计费周期");
                return;
            }
            if (!/^\d{6}(-\d{2})?$/.test(billingCycle)) {
                $.modal.alertWarning("计费周期格式错误，应为格式如：202502 或 202502-06");
                return;
            }
            
            var selectedValues = [];
            for (var i = 0; i < checkedBoxes.length; i++) {
                selectedValues.push(checkedBoxes[i].value);
            }
            
            console.log("选中的机构: " + selectedValues.join(','));
            
            // 构建提交数据
            var data = {
                institutions: selectedValues.join(','),
                publisher: publisher,
                billingCycle: billingCycle
            };
            
            // 提交数据
            $.ajax({
                type: "post",
                url: prefix + "/bill_push",
                data: data,
                success: function(result) {
                    if (result.code == 0) {
                        // 使用后端返回的详细信息
                        var detailedMessage = result.detailedMessage || "账单推送成功！";
                        
                        // 将换行符转换为HTML换行标签
                        var htmlMessage = detailedMessage.replace(/\n/g, '<br/>');
                        
                        layer.alert(htmlMessage, {
                            icon: 1,
                            title: "推送结果",
                            area: ['600px', '500px'],  // 设置固定的宽度和最大高度
                            maxHeight: 500,  // 设置最大高度
                            scrollbar: true,  // 启用滚动条
                            btn: ['关闭'],
                            yes: function(index) {
                                layer.close(index);
                                $.modal.closeTab();
                            }
                        });
                    } else {
                        // 显示后端返回的详细错误信息
                        var errorMessage = result.msg;
                        
                        // 使用更醒目的样式显示错误信息
                        layer.alert(errorMessage, {
                            icon: 2,
                            title: "错误提示",
                            area: ['600px', '400px'],  // 设置固定的宽度和高度
                            maxHeight: 400,  // 设置最大高度
                            scrollbar: true,  // 启用滚动条
                            skin: 'layui-layer-molv',
                            anim: 6,
                            btn: ['我知道了'],
                            yes: function(index) {
                                layer.close(index);
                            }
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // 显示详细的系统错误信息
                    var errorMessage;
                    try {
                        if (xhr.responseJSON) {
                            errorMessage = xhr.responseJSON.msg;
                        } else if (xhr.responseText) {
                            try {
                                var response = JSON.parse(xhr.responseText);
                                errorMessage = response.msg;
                            } catch (e) {
                                errorMessage = xhr.responseText;
                            }
                        } else {
                            errorMessage = "系统错误：" + error;
                        }
                    } catch (e) {
                        console.error("解析错误响应失败：", e);
                        errorMessage = "系统发生未知错误，请联系管理员";
                    }
                    
                    // 使用更醒目的样式显示错误信息
                    layer.alert(errorMessage, {
                        icon: 2,
                        title: "错误提示",
                        area: ['600px', '400px'],  // 设置固定的宽度和高度
                        maxHeight: 400,  // 设置最大高度
                        scrollbar: true,  // 启用滚动条
                        skin: 'layui-layer-molv',
                        anim: 6,
                        btn: ['我知道了'],
                        yes: function(index) {
                            layer.close(index);
                        }
                    });
                }
            });
        }
        
        /* 关闭弹出窗口 */
        function closeItem() {
            $.modal.close();
        }
    </script>
</body>
</html>