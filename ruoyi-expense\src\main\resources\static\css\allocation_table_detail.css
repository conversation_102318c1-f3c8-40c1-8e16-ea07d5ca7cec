/* 分摊表详情页面样式优化 */

/* 基本信息区域整体样式 */
.allocation-detail-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.allocation-detail-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    margin: 0;
    border-bottom: 3px solid #5a67d8;
}

.allocation-detail-header h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.allocation-detail-header h5 i {
    margin-right: 10px;
    font-size: 16px;
}

.allocation-detail-content {
    padding: 25px;
    background: #fafbfc;
}

/* 信息行样式 */
.info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #667eea;
}

.info-row:last-child {
    margin-bottom: 0;
}

/* 信息项样式 */
.info-item {
    flex: 1;
    min-width: 250px;
    margin-bottom: 15px;
    padding-right: 20px;
}

.info-item:last-child {
    padding-right: 0;
}

.info-item.full-width {
    flex: 1 1 100%;
    min-width: 100%;
}

.info-item.half-width {
    flex: 1 1 50%;
    min-width: 300px;
}

.info-item.third-width {
    flex: 1 1 33.333%;
    min-width: 200px;
}

/* 标签样式 */
.info-label {
    display: inline-block;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
    font-size: 14px;
    min-width: 80px;
    position: relative;
}

.info-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #667eea;
    border-radius: 1px;
}

/* 内容样式 */
.info-value {
    display: block;
    color: #2d3748;
    font-size: 15px;
    line-height: 1.5;
    padding: 8px 12px;
    background: #f7fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    min-height: 38px;
    display: flex;
    align-items: center;
}

/* 状态徽章优化 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-badge.status-pending {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    color: #d63031;
    border: 1px solid #fdcb6e;
}

.status-badge.status-approved {
    background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);
    color: #00695c;
    border: 1px solid #00b894;
}

.status-badge.status-rejected {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: #ffffff;
    border: 1px solid #e84393;
}

/* 修改意见特殊样式 */
.comments-section {
    background: #fff8e1;
    border: 1px solid #ffcc02;
    border-left: 4px solid #ff9800;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.comments-section .info-label {
    color: #e65100;
    font-weight: 700;
}

.comments-section .info-value {
    background: #ffffff;
    border: 1px solid #ffcc02;
    color: #bf360c;
    font-style: italic;
    min-height: auto;
    padding: 12px 15px;
    line-height: 1.6;
}

/* 时间信息特殊样式 */
.time-info .info-value {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #2e7d32;
    font-family: 'Courier New', monospace;
}

/* 人员信息样式 */
.person-info .info-value {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1565c0;
}

/* 基本信息样式 */
.basic-info .info-value {
    background: #f3e5f5;
    border-color: #9c27b0;
    color: #6a1b9a;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .allocation-detail-content {
        padding: 15px;
    }
    
    .info-row {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .info-item {
        flex: 1 1 100%;
        min-width: 100%;
        padding-right: 0;
        margin-bottom: 15px;
    }
    
    .info-item.half-width,
    .info-item.third-width {
        flex: 1 1 100%;
        min-width: 100%;
    }
    
    .allocation-detail-header {
        padding: 15px 20px;
    }
    
    .allocation-detail-header h5 {
        font-size: 16px;
    }
}

@media (max-width: 576px) {
    .allocation-detail-header h5 {
        font-size: 14px;
    }
    
    .info-label {
        font-size: 13px;
    }
    
    .info-value {
        font-size: 14px;
        padding: 6px 10px;
    }
    
    .status-badge {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* 动画效果 */
.info-row {
    transition: all 0.3s ease;
}

.info-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-value {
    transition: all 0.2s ease;
}

.info-value:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

/* 图标样式 */
.info-icon {
    margin-right: 8px;
    color: #667eea;
    font-size: 14px;
}
