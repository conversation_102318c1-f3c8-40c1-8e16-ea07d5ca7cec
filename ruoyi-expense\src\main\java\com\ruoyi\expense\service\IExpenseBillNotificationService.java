package com.ruoyi.expense.service;

import com.ruoyi.expense.domain.ExpenseBill;
import java.util.List;

/**
 * 账单通知服务接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface IExpenseBillNotificationService {
    
    /**
     * 发送账单通知给部门核对人
     * 
     * @param bills 账单列表
     * @return 通知发送结果
     */
    String sendBillNotifications(List<ExpenseBill> bills);
    
    /**
     * 发送单个账单通知
     * 
     * @param bill 账单信息
     * @return 通知发送结果
     */
    String sendSingleBillNotification(ExpenseBill bill);
    
    /**
     * 根据部门获取核对人EHR号
     * 
     * @param departmentName 部门名称
     * @return 核对人EHR号
     */
    String getVerifierEhrByDepartment(String departmentName);
    
    /**
     * 根据部门获取所有核对人的EHR号列表
     * 
     * @param departmentName 部门名称
     * @return 核对人EHR号列表
     */
    List<String> getAllVerifierEhrsByDepartment(String departmentName);
    
    /**
     * 发送账单退回通知给账单上传人
     * 
     * @param bill 账单信息
     * @param refuseComment 退回意见
     * @return 通知发送结果
     */
    String sendBillReturnNotification(ExpenseBill bill, String refuseComment);
    
    /**
     * 发送账单撤回通知给账单上传人
     * 
     * @param bill 账单信息
     * @param revokeComment 撤回说明
     * @return 通知发送结果
     */
    String sendBillRevokeNotification(ExpenseBill bill, String revokeComment);
    
    /**
     * 批量发送账单撤回通知给账单上传人
     * 
     * @param bills 账单列表
     * @param revokeComment 撤回说明
     * @return 通知发送结果
     */
    String sendBillRevokeNotifications(List<ExpenseBill> bills, String revokeComment);
    
    /**
     * 批量发送账单退回通知给账单上传人
     * 
     * @param bills 账单列表
     * @param refuseComment 退回意见
     * @return 通知发送结果
     */
    String sendBillReturnNotifications(List<ExpenseBill> bills, String refuseComment);
    
    /**
     * 根据用户名获取用户的EHR号
     * 
     * @param userName 用户名
     * @return 用户EHR号
     */
    String getUserEhrByUserName(String userName);
} 