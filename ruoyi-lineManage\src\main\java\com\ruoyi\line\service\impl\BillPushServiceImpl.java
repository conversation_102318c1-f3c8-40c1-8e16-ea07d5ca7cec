package com.ruoyi.line.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.line.domain.BillPushDTO;
import com.ruoyi.line.domain.BillPushResult;
import com.ruoyi.line.domain.LineAlldata;
import com.ruoyi.line.mapper.LineAlldataMapper;
import com.ruoyi.line.service.IBillPushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账单推送Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-20
 */
@Service
public class BillPushServiceImpl implements IBillPushService {
    
    private static final Logger log = LoggerFactory.getLogger(BillPushServiceImpl.class);
    
    @Autowired
    private LineAlldataMapper lineAlldataMapper;
    
    @Autowired
    private DictService dictService;
    
    @Value("${expense.receive.base-url}")
    private String expenseBaseUrl;
    
    @Value("${expense.receive.path:/expense/receive}")
    private String expensePath;
    
    // 字典缓存，避免重复查询
    private Map<String, Map<String, String>> dictCache = new HashMap<>();
    
    /**
     * 根据机构ID列表查询账单数据并推送
     * 
     * @param institutionIds 机构ID列表，多个ID用逗号分隔
     * @param publisher 账单发布人
     * @param billingCycle 计费周期
     * @return 详细的推送结果，包括有账单和无账单的机构信息
     */
    @Override
    public BillPushResult pushBillData(String institutionIds, String publisher, String billingCycle) {
        BillPushResult result = new BillPushResult();
        HttpURLConnection connection = null;
        
        try {
            // 解析机构ID列表
            String[] institutionIdArray = institutionIds.split(",");
            result.setTotalInstitutions(institutionIdArray.length);
            
            // 构建完整的请求URL
            String requestUrl = expenseBaseUrl + expensePath;
            
            log.info("=================== 开始推送账单 ===================");
            log.info("请求方法: POST");
            log.info("请求URL: {}", requestUrl);
            
            // 初始化字典缓存
            initDictCache();
            
            // 构建账单推送数据并统计机构信息
            BillPushDTO billPushDTO = buildBillPushDataWithStatistics(institutionIds, publisher, billingCycle, result);
            
            // 如果没有任何账单，直接返回成功结果
            if (result.getActualSentCount() == 0) {
                result.setSuccess(true);
                log.info("所有机构都没有账单数据，推送完成");
                return result;
            }
            
            // 将DTO转换为中文键名的JSON
            String jsonData = convertToChineseKeyJson(billPushDTO, false);
            log.info("请求数据: {}", jsonData);
            
            // 创建URL对象
            URL url = new URL(requestUrl);
            
            // 打开连接
            connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法
            connection.setRequestMethod("POST");
            
            // 设置请求头
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            
            // 启用输入输出流
            connection.setDoOutput(true);
            connection.setDoInput(true);
            
            // 设置连接和读取超时
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(30000);
            
            // 写入请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
                os.flush();
            }
            
            // 获取响应码
            int responseCode = connection.getResponseCode();
            log.info("响应状态码: {}", responseCode);
            
            // 读取响应
            StringBuilder response = new StringBuilder();
            InputStream inputStream = null;
            try {
                inputStream = responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream();
                try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                }
            } catch (Exception e) {
                log.error("读取响应失败", e);
            }
            
            String responseBody = response.toString();
            log.info("响应内容: {}", responseBody);
            
            // 解析响应内容
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> responseMap = mapper.readValue(responseBody, Map.class);
                Integer code = responseMap.get("code") instanceof Number ? ((Number) responseMap.get("code")).intValue() : null;
                String msg = responseMap.get("msg") != null ? responseMap.get("msg").toString() : "未知错误";
                
                if (code != null && code == 0) {
                    log.info("账单推送成功");
                    result.setSuccess(true);
                    return result;
                } else {
                    log.error("账单推送失败：{}", msg);
                    result.setSuccess(false);
                    result.setErrorMessage(msg);
                    return result;
                }
            } catch (Exception e) {
                log.error("账单推送失败", e);
                result.setSuccess(false);
                result.setErrorMessage(e.getMessage());
                return result;
            }
        } catch (Exception e) {
            log.error("账单推送过程中发生异常", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            return result;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            log.info("===============================================");
        }
    }
    
    /**
     * 初始化字典缓存
     */
    private void initDictCache() {
        // 缓存机构字典
        List<SysDictData> institutionDictList = dictService.getType("line_institude");
        Map<String, String> institutionDict = new HashMap<>();
        for (SysDictData dict : institutionDictList) {
            institutionDict.put(dict.getDictValue(), dict.getDictLabel());
        }
        dictCache.put("line_institude", institutionDict);
        
        // 缓存运营商字典
        List<SysDictData> operatorDictList = dictService.getType("line_operator");
        Map<String, String> operatorDict = new HashMap<>();
        for (SysDictData dict : operatorDictList) {
            operatorDict.put(dict.getDictValue(), dict.getDictLabel());
        }
        dictCache.put("line_operator", operatorDict);
    }
    
    /**
     * 根据字典类型和值获取标签
     * 
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    private String getDictLabel(String dictType, String dictValue) {
        if (StringUtils.isEmpty(dictValue)) {
            return "";
        }
        
        Map<String, String> dictMap = dictCache.get(dictType);
        if (dictMap != null && dictMap.containsKey(dictValue)) {
            return dictMap.get(dictValue);
        }
        
        // 如果缓存中没有，则从服务中获取
        String label = dictService.getLabel(dictType, dictValue);
        return StringUtils.isEmpty(label) ? dictValue : label;
    }
    
    /**
     * 将DTO转换为使用中文键名的JSON字符串
     * 
     * @param billPushDTO 账单推送DTO
     * @param prettyPrint 是否美化输出
     * @return 中文键名的JSON字符串
     */
    private String convertToChineseKeyJson(BillPushDTO billPushDTO, boolean prettyPrint) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        if (prettyPrint) {
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        }
        
        // 创建根节点
        ObjectNode rootNode = objectMapper.createObjectNode();
        
        // 添加账单元数据
        ObjectNode metadataNode = rootNode.putObject("账单元数据");
        metadataNode.put("账单数量", billPushDTO.getMetadata().getBillCount());
        metadataNode.put("发布人", billPushDTO.getMetadata().getPublisher());
        
        // 获取账单Map并按照账单编号排序
        Map<String, BillPushDTO.BillDetail> billMap = billPushDTO.getBills();
        List<String> sortedKeys = new ArrayList<>(billMap.keySet());
        Collections.sort(sortedKeys, (k1, k2) -> {
            // 提取账单编号中的数字部分进行比较
            int num1 = Integer.parseInt(k1.replaceAll("账单", ""));
            int num2 = Integer.parseInt(k2.replaceAll("账单", ""));
            return Integer.compare(num1, num2);
        });
        
        // 按照排序后的顺序添加账单
        for (String key : sortedKeys) {
            BillPushDTO.BillDetail billDetail = billMap.get(key);
            
            // 创建账单节点
            ObjectNode billNode = rootNode.putObject(key);
            
            // 添加账单相关数据
            ObjectNode billDataNode = billNode.putObject("账单相关数据");
            billDataNode.put("费用类型", billDetail.getBillData().getExpenseType());
            billDataNode.put("计费周期", billDetail.getBillData().getBillingCycle());
            
            // 使用字典转换后的划账部门名称
            String departmentLabel = getDictLabel("line_institude", billDetail.getBillData().getDepartment());
            billDataNode.put("划账部门", departmentLabel);
            
            billDataNode.put("含税总价", billDetail.getBillData().getTaxIncludingTotal());
            billDataNode.put("不含税总价", billDetail.getBillData().getTaxExcludingTotal());
            
            // 添加费用明细
            ArrayNode expenseDetailsNode = billNode.putArray("费用明细");
            for (BillPushDTO.ExpenseDetail expenseDetail : billDetail.getExpenseDetails()) {
                ObjectNode detailNode = expenseDetailsNode.addObject();
                detailNode.put("费用名称", expenseDetail.getExpenseName());
                
                // 添加编号字段
                detailNode.put("编号", expenseDetail.getNumber() != null ? expenseDetail.getNumber() : "");
                
                // 使用字典转换后的品牌名称（运营商）
                detailNode.put("品牌", expenseDetail.getBrand());
                
                detailNode.put("具体规格", expenseDetail.getSpecification());
                detailNode.put("备注", expenseDetail.getRemark() != null ? expenseDetail.getRemark() : "");
                detailNode.put("费用变动情况", expenseDetail.getExpenseChangeInfo());
                detailNode.put("数量", expenseDetail.getQuantity());
                detailNode.put("含税单价", expenseDetail.getTaxIncludingPrice() != null ? 
                        expenseDetail.getTaxIncludingPrice().doubleValue() : 0);
                detailNode.put("不含税单价", expenseDetail.getTaxExcludingPrice() != null ? 
                        expenseDetail.getTaxExcludingPrice().doubleValue() : 0);
                detailNode.put("含税单行总价", expenseDetail.getTaxIncludingLineTotal() != null ? 
                        expenseDetail.getTaxIncludingLineTotal().doubleValue() : 0);
                detailNode.put("不含税单行总价", expenseDetail.getTaxExcludingLineTotal() != null ? 
                        expenseDetail.getTaxExcludingLineTotal().doubleValue() : 0);
            }
        }
        
        return objectMapper.writeValueAsString(rootNode);
    }
    
    /**
     * 构建账单推送数据
     * 
     * @param institutionIds 机构ID列表，多个ID用逗号分隔
     * @param publisher 账单发布人
     * @param billingCycle 计费周期
     * @return 账单推送数据对象
     */
    @Override
    public BillPushDTO buildBillPushData(String institutionIds, String publisher, String billingCycle) {
        // 解析机构ID列表
        String[] institutionIdArray = institutionIds.split(",");
        
        // 创建账单推送DTO对象
        BillPushDTO billPushDTO = new BillPushDTO();
        
        // 设置账单元数据
        BillPushDTO.BillMetadata metadata = new BillPushDTO.BillMetadata();
        metadata.setBillCount(institutionIdArray.length);
        metadata.setPublisher(publisher);
        billPushDTO.setMetadata(metadata);
        
        // 创建账单Map
        Map<String, BillPushDTO.BillDetail> billMap = new HashMap<>();
        
        // 遍历每个机构ID，构建对应的账单
        for (int i = 0; i < institutionIdArray.length; i++) {
            String institutionId = institutionIdArray[i];
            
            // 查询该机构的线路数据
            List<LineAlldata> lineList = queryLineDataByInstitution(institutionId);
            
            if (lineList != null && !lineList.isEmpty()) {
                // 构建账单详情
                BillPushDTO.BillDetail billDetail = buildBillDetail(lineList, billingCycle);
                
                // 将账单详情添加到Map中
                billMap.put("账单" + (i + 1), billDetail);
            }
        }
        
        billPushDTO.setBills(billMap);
        
        return billPushDTO;
    }

    /**
     * 构建账单推送数据并统计机构信息
     * 
     * @param institutionIds 机构ID列表，多个ID用逗号分隔
     * @param publisher 账单发布人
     * @param billingCycle 计费周期
     * @param result 推送结果对象，用于记录统计信息
     * @return 账单推送数据对象
     */
    public BillPushDTO buildBillPushDataWithStatistics(String institutionIds, String publisher, String billingCycle, BillPushResult result) {
        // 解析机构ID列表
        String[] institutionIdArray = institutionIds.split(",");
        
        // 创建账单推送DTO对象
        BillPushDTO billPushDTO = new BillPushDTO();
        
        // 创建账单Map
        Map<String, BillPushDTO.BillDetail> billMap = new HashMap<>();
        
        int actualSentCount = 0;
        
        // 遍历每个机构ID，构建对应的账单
        for (int i = 0; i < institutionIdArray.length; i++) {
            String institutionId = institutionIdArray[i];
            
            // 查询该机构的线路数据
            List<LineAlldata> lineList = queryLineDataByInstitution(institutionId);
            
            // 获取机构名称
            String institutionName = getDictLabel("line_institude", institutionId);
            
            if (lineList != null && !lineList.isEmpty()) {
                // 构建账单详情
                BillPushDTO.BillDetail billDetail = buildBillDetail(lineList, billingCycle);
                
                // 将账单详情添加到Map中
                billMap.put("账单" + (actualSentCount + 1), billDetail);
                actualSentCount++;
                
                // 记录有账单的机构
                result.addInstitutionWithBills(institutionId, institutionName, lineList.size());
            } else {
                // 记录无账单的机构
                result.addInstitutionWithoutBills(institutionId, institutionName);
            }
        }
        
        // 设置实际发送的账单数量
        result.setActualSentCount(actualSentCount);
        
        // 设置账单元数据
        BillPushDTO.BillMetadata metadata = new BillPushDTO.BillMetadata();
        metadata.setBillCount(actualSentCount);
        metadata.setPublisher(publisher);
        billPushDTO.setMetadata(metadata);
        
        billPushDTO.setBills(billMap);
        
        return billPushDTO;
    }
    
    /**
     * 根据机构ID查询线路数据
     * 
     * @param institutionId 机构ID
     * @return 线路数据列表
     */
    private List<LineAlldata> queryLineDataByInstitution(String institutionId) {
        // 创建查询条件
        LineAlldata queryParam = new LineAlldata();
        queryParam.setInstitution(institutionId);
        
        // 查询线路数据
        List<LineAlldata> lineList = lineAlldataMapper.selectLineAlldataList(queryParam);
        
        // 过滤掉状态为3的记录
        return lineList.stream()
                .filter(line -> !"3".equals(line.getLineStatus()))
                .collect(Collectors.toList());
    }
    
    /**
     * 构建账单详情
     * 
     * @param lineList 线路数据列表
     * @param billingCycle 计费周期
     * @return 账单详情对象
     */
    private BillPushDTO.BillDetail buildBillDetail(List<LineAlldata> lineList, String billingCycle) {
        BillPushDTO.BillDetail billDetail = new BillPushDTO.BillDetail();
        
        // 获取第一条记录的机构ID
        String departmentId = lineList.get(0).getInstitution();
        
        // 计算含税总价和不含税总价
        BigDecimal taxIncludingTotal = BigDecimal.ZERO;
        BigDecimal taxExcludingTotal = BigDecimal.ZERO;
        
        for (LineAlldata line : lineList) {
            if (line.getTaxIncludingPrice() != null) {
                taxIncludingTotal = taxIncludingTotal.add(line.getTaxIncludingPrice());
            }
            
            if (line.getTaxExcludingPrice() != null) {
                taxExcludingTotal = taxExcludingTotal.add(line.getTaxExcludingPrice());
            }
        }
        
        // 设置账单数据
        BillPushDTO.BillData billData = new BillPushDTO.BillData();
        billData.setExpenseType("通讯线路");
        billData.setBillingCycle(billingCycle);
        billData.setDepartment(departmentId); // 保存原始ID，在JSON转换时再转为标签
        billData.setTaxIncludingTotal(taxIncludingTotal.toString());
        billData.setTaxExcludingTotal(taxExcludingTotal.toString());
        
        billDetail.setBillData(billData);
        
        // 构建费用明细列表
        List<BillPushDTO.ExpenseDetail> expenseDetails = new ArrayList<>();
        
        for (LineAlldata line : lineList) {
            BillPushDTO.ExpenseDetail expenseDetail = convertToExpenseDetail(line);
            expenseDetails.add(expenseDetail);
        }
        
        billDetail.setExpenseDetails(expenseDetails);
        
        return billDetail;
    }
    
    /**
     * 将线路数据转换为费用明细
     * 
     * @param line 线路数据
     * @return 费用明细对象
     */
    private BillPushDTO.ExpenseDetail convertToExpenseDetail(LineAlldata line) {
        BillPushDTO.ExpenseDetail expenseDetail = new BillPushDTO.ExpenseDetail();
        
        // 设置基本信息
        expenseDetail.setExpenseName(line.getLineName());
        
        // 设置编号（线路编号）
        expenseDetail.setNumber(line.getLineNumber());
        
        // 使用字典转换运营商名称
        String operatorLabel = getDictLabel("line_operator", line.getOperator());
        expenseDetail.setBrand(operatorLabel);
        
        expenseDetail.setSpecification(line.getLineBandwidth());
        expenseDetail.setRemark(line.getRemark());
        
        // 生成费用变动情况
        String expenseChangeInfo = generateExpenseChangeInfo(line.getLineAddDate(), line.getLineCancelDate());
        expenseDetail.setExpenseChangeInfo(expenseChangeInfo);
        
        // 设置数量和价格信息
        expenseDetail.setQuantity(1);
        expenseDetail.setTaxIncludingPrice(line.getTaxIncludingPrice());
        expenseDetail.setTaxExcludingPrice(line.getTaxExcludingPrice());
        expenseDetail.setTaxIncludingLineTotal(line.getTaxIncludingPrice());
        expenseDetail.setTaxExcludingLineTotal(line.getTaxExcludingPrice());
        
        return expenseDetail;
    }
    
    /**
     * 生成费用变动情况
     * 
     * @param addDate 新增日期
     * @param cancelDate 撤销日期
     * @return 费用变动情况描述
     */
    private String generateExpenseChangeInfo(Date addDate, Date cancelDate) {
        StringBuilder changeInfo = new StringBuilder();
        
        SimpleDateFormat yearMonthFormat = new SimpleDateFormat("yyyy年M月");
        
        // 如果有新增日期，添加新增信息
        if (addDate != null) {
            changeInfo.append("新增时间为").append(DateUtils.parseDateToStr("yyyy-MM-dd", addDate));
        }
        
        // 如果有撤销日期，添加撤销信息
        if (cancelDate != null) {
            if (changeInfo.length() > 0) {
                changeInfo.append("，");
            }
            changeInfo.append("撤销时间为").append(DateUtils.parseDateToStr("yyyy-MM-dd", cancelDate));
        }
        
        return changeInfo.toString();
    }
} 