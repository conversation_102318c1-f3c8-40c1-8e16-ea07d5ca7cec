package com.ruoyi.expense.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.expense.domain.BillReceiveDTO;
import com.ruoyi.expense.service.IExpenseBillService;

import javax.annotation.PostConstruct;

/**
 * 账单接收 控制器
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@RestController
@RequestMapping("/expense")
public class BillReceiveController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(BillReceiveController.class);
    
    @Autowired
    private IExpenseBillService expenseBillService;
    
    @PostConstruct
    public void init() {
        log.info("账单接收控制器已初始化");
    }
    
    /**
     * 接收账单数据
     */
    @PostMapping("/receive")
    public AjaxResult receiveBill(@RequestBody BillReceiveDTO receiveDTO)
    {
        log.info("=================== 接收到账单请求 ===================");
        log.info("请求路径: /expense/receive");
        log.info("请求方法: POST");
        log.info("请求数据: {}", receiveDTO);
        
        try {
            String result = expenseBillService.receiveBillData(receiveDTO);
            log.info("处理结果: {}", result);
            
            if (result.startsWith("数据入库成功")) {
                log.info("账单处理成功");
                return AjaxResult.success(result);
            } else {
                log.warn("账单处理失败: {}", result);
                return AjaxResult.error(result);
            }
        } catch (Exception e) {
            String errorMessage = "处理账单数据时发生错误：" + e.getMessage();
            log.error(errorMessage, e);
            return AjaxResult.error(errorMessage);
        } finally {
            log.info("===============================================");
        }
    }
} 