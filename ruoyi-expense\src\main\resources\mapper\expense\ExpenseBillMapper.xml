<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.expense.mapper.ExpenseBillMapper">
    
    <resultMap type="ExpenseBill" id="ExpenseBillResult">
        <result property="bill_id"    column="bill_id"    />
        <result property="expense_type"    column="expense_type"    />
        <result property="billing_cycle"    column="billing_cycle"    />
        <result property="transfer_department"    column="transfer_department"    />
        <result property="status"    column="status"    />
        <result property="bill_publisher"    column="bill_publisher"    />
        <result property="bill_confirmer"    column="bill_confirmer"    />
        <result property="bill_returner"    column="bill_returner"    />
        <result property="bill_refuse_comment"    column="bill_refuse_comment"    />
        <result property="total_price_with_tax"    column="total_price_with_tax"    />
        <result property="total_price_without_tax"    column="total_price_without_tax"    />
    </resultMap>

    <sql id="selectExpenseBillVo">
        select bill_id, expense_type, billing_cycle, transfer_department, status, bill_publisher, bill_confirmer, bill_returner, bill_refuse_comment, total_price_with_tax, total_price_without_tax from expense_bills
    </sql>

    <select id="selectExpenseBillList" parameterType="ExpenseBill" resultMap="ExpenseBillResult">
        <include refid="selectExpenseBillVo"/>
        <where>  
            <if test="expense_type != null  and expense_type != ''"> and expense_type = #{expense_type}</if>
            <if test="billing_cycle != null  and billing_cycle != ''"> and billing_cycle = #{billing_cycle}</if>
            <if test="transfer_department != null  and transfer_department != ''"> and transfer_department = #{transfer_department}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="bill_publisher != null  and bill_publisher != ''"> and bill_publisher = #{bill_publisher}</if>
            <if test="bill_confirmer != null  and bill_confirmer != ''"> and bill_confirmer = #{bill_confirmer}</if>
            <if test="bill_returner != null  and bill_returner != ''"> and bill_returner = #{bill_returner}</if>
        </where>
        ORDER BY 
        CASE status
            WHEN '0' THEN 1  -- 已入库
            WHEN '1' THEN 2  -- 已发布
            WHEN '3' THEN 3  -- 已退回
            WHEN '2' THEN 4  -- 已确认
            ELSE 5
        END ASC, 
        bill_id DESC
    </select>
    
    <select id="selectExpenseBillById" parameterType="Long" resultMap="ExpenseBillResult">
        <include refid="selectExpenseBillVo"/>
        where bill_id = #{bill_id}
    </select>
    
    <select id="selectExpenseBillByUniqueKey" resultMap="ExpenseBillResult">
        <include refid="selectExpenseBillVo"/>
        where expense_type = #{expense_type} and billing_cycle = #{billing_cycle} and transfer_department = #{transfer_department}
    </select>
        
    <insert id="insertExpenseBill" parameterType="ExpenseBill" useGeneratedKeys="true" keyProperty="bill_id">
        insert into expense_bills
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expense_type != null">expense_type,</if>
            <if test="billing_cycle != null">billing_cycle,</if>
            <if test="transfer_department != null">transfer_department,</if>
            <if test="status != null">status,</if>
            <if test="bill_publisher != null">bill_publisher,</if>
            <if test="bill_confirmer != null">bill_confirmer,</if>
            <if test="bill_returner != null">bill_returner,</if>
            <if test="bill_refuse_comment != null">bill_refuse_comment,</if>
            <if test="total_price_with_tax != null">total_price_with_tax,</if>
            <if test="total_price_without_tax != null">total_price_without_tax,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expense_type != null">#{expense_type},</if>
            <if test="billing_cycle != null">#{billing_cycle},</if>
            <if test="transfer_department != null">#{transfer_department},</if>
            <if test="status != null">#{status},</if>
            <if test="bill_publisher != null">#{bill_publisher},</if>
            <if test="bill_confirmer != null">#{bill_confirmer},</if>
            <if test="bill_returner != null">#{bill_returner},</if>
            <if test="bill_refuse_comment != null">#{bill_refuse_comment},</if>
            <if test="total_price_with_tax != null">#{total_price_with_tax},</if>
            <if test="total_price_without_tax != null">#{total_price_without_tax},</if>
         </trim>
    </insert>

    <update id="updateExpenseBill" parameterType="ExpenseBill">
        update expense_bills
        <trim prefix="SET" suffixOverrides=",">
            <if test="expense_type != null">expense_type = #{expense_type},</if>
            <if test="billing_cycle != null">billing_cycle = #{billing_cycle},</if>
            <if test="transfer_department != null">transfer_department = #{transfer_department},</if>
            <if test="status != null">status = #{status},</if>
            <if test="bill_publisher != null">bill_publisher = #{bill_publisher},</if>
            <if test="bill_confirmer != null">bill_confirmer = #{bill_confirmer},</if>
            <if test="bill_returner != null">bill_returner = #{bill_returner},</if>
            <if test="bill_refuse_comment != null">bill_refuse_comment = #{bill_refuse_comment},</if>
            <if test="total_price_with_tax != null">total_price_with_tax = #{total_price_with_tax},</if>
            <if test="total_price_without_tax != null">total_price_without_tax = #{total_price_without_tax},</if>
        </trim>
        where bill_id = #{bill_id}
    </update>

    <update id="clearReturnInfo" parameterType="Long">
        update expense_bills
        set bill_returner = null,
            bill_refuse_comment = null
        where bill_id = #{bill_id}
    </update>

    <delete id="deleteExpenseBillById" parameterType="Long">
        delete from expense_bills where bill_id = #{bill_id}
    </delete>

    <delete id="deleteExpenseBillByIds" parameterType="String">
        delete from expense_bills where bill_id in 
        <foreach item="bill_id" collection="array" open="(" separator="," close=")">
            #{bill_id}
        </foreach>
    </delete>
</mapper> 