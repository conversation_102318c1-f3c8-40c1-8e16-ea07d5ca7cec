<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.line.mapper.LineAlldataMapper">

    <resultMap type="LineAlldata" id="LineAlldataResult">
        <result property="lineCode"    column="line_code"    />
        <result property="lineNumber"    column="line_number"    />
        <result property="institution"    column="institution"    />
        <result property="operator"    column="operator"    />
        <result property="lineName"    column="line_name"    />
        <result property="lineType"    column="line_type"    />
        <result property="lineBandwidth"    column="line_bandwidth"    />
        <result property="lineStatus"    column="line_status"    />
        <result property="address"    column="address"    />
        <result property="taxIncludingPrice"    column="tax_including_price"    />
        <result property="taxExcludingPrice"    column="tax_excluding_price"    />
        <result property="oppositeContact"    column="opposite_contact"    />
        <result property="lineUsageEnvironment"    column="line_usage_environment"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="lineAddDate"    column="line_add_date"    />
        <result property="lineCancelDate"    column="line_cancel_date"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectLineAlldataVo">
        select line_code, line_number, institution, operator, line_name, line_type, line_bandwidth, line_status, address, tax_including_price, tax_excluding_price, opposite_contact, line_usage_environment, tax_rate, line_add_date, line_cancel_date, remark from line_alldata
    </sql>

    <select id="selectLineAlldataList" parameterType="LineAlldata" resultMap="LineAlldataResult">
        <include refid="selectLineAlldataVo"/>
        <where>
            <if test="keyword != null and keyword != ''">
                and (
                    line_number like concat('%', #{keyword}, '%')
                    or line_name like concat('%', #{keyword}, '%')
                    or address like concat('%', #{keyword}, '%')
                    or opposite_contact like concat('%', #{keyword}, '%')
                    or line_bandwidth like concat('%', #{keyword}, '%')
                    or line_usage_environment like concat('%', #{keyword}, '%')
                )
            </if>
            <if test="lineNumber != null  and lineNumber != ''"> and line_number like concat('%', #{lineNumber}, '%')</if>
            <if test="institution != null  and institution != ''"> and institution like concat('%', #{institution}, '%')</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
            <if test="lineName != null  and lineName != ''"> and line_name like concat('%', #{lineName}, '%')</if>
            <if test="lineType != null  and lineType != ''"> and line_type = #{lineType}</if>
            <if test="lineBandwidth != null  and lineBandwidth != ''"> and line_bandwidth like concat('%', #{lineBandwidth}, '%')</if>
            <if test="lineStatus != null  and lineStatus != ''"> and line_status = #{lineStatus}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="taxIncludingPrice != null "> and tax_including_price = #{taxIncludingPrice}</if>
            <if test="taxExcludingPrice != null "> and tax_excluding_price = #{taxExcludingPrice}</if>
            <if test="oppositeContact != null  and oppositeContact != ''"> and opposite_contact like concat('%', #{oppositeContact}, '%')</if>
            <if test="lineUsageEnvironment != null  and lineUsageEnvironment != ''"> and line_usage_environment like concat('%', #{lineUsageEnvironment}, '%')</if>
            <if test="taxRate != null "> and tax_rate = #{taxRate}</if>
            <if test="lineAddDate != null "> and line_add_date = #{lineAddDate}</if>
            <if test="lineCancelDate != null "> and line_cancel_date = #{lineCancelDate}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>
    <select id="selectLineAlldataById" parameterType="Long" resultMap="LineAlldataResult">
        <include refid="selectLineAlldataVo"/>
        where line_code = #{lineCode}
    </select>

    <insert id="insertLineAlldata" parameterType="LineAlldata">
<!--        <selectKey keyProperty="lineCode" resultType="long" order="BEFORE">-->
<!--            SELECT seq_line_alldata.NEXTVAL as lineCode FROM DUAL-->
<!--        </selectKey>-->
        insert into line_alldata
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lineCode != null">line_code,</if>
            <if test="lineNumber != null">line_number,</if>
            <if test="institution != null and institution != ''">institution,</if>
            <if test="operator != null and operator != ''">operator,</if>
            <if test="lineName != null and lineName != ''">line_name,</if>
            <if test="lineType != null and lineType != ''">line_type,</if>
            <if test="lineBandwidth != null">line_bandwidth,</if>
            <if test="lineStatus != null">line_status,</if>
            <if test="address != null">address,</if>
            <if test="taxIncludingPrice != null">tax_including_price,</if>
            <if test="taxExcludingPrice != null">tax_excluding_price,</if>
            <if test="oppositeContact != null">opposite_contact,</if>
            <if test="lineUsageEnvironment != null">line_usage_environment,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="lineAddDate != null">line_add_date,</if>
            <if test="lineCancelDate != null">line_cancel_date,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lineCode != null">#{lineCode},</if>
            <if test="lineNumber != null">#{lineNumber},</if>
            <if test="institution != null and institution != ''">#{institution},</if>
            <if test="operator != null and operator != ''">#{operator},</if>
            <if test="lineName != null and lineName != ''">#{lineName},</if>
            <if test="lineType != null and lineType != ''">#{lineType},</if>
            <if test="lineBandwidth != null">#{lineBandwidth},</if>
            <if test="lineStatus != null">#{lineStatus},</if>
            <if test="address != null">#{address},</if>
            <if test="taxIncludingPrice != null">#{taxIncludingPrice},</if>
            <if test="taxExcludingPrice != null">#{taxExcludingPrice},</if>
            <if test="oppositeContact != null">#{oppositeContact},</if>
            <if test="lineUsageEnvironment != null">#{lineUsageEnvironment},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="lineAddDate != null">#{lineAddDate},</if>
            <if test="lineCancelDate != null">#{lineCancelDate},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <update id="updateLineAlldata" parameterType="LineAlldata">
        update line_alldata
        <trim prefix="SET" suffixOverrides=",">
            <if test="lineNumber != null">line_number = #{lineNumber},</if>
            <if test="institution != null and institution != ''">institution = #{institution},</if>
            <if test="operator != null and operator != ''">operator = #{operator},</if>
            <if test="lineName != null and lineName != ''">line_name = #{lineName},</if>
            <if test="lineType != null and lineType != ''">line_type = #{lineType},</if>
            <if test="lineBandwidth != null">line_bandwidth = #{lineBandwidth},</if>
            <if test="lineStatus != null">line_status = #{lineStatus},</if>
            <if test="address != null">address = #{address},</if>
            <if test="taxIncludingPrice != null">tax_including_price = #{taxIncludingPrice},</if>
            <if test="taxExcludingPrice != null">tax_excluding_price = #{taxExcludingPrice},</if>
            <if test="oppositeContact != null">opposite_contact = #{oppositeContact},</if>
            <if test="lineUsageEnvironment != null">line_usage_environment = #{lineUsageEnvironment},</if>
            <if test="taxRate != null">tax_rate = #{taxRate},</if>
            <if test="lineAddDate != null">line_add_date = #{lineAddDate},</if>
            <if test="lineCancelDate != null">line_cancel_date = #{lineCancelDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where line_code = #{lineCode}
    </update>

    <delete id="deleteLineAlldataById" parameterType="Long">
        delete from line_alldata where line_code = #{lineCode}
    </delete>

    <delete id="deleteLineAlldataByIds" parameterType="String">
        delete from line_alldata where line_code in
        <foreach item="lineCode" collection="array" open="(" separator="," close=")">
            #{lineCode}
        </foreach>
    </delete>
</mapper>