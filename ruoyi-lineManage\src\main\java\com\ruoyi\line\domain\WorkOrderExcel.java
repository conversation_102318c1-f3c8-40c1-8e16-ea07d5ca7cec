package com.ruoyi.line.domain;

import com.ruoyi.common.annotation.Excel;

/**
 * 工单Excel导出对象
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public class WorkOrderExcel {
    
    /** 线路编号 */
    @Excel(name = "线路编号", width = 20)
    private String lineNumber;
    
    /** 名称 */
    @Excel(name = "名称", width = 30)
    private String name;
    
    /** 类型 */
    @Excel(name = "类型", width = 20)
    private String type;
    
    /** 地址及联系人 */
    @Excel(name = "地址及联系人", width = 50)
    private String addressAndContact;

    public String getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(String lineNumber) {
        this.lineNumber = lineNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAddressAndContact() {
        return addressAndContact;
    }

    public void setAddressAndContact(String addressAndContact) {
        this.addressAndContact = addressAndContact;
    }
} 