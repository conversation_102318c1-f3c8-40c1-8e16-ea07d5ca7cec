package com.ruoyi.line.service;

import com.ruoyi.line.domain.BillPushDTO;
import com.ruoyi.line.domain.BillPushResult;

/**
 * 账单推送Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-20
 */
public interface IBillPushService {
    
    /**
     * 根据机构ID列表查询账单数据并推送
     * 
     * @param institutionIds 机构ID列表，多个ID用逗号分隔
     * @param publisher 账单发布人
     * @param billingCycle 计费周期
     * @return 详细的推送结果，包括有账单和无账单的机构信息
     */
    BillPushResult pushBillData(String institutionIds, String publisher, String billingCycle);
    
    /**
     * 构建账单推送数据
     * 
     * @param institutionIds 机构ID列表，多个ID用逗号分隔
     * @param publisher 账单发布人
     * @param billingCycle 计费周期
     * @return 账单推送数据对象
     */
    BillPushDTO buildBillPushData(String institutionIds, String publisher, String billingCycle);
} 