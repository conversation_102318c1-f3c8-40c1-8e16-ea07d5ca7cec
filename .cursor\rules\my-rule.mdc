---
description: 所有的
globs: 
alwaysApply: false
---
- 项目的整体框架

  - ruoyi-expense：电子划账模块，主要用于接收其他数据模块的账单相关数据、围绕账单的审核进行一系列的CRUD类操作

  - ruoyi-lineManage：线路数据管理模块，也属于一种数据模块。主要用于线路数据的管理、线路的账单数据的推送（数据将推送至ruoyi-expense模块）

  - 其他模块均为若依框架（前后端不分离版本）自带的模块，不做额外说明。

- ruoyi-expense模块关联着3张数据表，下面进行说明

  1.费用明细数据表

  - 字段明细

    - 主键ID：自增

    - 费用名称

    - 编号

    - 品牌

    - 具体规格

    - 费用类型

    - 计费周期

      - 每月：例如202501

      - 其他周期：例如202501-06

    - 划账部门或支行

    - 数量

    - 费用变动情况

    - 备注

    - 含税单价

    - 不含税单价

    - 含税单行总价

    - 不含税单行总价
  - 其中以下字段为必填项（不能为空）：主键ID、费用名称、品牌、具体规格、费用类型、计费周期、划账部门或支行、数量、含税单价、不含税单位、含税单行总价、不含税单行总价
  - 除了必填项之外的其他字段值可以为空，但是不能没有这些字段。  

 1. 账单数据表

  - 字段明细

    - 账单编号：主键，自增

    - 费用类型

    - 计费周期

    - 划账部门或支行

    - 账单状态

      - 待核对、已退回、已确认

    - 账单发布人：

    - 账单确认人

    - 账单退回人

    - 账单退回意见

    - 账单含税总价

    - 账单不含税总价 
3. 各部门核对人信息表
- 编号：主键，自增
- 姓名
- EHR号
- 部门名称
- 备注

- 账单的唯一性与冲突：
	- 一份账单的唯一性由计费周期、部门、费用类型共同确定，同一份账单只能存在一份。
	- 只有当同一份账单处于已退回的状态下，账单上传人才能重新上传账单。其余情况均视为冲突，电子划账系统拒绝接收该份账单。
		- 如果想要重新推送某个部门的账单，只能让核对人员退回或者管理员召回后，才能进行
# 账单的校验顺序说明
		1. 账单数量是否符合
		2. 遍历每一份账单
			1. 校验当前账单中的JSON结构是否与BillReceiveDTO的结构一致
			2. 校验费用类型是否为EXPENSE_TYPE_DICT字典中的值
			3. 校验计费周期是否为类似这种日期格式：202503、202504-06
			4. 校验划账部门是否为expense_institude字典中的值
			5. 校验账单的唯一性是否冲突
			6. 校对账单费用：累加每一条费用明细的金额，与账单数据中的总价比较
		3. 所有账单校验通过后，开始将本次的数据入库。如果有一份账单有误，本次的所有数据都不入库 

# 账单状态
	- 0:已入库
		- 上传人第一次上传账单，账单变成已入库状态。
		- 该种状态下，上传人可以选择撤销自己发布的账单（即同时删除费用明细数据表和账单数据表中的相关数据）
	- 1:已发布
		- 情况1：上传人第一次上传账单，需要等待管理员发布账单后，才会进入已发布状态
		- 情况2：上传人在账单已被退回的状态下，重新上传一份账单后，这份账单直接进入已发布状态，无需发布人重新发布
	- 2:已确认：部门核对人对账单的信息核对无误后，点击确认后变成的状态
	- 3:已退回：部门核对人选择退回  
