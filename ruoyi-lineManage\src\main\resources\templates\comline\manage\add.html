<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增通讯线路管理')" />
    <th:block th:include="include :: datetimepicker-css" />
    <!-- 添加自定义样式 -->
    <style>
        .form-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .form-group label {
            text-align: right;
            padding-right: 15px;
            font-weight: bold;
        }

        .form-control {
            border-radius: 4px;
            border: 1px solid #ccc;
            padding: 6px 12px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-control:focus {
            border-color: #66afe9;
            outline: 0;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
        }

        .input-group-addon {
            border-radius: 0 4px 4px 0;
            background-color: #f5f5f5;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content" style="max-width: 800px; margin: 0 auto;">
        <form class="form-horizontal m" id="form-manage-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路编号：</label>
                <div class="col-sm-8">
                    <input name="lineNumber" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">所属机构：</label>
                <div class="col-sm-8">
                    <select name="institution" class="form-control m-b" th:with="type=${@dict.getType('line_institude')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">运营商：</label>
                <div class="col-sm-8">
                    <select name="operator" class="form-control m-b" th:with="type=${@dict.getType('line_operator')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">线路名称：</label>
                <div class="col-sm-8">
                    <input name="lineName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">线路类型：</label>
                <div class="col-sm-8">
                    <select name="lineType" class="form-control m-b" th:with="type=${@dict.getType('lineType')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路带宽：</label>
                <div class="col-sm-8">
                    <input name="lineBandwidth" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路状态：</label>
                <div class="col-sm-8">
                    <select name="lineStatus" class="form-control m-b" th:with="type=${@dict.getType('lineStatus')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">地址：</label>
                <div class="col-sm-8">
                    <input name="address" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">含税价：</label>
                <div class="col-sm-8">
                    <input name="taxIncludingPrice" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">不含税价：</label>
                <div class="col-sm-8">
                    <input name="taxExcludingPrice" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">对端联系人：</label>
                <div class="col-sm-8">
                    <input name="oppositeContact" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路使用环境：</label>
                <div class="col-sm-8">
                    <input name="lineUsageEnvironment" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">税率：</label>
                <div class="col-sm-8">
                    <input name="taxRate" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路新增日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="lineAddDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">线路撤销日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="lineCancelDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "comline/manage"
        
        // 添加自定义验证方法
        $.validator.addMethod("maxDecimalPlaces", function(value, element, param) {
            if (value === "") return true; // 空值通过验证
            var regex = new RegExp("^\\d+(\\.\\d{1," + param + "})?$");
            return regex.test(value);
        }, "最多只能有{0}位小数");
        
        $("#form-manage-add").validate({
            focusCleanup: true,
            rules: {
                taxIncludingPrice: {
                    number: true,
                    maxDecimalPlaces: 2
                },
                taxExcludingPrice: {
                    number: true,
                    maxDecimalPlaces: 2
                }
            },
            messages: {
                taxIncludingPrice: {
                    maxDecimalPlaces: "含税价格最多只能有2位小数"
                },
                taxExcludingPrice: {
                    maxDecimalPlaces: "不含税价格最多只能有2位小数"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-manage-add').serialize());
            }
        }

        $("input[name='lineAddDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='lineCancelDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>