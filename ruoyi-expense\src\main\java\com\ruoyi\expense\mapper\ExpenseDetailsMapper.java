package com.ruoyi.expense.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.expense.domain.ExpenseDetails;

/**
 * 费用明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface ExpenseDetailsMapper 
{
    /**
     * 查询费用明细
     * 
     * @param id 费用明细主键
     * @return 费用明细
     */
    public ExpenseDetails selectExpenseDetailsById(Long id);

    /**
     * 查询费用明细列表
     * 
     * @param expenseDetails 费用明细
     * @return 费用明细集合
     */
    public List<ExpenseDetails> selectExpenseDetailsList(ExpenseDetails expenseDetails);

    /**
     * 新增费用明细
     * 
     * @param expenseDetails 费用明细
     * @return 结果
     */
    public int insertExpenseDetails(ExpenseDetails expenseDetails);

    /**
     * 修改费用明细
     * 
     * @param expenseDetails 费用明细
     * @return 结果
     */
    public int updateExpenseDetails(ExpenseDetails expenseDetails);

    /**
     * 删除费用明细
     * 
     * @param id 费用明细主键
     * @return 结果
     */
    public int deleteExpenseDetailsById(Long id);

    /**
     * 批量删除费用明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExpenseDetailsByIds(Long[] ids);

    /**
     * 根据账单ID删除费用明细
     * 
     * @param billId 账单ID
     * @return 结果
     */
    public int deleteExpenseDetailsByBillId(Long billId);

    /**
     * 根据费用类型、计费周期和划账部门删除费用明细
     * 
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param transferDepartment 划账部门
     * @return 结果
     */
    public int deleteExpenseDetailsByUnique(
        @Param("expenseType") String expenseType, 
        @Param("billingCycle") String billingCycle, 
        @Param("transferDepartment") String transferDepartment);
}
