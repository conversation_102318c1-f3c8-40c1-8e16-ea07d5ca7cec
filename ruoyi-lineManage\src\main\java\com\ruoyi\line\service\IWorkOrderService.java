package com.ruoyi.line.service;

import com.ruoyi.line.domain.WorkOrderDTO;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 工单生成Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IWorkOrderService {
    
    /**
     * 生成运营商工单
     * 
     * @param workOrderDTO 工单数据传输对象
     * @return 操作结果，包含生成的Excel文件信息
     */
    AjaxResult generateWorkOrder(WorkOrderDTO workOrderDTO);
    
    /**
     * 验证工单数据
     * 
     * @param workOrderDTO 工单数据传输对象
     * @return 验证结果
     */
    AjaxResult validateWorkOrderData(WorkOrderDTO workOrderDTO);
} 