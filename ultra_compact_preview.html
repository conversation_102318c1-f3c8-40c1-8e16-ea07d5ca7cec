<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分摊表基本信息 - 极度紧凑版</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
        }

        .comparison-container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .comparison-section {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            text-align: center;
            margin-bottom: 15px;
            padding: 8px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }

        .before-title {
            background: #ffebee;
            color: #c62828;
        }

        .after-title {
            background: #e8f5e8;
            color: #2e7d32;
        }

        /* 之前的紧凑样式 */
        .previous .allocation-detail-container {
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .previous .allocation-detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 18px;
            margin: 0;
            border-bottom: 2px solid #5a67d8;
        }

        .previous .allocation-detail-header h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .previous .allocation-detail-content {
            padding: 15px;
            background: #fafbfc;
        }

        .previous .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 12px;
            background: white;
            border-radius: 6px;
            padding: 12px 15px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            border-left: 3px solid #667eea;
        }

        .previous .info-item {
            flex: 1;
            min-width: 200px;
            margin-bottom: 8px;
            padding-right: 15px;
        }

        .previous .info-label {
            font-size: 13px;
            margin-bottom: 4px;
        }

        .previous .info-value {
            font-size: 14px;
            padding: 6px 10px;
            min-height: 32px;
        }

        /* 极度紧凑样式 */
        .ultra .allocation-detail-container {
            background: #ffffff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .ultra .allocation-detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 12px;
            margin: 0;
            border-bottom: 1px solid #5a67d8;
        }

        .ultra .allocation-detail-header h5 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .ultra .allocation-detail-header h5 i {
            margin-right: 6px;
            font-size: 12px;
        }

        .ultra .allocation-detail-content {
            padding: 8px;
            background: #fafbfc;
        }

        .ultra .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 6px;
            background: white;
            border-radius: 3px;
            padding: 6px 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
            border-left: 2px solid #667eea;
        }

        .ultra .info-row:last-child {
            margin-bottom: 0;
        }

        .ultra .info-item {
            flex: 1;
            min-width: 150px;
            margin-bottom: 4px;
            padding-right: 8px;
        }

        .ultra .info-item:last-child {
            padding-right: 0;
        }

        .ultra .info-item.half-width {
            flex: 1 1 50%;
            min-width: 200px;
        }

        .ultra .info-item.third-width {
            flex: 1 1 33.333%;
            min-width: 140px;
        }

        .ultra .info-label {
            display: inline-block;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 2px;
            font-size: 12px;
            min-width: 60px;
            position: relative;
        }

        .ultra .info-label::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 18px;
            height: 1px;
            background: #667eea;
            border-radius: 1px;
        }

        .ultra .info-value {
            display: flex;
            align-items: center;
            color: #2d3748;
            font-size: 13px;
            line-height: 1.3;
            padding: 4px 6px;
            background: #f7fafc;
            border-radius: 3px;
            border: 1px solid #e2e8f0;
            min-height: 24px;
        }

        .ultra .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            color: #d63031;
            border: 1px solid #fdcb6e;
        }

        .ultra .info-icon {
            margin-right: 4px;
            color: #667eea;
            font-size: 11px;
        }

        .ultra .basic-info .info-value {
            background: #f3e5f5;
            border-color: #9c27b0;
            color: #6a1b9a;
        }

        .ultra .person-info .info-value {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1565c0;
        }

        .ultra .time-info .info-value {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
            font-family: 'Courier New', monospace;
        }

        .ultra .comments-section {
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-left: 2px solid #ff9800;
            border-radius: 3px;
            padding: 6px 8px;
            margin-top: 6px;
        }

        .ultra .comments-section .info-label {
            color: #e65100;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .ultra .comments-section .info-value {
            background: #ffffff;
            border: 1px solid #ffcc02;
            color: #bf360c;
            font-style: italic;
            min-height: auto;
            padding: 4px 6px;
            line-height: 1.4;
        }

        @media (max-width: 1200px) {
            .comparison-container {
                flex-direction: column;
            }
        }

        .stats {
            margin-top: 10px;
            padding: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; margin-bottom: 30px; color: #333;">分摊表基本信息 - 极度紧凑优化</h1>
    
    <div class="comparison-container">
        <!-- 之前的紧凑版 -->
        <div class="comparison-section previous">
            <div class="section-title before-title">之前版本 - 紧凑样式</div>
            
            <div class="allocation-detail-container">
                <div class="allocation-detail-header">
                    <h5><i class="fa fa-info-circle"></i>分摊表基本信息</h5>
                </div>
                <div class="allocation-detail-content">
                    <div class="info-row basic-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-file-text info-icon"></i>分摊表名称</span>
                            <span class="info-value">202506-电子设备配件-分摊表</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-flag info-icon"></i>状态</span>
                            <span class="info-value">
                                <span class="status-badge">审核中</span>
                            </span>
                        </div>
                    </div>
                    <div class="info-row basic-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-tags info-icon"></i>费用类型</span>
                            <span class="info-value">电子设备配件</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-calendar info-icon"></i>计费周期</span>
                            <span class="info-value">202506</span>
                        </div>
                    </div>
                    <div class="info-row person-info">
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user info-icon"></i>制表人</span>
                            <span class="info-value">王海东</span>
                        </div>
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user-check info-icon"></i>复核人</span>
                            <span class="info-value">若依</span>
                        </div>
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user-cog info-icon"></i>负责人</span>
                            <span class="info-value"></span>
                        </div>
                    </div>
                    <div class="info-row time-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-clock-o info-icon"></i>创建时间</span>
                            <span class="info-value">2025.08.01 17:05:00</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-check-circle info-icon"></i>确认时间</span>
                            <span class="info-value">未确认</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="stats">高度: ~180px | 间距: 中等</div>
        </div>

        <!-- 极度紧凑版 -->
        <div class="comparison-section ultra">
            <div class="section-title after-title">极度紧凑版 - 最小化空间</div>
            
            <div class="allocation-detail-container">
                <div class="allocation-detail-header">
                    <h5><i class="fa fa-info-circle"></i>分摊表基本信息</h5>
                </div>
                <div class="allocation-detail-content">
                    <div class="info-row basic-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-file-text info-icon"></i>分摊表名称</span>
                            <span class="info-value">202506-电子设备配件-分摊表</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-flag info-icon"></i>状态</span>
                            <span class="info-value">
                                <span class="status-badge">审核中</span>
                            </span>
                        </div>
                    </div>
                    <div class="info-row basic-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-tags info-icon"></i>费用类型</span>
                            <span class="info-value">电子设备配件</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-calendar info-icon"></i>计费周期</span>
                            <span class="info-value">202506</span>
                        </div>
                    </div>
                    <div class="info-row person-info">
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user info-icon"></i>制表人</span>
                            <span class="info-value">王海东</span>
                        </div>
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user-check info-icon"></i>复核人</span>
                            <span class="info-value">若依</span>
                        </div>
                        <div class="info-item third-width">
                            <span class="info-label"><i class="fa fa-user-cog info-icon"></i>负责人</span>
                            <span class="info-value"></span>
                        </div>
                    </div>
                    <div class="info-row time-info">
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-clock-o info-icon"></i>创建时间</span>
                            <span class="info-value">2025.08.01 17:05:00</span>
                        </div>
                        <div class="info-item half-width">
                            <span class="info-label"><i class="fa fa-check-circle info-icon"></i>确认时间</span>
                            <span class="info-value">未确认</span>
                        </div>
                    </div>
                    <div class="comments-section">
                        <div class="info-item full-width">
                            <span class="info-label"><i class="fa fa-comment info-icon"></i>修改意见</span>
                            <span class="info-value">请核实部分账单的分摊部门信息，确保准确性。</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="stats">高度: ~130px | 空间节省: ~28%</div>
        </div>
    </div>
</body>
</html>
