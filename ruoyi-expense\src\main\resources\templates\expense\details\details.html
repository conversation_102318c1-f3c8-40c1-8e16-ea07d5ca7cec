<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('费用明细列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>名称：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>编号：</label>
                                <input type="text" name="number"/>
                            </li>
                            <li>
                                <label>品牌：</label>
                                <input type="text" name="brand"/>
                            </li>
                            <li>
                                <label>具体规格：</label>
                                <input type="text" name="specificSpecification"/>
                            </li>
                            <li>
                                <label>费用类型：</label>
                                <select name="expenseType">
                                    <option value="">所有</option>
                                    <option value="-1">代码生成请选择字典属性</option>
                                </select>
                            </li>
                            <li>
                                <label>计费周期：</label>
                                <input type="text" name="billingCycle"/>
                            </li>
                            <li>
                                <label>划账部门或支行：</label>
                                <input type="text" name="transferDepartment"/>
                            </li>
                            <li>
                                <label>数量：</label>
                                <input type="text" name="quantity"/>
                            </li>
                            <li>
                                <label>费用变动情况：</label>
                                <select name="expenseChangeStatus">
                                    <option value="">所有</option>
                                    <option value="-1">代码生成请选择字典属性</option>
                                </select>
                            </li>
                            <li>
                                <label>含税单价：</label>
                                <input type="text" name="unitPriceIncludingTax"/>
                            </li>
                            <li>
                                <label>不含税单价：</label>
                                <input type="text" name="unitPriceExcludingTax"/>
                            </li>
                            <li>
                                <label>含税单行总价：</label>
                                <input type="text" name="totalLinePriceIncludingTax"/>
                            </li>
                            <li>
                                <label>不含税单行总价：</label>
                                <input type="text" name="totalLinePriceExcludingTax"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="expense:details:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="expense:details:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="expense:details:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="expense:details:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('expense:details:edit')}]];
        var removeFlag = [[${@permission.hasPermi('expense:details:remove')}]];
        var prefix = ctx + "expense/details";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "费用明细",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键ID',
                    visible: false
                },
                {
                    field: 'name',
                    title: '名称'
                },
                {
                    field: 'number',
                    title: '编号'
                },
                {
                    field: 'brand',
                    title: '品牌'
                },
                {
                    field: 'specificSpecification',
                    title: '具体规格'
                },
                {
                    field: 'expenseType',
                    title: '费用类型'
                },
                {
                    field: 'billingCycle',
                    title: '计费周期'
                },
                {
                    field: 'transferDepartment',
                    title: '划账部门或支行'
                },
                {
                    field: 'quantity',
                    title: '数量'
                },
                {
                    field: 'expenseChangeStatus',
                    title: '费用变动情况'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    field: 'unitPriceIncludingTax',
                    title: '含税单价'
                },
                {
                    field: 'unitPriceExcludingTax',
                    title: '不含税单价'
                },
                {
                    field: 'totalLinePriceIncludingTax',
                    title: '含税单行总价'
                },
                {
                    field: 'totalLinePriceExcludingTax',
                    title: '不含税单行总价'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>