<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('编辑核对人')" />
    <style>
        .container-div {
            padding-bottom: 0 !important;
            margin-bottom: 0 !important;
            height: auto !important;
        }
        .form-horizontal {
            margin-bottom: 0 !important;
        }
        .form-group {
            margin-bottom: 10px !important;
        }
        body {
            padding-bottom: 0 !important;
            overflow: hidden;
        }
    </style>
</head>
<body class="white-bg">
    <div class="container-div">
        <form id="form-edit-checker-manage" class="form-horizontal">
            <input type="hidden" name="id" th:value="${checkerManage.id}" />
            <div class="form-group">
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="col-sm-8">
                    <input type="text" name="name" class="form-control" th:value="${checkerManage.name}" readonly style="background-color: #f5f5f5;" />
                    <small class="text-muted">姓名不可修改</small>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">EHR号：</label>
                <div class="col-sm-8">
                    <input type="text" name="ehrNumber" class="form-control" th:value="${checkerManage.ehrNumber}" readonly style="background-color: #f5f5f5;" />
                    <small class="text-muted">EHR号不可修改</small>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">部门名称：</label>
                <div class="col-sm-8">
                    <select name="departmentName" class="form-control" required>
                        <option value="">请选择部门</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input type="text" name="remarks" class="form-control" th:value="${checkerManage.remarks}" />
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        // expense_institude字典列表
        var expenseInstitudeList = [];
        var currentDepartment = /*[[${checkerManage.departmentName}]]*/ '';
        
        $(function() {
            // 获取expense_institude字典
            console.log("开始获取字典数据...");
            $.ajax({
                url: ctx + "system/dict/data/list",
                type: "post",
                data: {
                    dictType: "expense_institude"
                },
                success: function(res) {
                    console.log("字典请求成功，响应数据：", res);
                    if(res.code === 0 && res.rows && res.rows.length > 0) {
                        expenseInstitudeList = res.rows.map(function(item){ return item.dictLabel; });
                        console.log("获取到的字典列表：", expenseInstitudeList);
                        
                        // 填充部门下拉框
                        var departmentSelect = $("select[name='departmentName']");
                        departmentSelect.empty();
                        departmentSelect.append('<option value="">请选择部门</option>');
                        
                        $.each(res.rows, function(index, item) {
                            var selected = item.dictLabel === currentDepartment ? 'selected' : '';
                            departmentSelect.append('<option value="' + item.dictLabel + '" ' + selected + '>' + item.dictLabel + '</option>');
                        });
                        console.log("下拉框选项填充完成，共 " + res.rows.length + " 个选项");
                    } else {
                        console.warn("字典数据为空");
                        var departmentSelect = $("select[name='departmentName']");
                        departmentSelect.append('<option value="">暂无部门数据</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("获取字典数据失败：", error);
                    var departmentSelect = $("select[name='departmentName']");
                    departmentSelect.append('<option value="">数据加载失败</option>');
                }
            });
        });
        
        // 为适配RuoYi框架，添加submitHandler函数
        function submitHandler() {
            return submitEditCheckerManage();
        }
        
        function submitEditCheckerManage() {
            // 前端校验
            var departmentName = $("select[name='departmentName']").val();
            if (!departmentName) {
                $.modal.msgError("请选择部门");
                return false;
            }
            
            var arr = $("#form-edit-checker-manage").serializeArray();
            var data = {};
            $.each(arr, function() {
                data[this.name] = this.value;
            });
            
            $.ajax({
                url: ctx + "expense/checker_manage/edit",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(data),
                success: function(res) {
                    if(res.code === 0) {
                        $.modal.msgSuccess("更新成功");
                        
                        // 先刷新父页面表格
                        console.log("开始刷新父页面表格...");
                        if (parent && parent.$.table && parent.$.table.refresh) {
                            console.log("表格刷新成功");
                            parent.$.table.refresh();
                        } else {
                            console.log("无法刷新表格，parent.$.table.refresh不存在");
                        }
                        
                        // 延迟关闭弹出框
                        setTimeout(function() {
                            console.log("开始关闭弹窗...");
                            
                            // 使用layer.closeAll()关闭所有弹窗
                            if (parent && parent.layer) {
                                try {
                                    parent.layer.closeAll();
                                    console.log("✅ 弹窗关闭成功");
                                } catch (e) {
                                    console.error("❌ 弹窗关闭失败:", e);
                                    // 兜底方案：刷新父页面
                                    if (parent && parent.location) {
                                        parent.location.reload();
                                    }
                                }
                            } else {
                                console.warn("⚠️ parent.layer不存在，使用刷新页面");
                                if (parent && parent.location) {
                                    parent.location.reload();
                                }
                            }
                        }, 100);
                        
                        return true;
                    } else {
                        $.modal.msgError(res.msg || "更新失败");
                        return false;
                    }
                },
                error: function() {
                    $.modal.msgError("系统错误");
                    return false;
                }
            });
            return false; // 阻止默认提交
        }
    </script>
</body>
</html> 