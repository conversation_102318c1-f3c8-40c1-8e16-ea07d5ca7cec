<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运营商工单生成')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12">
            <!-- 工单类型选择 -->
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">选择工单类型</h3>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">工单操作类型：</label>
                        <div class="col-sm-9">
                            <label class="radio-inline">
                                <input type="radio" name="operationType" value="1" onclick="showOperationForm('1')"> 新增线路
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="operationType" value="2" onclick="showOperationForm('2')"> 撤销
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="operationType" value="3" onclick="showOperationForm('3')"> 移机
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="operationType" value="4" onclick="showOperationForm('4')"> 扩容
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="operationType" value="5" onclick="showOperationForm('5')"> 停用
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增线路表单 -->
            <div id="newLineForm" class="panel panel-info" style="display: none;">
                <div class="panel-heading">
                    <h3 class="panel-title">新增线路信息</h3>
                </div>
                <div class="panel-body">
                    <form id="newLineFormData" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">运营商：</label>
                            <div class="col-sm-9">
                                <select id="newOperator" name="operator" class="form-control" th:with="type=${@dict.getType('line_operator')}">
                                    <option value="">请选择运营商</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">带宽：</label>
                            <div class="col-sm-9">
                                <input id="newBandwidth" name="bandwidth" type="text" class="form-control" placeholder="请输入带宽信息（如：100M）">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">地址：</label>
                            <div class="col-sm-9">
                                <input id="newAddress" name="address" type="text" class="form-control" placeholder="请输入地址信息">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系人：</label>
                            <div class="col-sm-9">
                                <input id="newContact" name="contact" type="text" class="form-control" placeholder="请输入联系人信息">
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 选择线路表单 -->
            <div id="selectLinesForm" class="panel panel-info" style="display: none;">
                <div class="panel-heading">
                    <h3 class="panel-title">选择操作线路</h3>
                </div>
                <div class="panel-body">
                    <!-- 搜索条件 -->
                    <div class="form-group">
                        <div class="col-sm-3">
                            <input type="text" id="searchKeyword" class="form-control" placeholder="关键词（支持模糊搜索线路编号、名称、地址、联系人等）" onkeypress="if(event.keyCode==13) searchLines();">
                        </div>
                        <div class="col-sm-3">
                            <select id="searchOperator" class="form-control" th:with="type=${@dict.getType('line_operator')}">
                                <option value="">所有运营商</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <select id="searchInstitution" class="form-control" th:with="type=${@dict.getType('line_institude')}">
                                <option value="">所有机构</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <button type="button" class="btn btn-primary" onclick="searchLines()">
                                <i class="fa fa-search"></i> 搜索
                            </button>
                            <button type="button" class="btn btn-default" onclick="clearSearch()">
                                <i class="fa fa-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                    
                    <!-- 线路列表 -->
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table id="linesTable" class="table table-bordered table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th style="width: 40px;">
                                                <input type="checkbox" id="selectAllLines" onchange="toggleAllLines(this)">
                                            </th>
                                            <th>线路编号</th>
                                            <th>线路名称</th>
                                            <th>运营商</th>
                                            <th>地址</th>
                                            <th>联系人</th>
                                        </tr>
                                    </thead>
                                    <tbody id="linesTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center">请点击搜索查看线路列表</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 已选择的线路汇总 -->
                    <div class="form-group">
                        <label class="col-sm-3 control-label">已选择的线路：</label>
                        <div class="col-sm-9">
                            <div id="selectedLinesInfo" class="well">
                                <p>尚未选择线路</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 移机附加信息 -->
            <div id="relocateForm" class="panel panel-warning" style="display: none;">
                <div class="panel-heading">
                    <h3 class="panel-title">移机信息</h3>
                </div>
                <div class="panel-body">
                    <form id="relocateFormData" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">新地址：</label>
                            <div class="col-sm-9">
                                <input id="newAddressRelocate" name="newAddress" type="text" class="form-control" placeholder="请输入移机后的新地址">
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 扩容附加信息 -->
            <div id="expandForm" class="panel panel-warning" style="display: none;">
                <div class="panel-heading">
                    <h3 class="panel-title">扩容信息</h3>
                </div>
                <div class="panel-body">
                    <form id="expandFormData" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">新带宽：</label>
                            <div class="col-sm-9">
                                <input id="newBandwidthExpand" name="bandwidth" type="text" class="form-control" placeholder="请输入扩容后的带宽（如：200M）">
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 停用附加信息 -->
            <div id="disableForm" class="panel panel-danger" style="display: none;">
                <div class="panel-heading">
                    <h3 class="panel-title">停用信息</h3>
                </div>
                <div class="panel-body">
                    <form id="disableFormData" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">停用备注：</label>
                            <div class="col-sm-9">
                                <textarea id="disableRemarks" name="remarks" class="form-control" rows="3" placeholder="请输入停用原因或相关说明"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12 text-center">
                            <button type="button" class="btn btn-success btn-lg" onclick="generateWorkOrder()" disabled id="generateBtn">
                                <i class="fa fa-file-excel-o"></i> 生成工单
                            </button>
                            <button type="button" class="btn btn-default btn-lg" onclick="resetForm()">
                                <i class="fa fa-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "comline/manage";
    var selectedLineIds = [];
    var selectedOperatorName = "";
    
    // 显示对应的操作表单
    function showOperationForm(operationType) {
        // 隐藏所有表单
        $("#newLineForm").hide();
        $("#selectLinesForm").hide();
        $("#relocateForm").hide();
        $("#expandForm").hide();
        $("#disableForm").hide();
        
        // 根据操作类型显示对应表单
        switch(operationType) {
            case '1': // 新增
                $("#newLineForm").show();
                break;
            case '2': // 撤销
                $("#selectLinesForm").show();
                break;
            case '3': // 移机
                $("#selectLinesForm").show();
                $("#relocateForm").show();
                break;
            case '4': // 扩容
                $("#selectLinesForm").show();
                $("#expandForm").show();
                break;
            case '5': // 停用
                $("#selectLinesForm").show();
                $("#disableForm").show();
                break;
        }
        
        // 启用生成按钮
        $("#generateBtn").prop('disabled', false);
    }
    
    // 搜索线路
    function searchLines() {
        var keyword = $("#searchKeyword").val();
        var searchData = {
            operator: $("#searchOperator").val(),
            institution: $("#searchInstitution").val()
        };
        
        // 使用关键词搜索多个字段（OR条件）
        if (keyword && keyword.trim() !== '') {
            searchData.keyword = keyword.trim();
        }
        
        $.post(prefix + "/list", searchData, function(result) {
            if (result.code == 0) {
                displayLines(result.rows);
            } else {
                $.modal.alertError("搜索失败：" + (result.msg || "未知错误"));
            }
        }).fail(function() {
            $.modal.alertError("搜索失败，请稍后重试");
        });
    }
    
    // 显示线路列表
    function displayLines(lines) {
        var tbody = $("#linesTableBody");
        tbody.empty();
        
        if (!lines || lines.length == 0) {
            tbody.append('<tr><td colspan="6" class="text-center">没有找到匹配的线路</td></tr>');
            return;
        }
        
        $.each(lines, function(index, line) {
            var operatorName = getDictLabel("line_operator", line.operator);
            var row = '<tr>' +
                '<td><input type="checkbox" class="line-checkbox" value="' + line.lineCode + '" data-line=\'' + JSON.stringify(line) + '\' onchange="updateSelectedLinesDisplay()"></td>' +
                '<td>' + (line.lineNumber || '') + '</td>' +
                '<td>' + (line.lineName || '') + '</td>' +
                '<td>' + operatorName + '</td>' +
                '<td>' + (line.address || '') + '</td>' +
                '<td>' + (line.oppositeContact || '') + '</td>' +
                '</tr>';
            tbody.append(row);
        });
    }
    
    // 全选/取消全选
    function toggleAllLines(checkbox) {
        $(".line-checkbox").prop('checked', checkbox.checked);
        updateSelectedLinesDisplay();
    }
    
    // 更新已选择线路的显示
    function updateSelectedLinesDisplay() {
        var selected = [];
        var selectedIds = [];
        
        $(".line-checkbox:checked").each(function() {
            var lineData = JSON.parse($(this).attr('data-line'));
            selected.push(lineData);
            selectedIds.push(lineData.lineCode);
        });
        
        // 更新全局变量
        selectedLineIds = selectedIds;
        
        var html;
        if (selected.length == 0) {
            html = "<p>尚未选择线路</p>";
        } else {
            html = "<div class='table-responsive'><table class='table table-bordered table-striped'>";
            html += "<thead><tr><th>线路编号</th><th>线路名称</th><th>运营商</th><th>地址</th><th>联系人</th></tr></thead><tbody>";
            
            $.each(selected, function(index, line) {
                var operatorName = getDictLabel("line_operator", line.operator);
                html += "<tr>";
                html += "<td>" + (line.lineNumber || '') + "</td>";
                html += "<td>" + (line.lineName || '') + "</td>";
                html += "<td>" + operatorName + "</td>";
                html += "<td>" + (line.address || '') + "</td>";
                html += "<td>" + (line.oppositeContact || '') + "</td>";
                html += "</tr>";
            });
            
            html += "</tbody></table></div>";
            html += "<p><strong>共选择了 " + selected.length + " 条线路</strong></p>";
        }
        
        $("#selectedLinesInfo").html(html);
    }
    
    // 清空搜索条件
    function clearSearch() {
        $("#searchKeyword").val('');
        $("#searchOperator").val('');
        $("#searchInstitution").val('');
        $("#selectAllLines").prop('checked', false);
        loadAllLines(); // 重新加载所有线路
    }
    
    // 获取字典标签（简化版）
    function getDictLabel(dictType, dictValue) {
        if (dictType == "line_operator") {
            if (dictValue == "1") return "中国电信";
            if (dictValue == "2") return "中国移动";
            if (dictValue == "3") return "中国联通";
            if (dictValue == "4") return "其他";
        }
        return dictValue || '';
    }
    
    // 生成工单
    function generateWorkOrder() {
        var operationType = $("input[name='operationType']:checked").val();
        if (!operationType) {
            $.modal.alertWarning("请选择工单操作类型");
            return;
        }
        
        var workOrderData = {
            operationType: operationType,
            workOrderDate: new Date().toISOString().split('T')[0] // 格式化为 yyyy-MM-dd
        };
        
        // 根据操作类型收集数据
        switch(operationType) {
            case '1': // 新增
                workOrderData.operator = $("#newOperator").val();
                workOrderData.bandwidth = $("#newBandwidth").val();
                workOrderData.address = $("#newAddress").val();
                workOrderData.contact = $("#newContact").val();
                
                if (!workOrderData.operator) {
                    $.modal.alertWarning("请选择运营商");
                    return;
                }
                if (!workOrderData.bandwidth) {
                    $.modal.alertWarning("请填写带宽信息");
                    return;
                }
                if (!workOrderData.address) {
                    $.modal.alertWarning("请填写地址信息");
                    return;
                }
                if (!workOrderData.contact) {
                    $.modal.alertWarning("请填写联系人信息");
                    return;
                }
                break;
                
            case '2': // 撤销
                // 过滤并验证选中的线路ID
                var validLineIds = selectedLineIds.filter(function(id) {
                    return id !== null && id !== undefined && id !== '' && !isNaN(id) && id > 0;
                });
                if (validLineIds.length == 0) {
                    $.modal.alertWarning("请先选择要撤销的线路");
                    return;
                }
                workOrderData.selectedLineIds = validLineIds;
                break;
                
            case '3': // 移机
                // 过滤并验证选中的线路ID
                var validLineIds = selectedLineIds.filter(function(id) {
                    return id !== null && id !== undefined && id !== '' && !isNaN(id) && id > 0;
                });
                if (validLineIds.length == 0) {
                    $.modal.alertWarning("请先选择要移机的线路");
                    return;
                }
                workOrderData.selectedLineIds = validLineIds;
                workOrderData.newAddress = $("#newAddressRelocate").val();
                
                if (!workOrderData.newAddress) {
                    $.modal.alertWarning("请填写新地址信息");
                    return;
                }
                break;
                
            case '4': // 扩容
                // 过滤并验证选中的线路ID
                var validLineIds = selectedLineIds.filter(function(id) {
                    return id !== null && id !== undefined && id !== '' && !isNaN(id) && id > 0;
                });
                if (validLineIds.length == 0) {
                    $.modal.alertWarning("请先选择要扩容的线路");
                    return;
                }
                workOrderData.selectedLineIds = validLineIds;
                workOrderData.bandwidth = $("#newBandwidthExpand").val();
                
                if (!workOrderData.bandwidth) {
                    $.modal.alertWarning("请填写新带宽信息");
                    return;
                }
                break;
                
            case '5': // 停用
                // 过滤并验证选中的线路ID
                var validLineIds = selectedLineIds.filter(function(id) {
                    return id !== null && id !== undefined && id !== '' && !isNaN(id) && id > 0;
                });
                if (validLineIds.length == 0) {
                    $.modal.alertWarning("请先选择要停用的线路");
                    return;
                }
                workOrderData.selectedLineIds = validLineIds;
                workOrderData.remarks = $("#disableRemarks").val();
                
                if (!workOrderData.remarks || workOrderData.remarks.trim() === '') {
                    $.modal.alertWarning("请填写停用备注信息");
                    return;
                }
                break;
        }
        
        // 最终数据清理和验证
        if (workOrderData.selectedLineIds) {
            workOrderData.selectedLineIds = workOrderData.selectedLineIds.filter(function(id) {
                return id !== null && id !== undefined && id !== '' && !isNaN(id) && parseInt(id) > 0;
            }).map(function(id) {
                return parseInt(id); // 确保是数字类型
            });
        }
        
        // 发送请求生成工单
        $.modal.loading("正在生成工单，请稍候...");
        $.ajax({
            url: prefix + "/generate_work_order",
            type: "POST",
            contentType: "application/json;charset=utf-8",
            data: JSON.stringify(workOrderData),
            dataType: "json",
            success: function(result) {
                $.modal.closeLoading();
                if (result.code == web_status.SUCCESS) {
                    // 检查是否是多文件情况
                    if (result.fileList && result.fileList.length > 0) {
                        // 多文件下载处理
                        var message = result.message || ("工单生成成功！操作类型：" + result.operationType + 
                                     "，工单数量：" + result.workOrderCount + "，文件数量：" + result.fileCount);
                        $.modal.alertSuccess(message);
                        
                        // 依次下载多个文件
                        downloadMultipleFiles(result.fileList);
                    } else {
                        // 单文件下载处理（新增工单）
                        var operatorName = result.operatorName || "未知运营商";
                        $.modal.alertSuccess("工单生成成功！运营商：" + operatorName + 
                                           "，操作类型：" + result.operationType + 
                                           "，工单数量：" + result.workOrderCount);
                        
                        // 下载单个文件
                        if (result.msg) {
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                        }
                    }
                } else {
                    $.modal.alertError(result.msg || "工单生成失败");
                }
            },
            error: function() {
                $.modal.closeLoading();
                $.modal.alertError("工单生成失败，请稍后重试");
            }
        });
    }
    
    // 下载多个文件
    function downloadMultipleFiles(fileList) {
        if (!fileList || fileList.length === 0) {
            return;
        }
        
        // 依次下载每个文件，添加延迟避免浏览器阻止
        fileList.forEach(function(fileName, index) {
            setTimeout(function() {
                window.location.href = ctx + "common/download?fileName=" + encodeURI(fileName) + "&delete=" + true;
            }, index * 1000); // 每个文件间隔1秒下载
        });
    }

    
    // 重置表单
    function resetForm() {
        $("input[name='operationType']").prop('checked', false);
        $("#newLineFormData")[0].reset();
        $("#relocateFormData")[0].reset();
        $("#expandFormData")[0].reset();
        $("#disableFormData")[0].reset();
        
        $("#newLineForm").hide();
        $("#selectLinesForm").hide();
        $("#relocateForm").hide();
        $("#expandForm").hide();
        $("#disableForm").hide();
        
        $("#generateBtn").prop('disabled', true);
        selectedLineIds = [];
        $("#selectedLinesInfo").html("<p>尚未选择线路</p>");
    }
    
    // 页面加载时从sessionStorage获取选中的线路
    $(document).ready(function() {
        var storedLineIds = sessionStorage.getItem('selectedLineIds');
        var storedOperatorName = sessionStorage.getItem('selectedOperatorName');
        
        // 初始化selectedLineIds
        selectedLineIds = [];
        
        if (storedLineIds && storedLineIds !== 'null' && storedLineIds !== '[]') {
            try {
                var parsedIds = JSON.parse(storedLineIds);
                // 过滤掉空值、null、undefined和非数字的元素
                selectedLineIds = parsedIds.filter(function(id) {
                    return id !== null && id !== undefined && id !== '' && !isNaN(id) && id > 0;
                });
                
                if (selectedLineIds.length > 0) {
                    selectedOperatorName = storedOperatorName || "";
                    
                    // 获取选中线路的详细信息并显示
                    loadPreSelectedLines();
                }
            } catch (e) {
                console.error("解析存储的线路ID失败:", e);
                selectedLineIds = [];
            }
        }
        
        // 清理sessionStorage，避免影响下次使用
        sessionStorage.removeItem('selectedLineIds');
        sessionStorage.removeItem('selectedOperatorName');
        
        // 初始化时加载所有线路
        loadAllLines();
    });
    
    // 加载所有线路用于初始显示和搜索
    function loadAllLines() {
        $.post(prefix + "/list", {}, function(result) {
            if (result.code == 0) {
                displayLines(result.rows);
            } else if (result.rows && result.rows.length > 0) {
                // 兼容分页返回格式
                displayLines(result.rows);
            } else {
                console.error("加载线路列表失败：", result.msg);
                $("#linesTableBody").html('<tr><td colspan="6" class="text-center">加载线路失败，请刷新页面重试</td></tr>');
            }
        }).fail(function() {
            console.error("加载线路列表失败");
            $("#linesTableBody").html('<tr><td colspan="6" class="text-center">加载线路失败，请刷新页面重试</td></tr>');
        });
    }
    
    // 加载预选线路信息
    function loadPreSelectedLines() {
        $.post(prefix + "/get_selected_lines", { ids: selectedLineIds.join(',') }, function(result) {
            if (result.code == web_status.SUCCESS) {
                var html = "<div class='table-responsive'><table class='table table-bordered table-striped'>";
                html += "<thead><tr><th>线路编号</th><th>线路名称</th><th>运营商</th><th>地址</th><th>联系人</th></tr></thead><tbody>";
                
                $.each(result.selectedLines, function(index, line) {
                    html += "<tr>";
                    html += "<td>" + (line.lineNumber || '') + "</td>";
                    html += "<td>" + (line.lineName || '') + "</td>";
                    html += "<td>" + result.operatorName + "</td>";
                    html += "<td>" + (line.address || '') + "</td>";
                    html += "<td>" + (line.oppositeContact || '') + "</td>";
                    html += "</tr>";
                });
                
                html += "</tbody></table></div>";
                html += "<p><strong>共选择了 " + result.selectedLines.length + " 条线路</strong></p>";
                html += "<p class='text-muted'><small>注：以上是从主页面预选的线路，您也可以重新选择</small></p>";
                
                if (!result.operatorConsistent) {
                    html += "<div class='alert alert-warning'>警告：选中的线路包含不同的运营商，建议分别生成工单！</div>";
                }
                
                $("#selectedLinesInfo").html(html);
            } else {
                $("#selectedLinesInfo").html("<p>获取预选线路信息失败：" + (result.msg || '未知错误') + "</p>");
                selectedLineIds = []; // 清空无效的ID
            }
        }).fail(function() {
            $("#selectedLinesInfo").html("<p>获取预选线路信息失败，请稍后重试</p>");
            selectedLineIds = []; // 清空无效的ID
        });
    }
</script>
</body>
</html> 